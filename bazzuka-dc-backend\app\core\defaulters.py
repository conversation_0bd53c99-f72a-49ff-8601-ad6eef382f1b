from datetime import datetime, timedelta
from app.core.lru_cache import LR<PERSON><PERSON>
from app.utils import hash_sensitive_data
from app.utils.supabase.queries import (
    get_defaulter_by_email,
    get_defaulter_by_phone,
    get_defaulter_by_id,
    get_issues_by_defaulter_id,
    get_defaulters_by_ids,
    insert_defaulters,
    insert_issues_bulk,
    get_org,
    get_negotiation_strategy,
    update_customizations,
    get_comm_logs,
)
from app.core.ai import make_ai_client, make_negotiation_gen_client


def format_name(name: str) -> str:
    parts = name.split(" ")
    formatted_parts = [
        "-".join(word.capitalize() for word in part.split("-")) for part in parts
    ]
    return " ".join(formatted_parts)


# Remove global AI client instances - they will be created per-thread in background tasks


class Defaulters:
    def __init__(self):
        pass

    def get_strategy(self, defaulter_id):
        # comm_logs = get_comm_logs(defaulter_id=int(defaulter_id), limit=10)
        strategy = get_negotiation_strategy(defaulter_id)
        if strategy.data:
            if strategy.data[0]["negotiation_strategy"] is not None:
                return strategy.data[0]["negotiation_strategy"]

        org_response = get_org()

        policy = ""
        if org_response.data:
            metadata = org_response.data[0]["metadata"]
            if metadata.get("settlement_plans"):
                policy += (
                    "\nThe settlement options are: " + metadata["settlement_plans"]
                )

        defaulter = str(self.get_defaulter_by_id(defaulter_id))
        # defaulter.pop("customizations", None) # str(get_defaulter_by_id(defaulter_id).data[0])
        # case_info = str(get_issues_by_defaulter_id(defaulter_id).data)

        # TODO: refactor as necessary...
        output = (
            negotiation_gen_client.do("generate_negotiation_prompt")
            .with_context(
                {
                    "policy": policy,
                    "case_info": defaulter,
                }
            )
            .execute()
        )

        insert_negotiation_strategy(defaulter_id, output)

        return output

    def load_defaulters(self, data, org_id="8241d390-a8b5-4f59-b0a9-b95c074db3f5"):
        """
        Optimized load_defaulters method for scalability with thousands of accounts.
        Uses bulk database operations and background processing for heavy operations.
        Handles multiple issues per defaulter correctly.
        """
        # Starting load_defaulters

        # Phase 1: Data preparation and validation (fast)
        defaulters_to_insert = {}
        issues_to_insert = []
        defaulter_conflicts = {}  # Track conflicts in defaulter data

        for issue in data:
            # Phone number validation
            if issue["phone"][0] != "+":
                issue["phone"] = "+" + issue["phone"]

            defaulter_id = issue["studnum"]

            # Build defaulter data for this issue
            current_defaulter_data = {
                "name": format_name(issue["name"]),
                "email": issue["email"],
                "phone": issue["phone"],
                "customizations": {
                    "ssn": hash_sensitive_data(issue["ssn"][-4:]),
                    "dob": hash_sensitive_data(issue["dob"]),
                    "email_last_verified": "",
                },
            }

            # Handle multiple issues per defaulter with conflict detection
            if defaulter_id not in defaulters_to_insert:
                defaulters_to_insert[defaulter_id] = current_defaulter_data
            else:
                # Check for conflicts in defaulter data
                existing_defaulter = defaulters_to_insert[defaulter_id]
                conflicts = []

                if existing_defaulter["name"] != current_defaulter_data["name"]:
                    conflicts.append(
                        f"name: '{existing_defaulter['name']}' vs '{current_defaulter_data['name']}'"
                    )
                if existing_defaulter["email"] != current_defaulter_data["email"]:
                    conflicts.append(
                        f"email: '{existing_defaulter['email']}' vs '{current_defaulter_data['email']}'"
                    )
                if existing_defaulter["phone"] != current_defaulter_data["phone"]:
                    conflicts.append(
                        f"phone: '{existing_defaulter['phone']}' vs '{current_defaulter_data['phone']}'"
                    )

                if conflicts:
                    if defaulter_id not in defaulter_conflicts:
                        defaulter_conflicts[defaulter_id] = []
                    defaulter_conflicts[defaulter_id].extend(conflicts)
                    # Use the first occurrence of data (existing behavior)

                        # Prepare issue data (multiple issues per defaulter are handled correctly)
            delinquency_val = issue["delinquency"]
            delinquency_date = None
            try:
                # Try to interpret as integer (days delinquent)
                days = int(delinquency_val)
                delinquency_date = (datetime.now() - timedelta(days=days)).strftime("%Y-%m-%d")
            except (ValueError, TypeError):
                # Try to parse as date string
                try:
                    delinquency_date = datetime.strptime(delinquency_val, "%m/%d/%y").strftime("%Y-%m-%d")
                except Exception:
                    delinquency_date = None


            # Prepare issue data (multiple issues per defaulter are handled correctly)
            issues_to_insert.append(
                {
                    "defaulter_id": defaulter_id,
                    "outstanding_amount": issue["outstanding_amount"],
                    "delinquency_date": delinquency_date,
                    "status": "unsolved",
                    "metadata": {
                        # "delinquency": issue["delinquency"],
                        # "last_paid_date": issue["last_paid_date"],
                        # "last_paid_amount": issue["last_paid_amt"],
                        "loan_description": issue.get("loan_description", "Unknown"),
                        "original_monthly_loan_payment_amount": issue.get("loan_payment_amt", "Unknown"),
                    },
                }
            )

        # Phase 2: Bulk database operations (fast)

        # Convert defaulters to list format for insertion
        defaulters_list = []
        for defaulter_id, defaulter in defaulters_to_insert.items():
            defaulter["id"] = defaulter_id
            defaulters_list.append(defaulter)

        # Bulk insert defaulters
        insert_defaulters(defaulters_list)

        # Bulk insert issues in batches to avoid overwhelming the database
        batch_size = 100
        for i in range(0, len(issues_to_insert), batch_size):
            batch = issues_to_insert[i : i + batch_size]
            insert_issues_bulk(batch)

        # Phase 3: Return immediately and process heavy operations in background
        defaulter_ids = list(defaulters_to_insert.keys())

        # Get organization settings for demo mode
        org = get_org()
        demo_mode = True  # This is the toggle for email sending vs analysis

        # Start background processing (non-blocking)
        from app.core.background_tasks import background_task_manager
        background_task_manager.process_defaulters_async(defaulter_ids, demo_mode)

        return {
            "defaulter_ids": defaulter_ids,
            "total_defaulters": len(defaulter_ids),
            "total_issues": len(issues_to_insert),
            "conflicts": defaulter_conflicts if defaulter_conflicts else None,
        }

    def get_defaulter_by_email(self, email_address):
        """
        Get the issue by email address.
        """
        defaulter_response = get_defaulter_by_email(email_address)
        if not defaulter_response.data or len(defaulter_response.data) == 0:
            return None
            
        defaulter = defaulter_response.data[0]
        if defaulter:
            cases = get_issues_by_defaulter_id(defaulter["id"]).data
            defaulter["cases"] = cases
            return defaulter
        else:
            return None

    def get_defaulter_by_phone(self, phone_number):
        """
        Get the issue by phone number.
        """
        defaulter_response = get_defaulter_by_phone(phone_number)
        if not defaulter_response.data or len(defaulter_response.data) == 0:
            return None
            
        defaulter = defaulter_response.data[0]
        if defaulter:
            cases = get_issues_by_defaulter_id(defaulter["id"]).data
            defaulter["cases"] = cases
            return defaulter
        else:
            return None

    def get_defaulter_by_id(self, defaulter_id):
        """
        Get the defaulter by issue ID.
        """
        # Convert string to int if needed
        if isinstance(defaulter_id, str):
            try:
                defaulter_id = int(defaulter_id)
            except ValueError:
                print(f"Invalid defaulter_id format: {defaulter_id}")
                return None
                
        defaulter_response = get_defaulter_by_id(defaulter_id)
        if not defaulter_response.data or len(defaulter_response.data) == 0:
            return None
            
        defaulter = defaulter_response.data[0]
        if defaulter:
            cases = get_issues_by_defaulter_id(defaulter["id"]).data
            defaulter["cases"] = cases
            return defaulter
        else:
            return None

    def get_defaulters_by_ids(self, defaulter_ids):
        data = get_defaulters_by_ids(defaulter_ids).data

        # Create a mapping of defaulter_id to defaulter
        defaulters_map = {row["defaulter_id"]: row.get("defaulters") for row in data}

        print(defaulters_map)

        # Return defaulters in the same order as defaulter_ids
        return [
            defaulters_map.get(defaulter_id, None) for defaulter_id in defaulter_ids
        ]


class CachedDefaulters(Defaulters):
    """
    Note that defaulter_id, email address, phone number are permenantly linked, so caching is simple.
    Defaulter data should never really change either.
    """

    def __init__(self, max_size=16):
        super().__init__()
        self.phone_cache = LRUCache(max_size)
        self.email_cache = LRUCache(max_size)
        self.defaulters_cache = LRUCache(max_size)
        self.strategies = LRUCache(max_size)

    def get_strategy(self, defaulter_id):
        cached_result = self.strategies.get(defaulter_id)

        if cached_result is not None:
            return cached_result
        else:
            res = super().get_strategy(defaulter_id)
            if res:
                self.strategies.set(defaulter_id, res)
            return res

    def get_defaulter_by_email(self, address):
        cached_result = self.email_cache.get(address)

        if cached_result is not None:
            return cached_result
        else:
            res = super().get_defaulter_by_email(address)

            if not res:
                raise KeyError("There is no issue for this email address.")

            self.email_cache.set(address, res)
            return res

    def get_defaulter_by_phone(self, phone_number):
        cached_result = self.phone_cache.get(phone_number)

        if cached_result is not None:
            return cached_result
        else:
            res = super().get_defaulter_by_phone(phone_number)
            if res:
                self.phone_cache.set(phone_number, res)
            return res

        raise KeyError("There is no issue for this phone number.")

    def get_defaulter_by_id(self, defaulter_id: str):
        cached_result = self.defaulters_cache.get(defaulter_id)

        if cached_result is not None:
            return cached_result
        else:
            res = super().get_defaulter_by_id(defaulter_id)
            if res:
                self.defaulters_cache.set(defaulter_id, res)
            return res

        raise KeyError("There is no issue for this phone number.")

    def get_defaulters_by_ids(self, defaulter_ids):
        return super().get_defaulters_by_ids(defaulter_ids)

        cached_results = {}
        missing_defaulter_ids = []

        # Check cache for each defaulter_id
        for defaulter_id in defaulter_ids:
            result = self.defaulters_cache.get(defaulter_id)
            if result is not None:
                cached_results[defaulter_id] = result
            else:
                missing_defaulter_ids.append(defaulter_id)

        # Fetch missing issues in bulk
        if missing_defaulter_ids:
            fetched_defaulters = super().get_defaulters_by_ids(missing_defaulter_ids)

            # Update cache
            for defaulter_id, defaulter in zip(
                missing_defaulter_ids, fetched_defaulters
            ):
                if defaulter:
                    self.defaulters_cache.set(defaulter_id, defaulter)
                    cached_results[defaulter_id] = defaulter

        # Return results in the same order as input
        return [
            cached_results.get(defaulter_id, None) for defaulter_id in defaulter_ids
        ]


defaulters = Defaulters()
