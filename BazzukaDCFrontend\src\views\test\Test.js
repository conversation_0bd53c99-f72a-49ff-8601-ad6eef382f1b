import React, { useEffect, useRef, useState } from "react";
import {
  CTable,
  CTableHead,
  CTableRow,
  CTableHeaderCell,
  CTableBody,
  CTableDataCell,
  CAvatar,
  CProgress,
} from "@coreui/react";
import { cilPeople } from "@coreui/icons";
import { CIcon } from '@coreui/icons-react';

const InfiniteScrollTable = () => {
  const [tableExample, setTableExample] = useState([]); // Table data
  const [isLoading, setIsLoading] = useState(false); // Loading state
  const [hasMore, setHasMore] = useState(true); // Check if more data is available
  const observerRef = useRef(null); // Ref for the intersection observer

  // Simulate fetching data from the DB
  const fetchData = async () => {
    if (isLoading) return; // Prevent multiple fetches
    setIsLoading(true);

    // Simulate API call delay
    await new Promise((resolve) => setTimeout(resolve, 1000));

    // Simulated fetched data
    const newItems = Array.from({ length: 10 }, (_, i) => ({
      avatar: {
        src: `https://i.pravatar.cc/150?img=${Math.floor(Math.random() * 70)}`,
        status: Math.random() > 0.5 ? "success" : "warning",
      },
      user: {
        name: `User ${tableExample.length + i + 1}`,
        new: Math.random() > 0.5,
        registered: `202${Math.floor(Math.random() * 5)}`,
      },
      country: {
        flag: cilPeople,
        name: `Country ${tableExample.length + i + 1}`,
      },
      usage: {
        value: Math.floor(Math.random() * 100),
        color: "success",
        period: "Last week",
      },
      payment: {
        icon: cilPeople,
      },
      activity: `${Math.floor(Math.random() * 24)} hours ago`,
    }));

    // Update table data
    setTableExample((prev) => [...prev, ...newItems]);

    // Stop loading and update `hasMore` if no more data
    setIsLoading(false);
    if (tableExample.length + newItems.length >= 100) setHasMore(false); // Example limit
  };

  // Setup Intersection Observer
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        const target = entries[0];
        if (target.isIntersecting && hasMore) {
          fetchData(); // Fetch more data when the target element is visible
        }
      },
      { threshold: 1 }
    );

    if (observerRef.current) observer.observe(observerRef.current);
    return () => observer.disconnect(); // Cleanup observer on unmount
  }, [hasMore]);

  return (
    <div style={{ height: "500px", overflowY: "auto" }}>
      <CTable align="middle" className="mb-0 mt-5 border" hover responsive>
        <CTableHead className="text-nowrap">
          <CTableRow>
            <CTableHeaderCell className="bg-body-tertiary text-center">
              <CIcon icon={cilPeople} />
            </CTableHeaderCell>
            <CTableHeaderCell className="bg-body-tertiary">User</CTableHeaderCell>
            <CTableHeaderCell className="bg-body-tertiary text-center">
              Country
            </CTableHeaderCell>
            <CTableHeaderCell className="bg-body-tertiary">Usage</CTableHeaderCell>
            <CTableHeaderCell className="bg-body-tertiary text-center">
              Payment Method
            </CTableHeaderCell>
            <CTableHeaderCell className="bg-body-tertiary">Activity</CTableHeaderCell>
          </CTableRow>
        </CTableHead>
        <CTableBody>
          {tableExample.map((item, index) => (
            <CTableRow key={index}>
              <CTableDataCell className="text-center">
                <CAvatar size="md" src={item.avatar.src} status={item.avatar.status} />
              </CTableDataCell>
              <CTableDataCell>
                <div>{item.user.name}</div>
                <div className="small text-body-secondary text-nowrap">
                  <span>{item.user.new ? "New" : "Recurring"}</span> | Registered:{" "}
                  {item.user.registered}
                </div>
              </CTableDataCell>
              <CTableDataCell className="text-center">
                <CIcon size="xl" icon={item.country.flag} title={item.country.name} />
              </CTableDataCell>
              <CTableDataCell>
                <div className="d-flex justify-content-between text-nowrap">
                  <div className="fw-semibold">{item.usage.value}%</div>
                  <div className="ms-3">
                    <small className="text-body-secondary">{item.usage.period}</small>
                  </div>
                </div>
                <CProgress thin color={item.usage.color} value={item.usage.value} />
              </CTableDataCell>
              <CTableDataCell className="text-center">
                <CIcon size="xl" icon={item.payment.icon} />
              </CTableDataCell>
              <CTableDataCell>
                <div className="small text-body-secondary text-nowrap">Last login</div>
                <div className="fw-semibold text-nowrap">{item.activity}</div>
              </CTableDataCell>
            </CTableRow>
          ))}
        </CTableBody>
      </CTable>
      <div
        ref={observerRef}
        style={{
          height: "50px",
          display: hasMore ? "block" : "none",
        }}
      >
        {isLoading && <p>Loading more data...</p>}
      </div>
    </div>
  );
};

export default InfiniteScrollTable;