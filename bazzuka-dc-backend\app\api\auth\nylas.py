import os

from flask import (
    Blueprint,
    request,
    request,
    redirect,
    jsonify,
    g,
)
from nylas import Client
from nylas.models.auth import URLForAuthenticationConfig
from nylas.models.auth import CodeExchangeRequest

from app.utils.supabase.client import supabase as s
from app.utils.supabase.admin import update_user_metadata

nylas_bp = Blueprint("nylas", __name__)
ROOT_DOMAIN = os.environ.get("ROOT_DOMAIN", "http://localhost:5000")

nylas = Client(
    api_key=os.environ.get("NYLAS_API_KEY"), api_uri=os.environ.get("NYLAS_API_URI")
)


@nylas_bp.route("/callback", methods=["GET"])
def authorized():
    try:
        code = request.args.get("code")
        exchangeRequest = CodeExchangeRequest(
            {
                "redirect_uri": ROOT_DOMAIN + "/v0/auth/nylas/callback",
                "code": code,
                "client_id": os.environ.get("NYLAS_CLIENT_ID"),
            }
        )
        exchange = nylas.auth.exchange_code_for_token(exchangeRequest)

        update_user_metadata(g.user, {"nylas_grant_id": exchange.grant_id})

        return redirect("http://localhost:3000/")

    except Exception as e:
        print(e)
        return (
            jsonify({"error": "Failed to exchange authorization code for token"}),
            500,
        )


# NOTE: This has changed and so the frontend code should be updated accordingly.
@nylas_bp.route("/", methods=["POST", "OPTIONS", "GET"])
def authorize():
    if g.user.user_metadata.get("nylas_grant_id"):
        return jsonify({"message": "success"}), 200

    config = URLForAuthenticationConfig(
        {
            "client_id": os.environ.get("NYLAS_CLIENT_ID"),
            "redirect_uri": ROOT_DOMAIN + "/v0/auth/nylas/callback",
        }
    )
    url = nylas.auth.url_for_oauth2(config)
    return redirect(url)
