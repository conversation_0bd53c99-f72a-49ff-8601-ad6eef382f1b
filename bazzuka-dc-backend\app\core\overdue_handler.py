from app.utils.supabase.queries import update_issue_status, update_payment_link_status, get_issue_by_id, get_payment_link_by_id
from app.core.actionitems import ActionItemRepository
from app.core.ai import AnalyzeAction
from datetime import datetime
from app.core.ai import make_ai_client

def handle_overdue_batch(events):
    results = []
    for event in events:
        if event.get('event_type') != 'overdue' or 'payment_link_id' not in event:
            results.append({'error': 'Invalid event', 'event': event})
            continue
        payment_link_id = event['payment_link_id']
        # Update payment link status to 'overdue'
        update_payment_link_status(payment_link_id, 'overdue')
        # Get payment link by UUID
        payment_link = get_payment_link_by_id(payment_link_id)
        if not payment_link or not payment_link.data:
            results.append({'error': 'Payment link not found', 'payment_link_id': payment_link_id})
            continue
        issue_id = payment_link.data[0]['issue_id']
        if not issue_id:
            results.append({'error': 'No issue_id on payment link', 'payment_link_id': payment_link_id})
            continue
        # Get issue by integer ID
        issue_info = get_issue_by_id(issue_id)
        if not issue_info or not issue_info.data:
            results.append({'error': 'Issue not found', 'issue_id': issue_id})
            continue
        issue = issue_info.data[0]
        defaulter_id = issue['defaulter_id']
        # Mark issue as 'promise-failed'
        update_issue_status(issue_id, 'promise-failed')
        # Prepare analyzer input
        overdue_date = event.get('due_date', datetime.now().strftime('%Y-%m-%d'))
        overdue_message = f"The defaulter had a payment arrangement due on {overdue_date} and it is now overdue."
        analyzer_args = {
            'defaulter_id': defaulter_id,
            'case_info': issue,
            'overdue_payment': overdue_message,
            'restrictions': 'none',
        }
        # Call analyzer to schedule follow-up
        # analyzer = AnalyzeAction()
        # analyzer_result = analyzer.prompt_generator.make_inputs(analyzer_args)
        # Create action item (category: overdue-follow-up)
        ai_client = make_ai_client()
        ai_client.do("analyze").with_context(analyzer_args).execute()
        

        results.append({'payment_link_id': payment_link_id, 'status': 'processed'})
    return {'results': results} 