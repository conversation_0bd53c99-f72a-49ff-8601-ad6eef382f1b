.icon-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Transcript content styling */
.transcript-content p {
  margin-bottom: 0.75rem !important;
  margin-top: 0 !important;
}

.transcript-content p:last-child {
  margin-bottom: 0 !important;
}

.transcript-content {
  white-space: normal !important;
  line-height: 1.6 !important;
}

/* Ensure line breaks are properly displayed */
.transcript-content br {
  line-height: 1.6;
}

/* Handle multiple consecutive line breaks */
.transcript-content br + br {
  margin-bottom: 0.5rem;
}