#!/usr/bin/env python3
"""
Verification test for schedule_one_off_communication tool migration.
This test verifies that the tool has been successfully moved from voice agent 
and email composer to the analyzer.
"""

import sys
import os

# Add the backend directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'bazzuka-dc-backend'))

def test_analyzer_has_schedule_tool():
    """Test that the analyzer tool engine includes the schedule_one_off_communication tool."""
    print("Testing analyzer has schedule_one_off_communication tool...")
    try:
        from app.core.ai.tools import analyzer_tool_engine
        
        available_tools = list(analyzer_tool_engine.tools.keys())
        print(f"✓ Analyzer tool engine loaded successfully")
        print(f"  Available tools: {available_tools}")
        
        if 'schedule_one_off_communication' in available_tools:
            print("✓ schedule_one_off_communication tool is available in analyzer")
            return True
        else:
            print("✗ schedule_one_off_communication tool is NOT available in analyzer")
            return False
            
    except Exception as e:
        print(f"✗ Error loading analyzer tool engine: {e}")
        return False

def test_email_composer_missing_schedule_tool():
    """Test that the email composer tool engine no longer includes the schedule_one_off_communication tool."""
    print("\nTesting email composer missing schedule_one_off_communication tool...")
    try:
        from app.core.ai.payments_tools import email_payment_tool_engine
        
        available_tools = list(email_payment_tool_engine.tools.keys())
        print(f"✓ Email composer tool engine loaded successfully")
        print(f"  Available tools: {available_tools}")
        
        if 'schedule_one_off_communication' not in available_tools:
            print("✓ schedule_one_off_communication tool is NOT in email composer (as expected)")
            return True
        else:
            print("✗ schedule_one_off_communication tool is still in email composer")
            return False
            
    except Exception as e:
        print(f"✗ Error loading email composer tool engine: {e}")
        return False

def test_payment_tool_engine_missing_schedule_tool():
    """Test that the payment tool engine no longer includes the schedule_one_off_communication tool."""
    print("\nTesting payment tool engine missing schedule_one_off_communication tool...")
    try:
        from app.core.ai.payments_tools import payment_tool_engine
        
        available_tools = list(payment_tool_engine.tools.keys())
        print(f"✓ Payment tool engine loaded successfully")
        print(f"  Available tools: {available_tools}")
        
        if 'schedule_one_off_communication' not in available_tools:
            print("✓ schedule_one_off_communication tool is NOT in payment tool engine (as expected)")
            return True
        else:
            print("✗ schedule_one_off_communication tool is still in payment tool engine")
            return False
            
    except Exception as e:
        print(f"✗ Error loading payment tool engine: {e}")
        return False

def test_payment_agent_tool_engine_missing_schedule_tool():
    """Test that the payment agent tool engine no longer includes the schedule_one_off_communication tool."""
    print("\nTesting payment agent tool engine missing schedule_one_off_communication tool...")
    try:
        from app.core.ai.payment_agent_tools import payment_agent_tool_engine
        
        available_tools = list(payment_agent_tool_engine.tools.keys())
        print(f"✓ Payment agent tool engine loaded successfully")
        print(f"  Available tools: {available_tools}")
        
        if 'schedule_one_off_communication' not in available_tools:
            print("✓ schedule_one_off_communication tool is NOT in payment agent tool engine (as expected)")
            return True
        else:
            print("✗ schedule_one_off_communication tool is still in payment agent tool engine")
            return False
            
    except Exception as e:
        print(f"✗ Error loading payment agent tool engine: {e}")
        return False

def test_analyzer_prompt_includes_scheduling():
    """Test that the analyzer prompt includes instructions for scheduling customer-requested follow-ups."""
    print("\nTesting analyzer prompt includes scheduling instructions...")
    try:
        with open('bazzuka-dc-backend/data/prompts/analyzer.md', 'r', encoding='utf-8') as f:
            prompt_content = f.read()
        
        print("✓ Analyzer prompt loaded successfully")
        
        # Check for key phrases that should be in the updated prompt
        checks = [
            ('schedule_one_off_communication', 'schedule_one_off_communication tool mentioned'),
            ('Customer-Requested Follow-ups', 'Customer-Requested Follow-ups section exists'),
            ('is_human_followup', 'is_human_followup parameter explained'),
            ('handle customer-requested follow-ups', 'role description updated')
        ]
        
        all_passed = True
        for phrase, description in checks:
            if phrase in prompt_content:
                print(f"  ✓ {description}")
            else:
                print(f"  ✗ {description}")
                all_passed = False
        
        return all_passed
            
    except Exception as e:
        print(f"✗ Error loading analyzer prompt: {e}")
        return False

def test_email_composer_prompt_updated():
    """Test that the email composer prompt mentions analyzer will handle scheduling."""
    print("\nTesting email composer prompt updated...")
    try:
        with open('bazzuka-dc-backend/data/prompts/email_composer.md', 'r', encoding='utf-8') as f:
            prompt_content = f.read()
        
        print("✓ Email composer prompt loaded successfully")
        
        # Check for key phrases that should be in the updated prompt
        checks = [
            ('Customer-Requested Follow-ups', 'Customer-Requested Follow-ups section exists'),
            ('analyzer will automatically handle scheduling', 'mentions analyzer handles scheduling'),
            ('You do not need to use any scheduling tools', 'clarifies no scheduling tools needed')
        ]
        
        all_passed = True
        for phrase, description in checks:
            if phrase in prompt_content:
                print(f"  ✓ {description}")
            else:
                print(f"  ✗ {description}")
                all_passed = False
        
        return all_passed
            
    except Exception as e:
        print(f"✗ Error loading email composer prompt: {e}")
        return False

def test_tool_definition_consistency():
    """Test that the schedule_one_off_communication tool definition is consistent."""
    print("\nTesting tool definition consistency...")
    try:
        from app.core.ai.tools import schedule_one_off_communication_tool
        from app.core.payments import PaymentsTool
        
        print("✓ Tool definition loaded successfully")
        
        # Check tool properties
        checks = [
            (schedule_one_off_communication_tool.name == "schedule_one_off_communication", "Tool name is correct"),
            (schedule_one_off_communication_tool.function == PaymentsTool.schedule_one_off_communication, "Tool function is correct"),
            (len(schedule_one_off_communication_tool.params.params) == 6, "Tool has correct number of parameters"),
        ]
        
        all_passed = True
        for check, description in checks:
            if check:
                print(f"  ✓ {description}")
            else:
                print(f"  ✗ {description}")
                all_passed = False
        
        # Check parameter names
        param_names = [param.name for param in schedule_one_off_communication_tool.params.params]
        expected_params = ['defaulter_id', 'channel', 'date', 'time', 'reason', 'is_human_followup']
        
        for param in expected_params:
            if param in param_names:
                print(f"  ✓ Parameter '{param}' exists")
            else:
                print(f"  ✗ Parameter '{param}' missing")
                all_passed = False
        
        return all_passed
            
    except Exception as e:
        print(f"✗ Error checking tool definition: {e}")
        return False

def main():
    """Run all verification tests and report results."""
    print("=" * 80)
    print("SCHEDULE_ONE_OFF_COMMUNICATION TOOL MIGRATION VERIFICATION")
    print("=" * 80)
    
    tests = [
        ("Analyzer has schedule tool", test_analyzer_has_schedule_tool),
        ("Email composer missing schedule tool", test_email_composer_missing_schedule_tool),
        ("Payment tool engine missing schedule tool", test_payment_tool_engine_missing_schedule_tool),
        ("Payment agent tool engine missing schedule tool", test_payment_agent_tool_engine_missing_schedule_tool),
        ("Analyzer prompt includes scheduling", test_analyzer_prompt_includes_scheduling),
        ("Email composer prompt updated", test_email_composer_prompt_updated),
        ("Tool definition consistency", test_tool_definition_consistency),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'-' * 60}")
        result = test_func()
        results.append((test_name, result))
    
    print(f"\n{'=' * 80}")
    print("SUMMARY")
    print("=" * 80)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "PASS" if result else "FAIL"
        print(f"{status:4} | {test_name}")
        if result:
            passed += 1
    
    print(f"\nResults: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 ALL TESTS PASSED! Migration completed successfully.")
        return 0
    else:
        print(f"\n❌ {total - passed} tests failed. Migration needs attention.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
