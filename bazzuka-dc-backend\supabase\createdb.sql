CREATE TABLE Issues (
    defaulter_id SERIAL PRIMARY KEY,
    timestamp TIMESTAMPTZ NOT NULL,
    defaulter_id INT NOT NULL,
    user_id INT NOT NULL,
    status TEXT(20) NOT NULL CHECK (status IN ('solved', 'unsolved', 'partial-payment'))
);

CREATE TABLE TextLogs (
    text_id SERIAL PRIMARY KEY,
    timestamp TIMESTAMPTZ NOT NULL,
    defaulter_id INT NOT NULL REFERENCES Issues(defaulter_id) ON DELETE CASCADE,
    text_body TEXT NOT NULL,
    direction TEXT(10) NOT NULL CHECK (direction IN ('inbound', 'outbound'))
);

CREATE TABLE EmailLogs (
    email_id SERIAL PRIMARY KEY,
    message_id TEXT(255) NOT NULL,
    timestamp TIMESTAMPTZ NOT NULL,
    defaulter_id INT NOT NULL REFERENCES Issues(defaulter_id) ON DELETE CASCADE,
    email_subject TEXT(255),
    email_body TEXT,
    direction TEXT(10) NOT NULL CHECK (direction IN ('inbound', 'outbound'))
);

CREATE TABLE Summaries (
    id SERIAL PRIMARY KEY,
    defaulter_id INT NOT NULL UNIQUE REFERENCES Issues(defaulter_id) ON DELETE CASCADE,
    summary TEXT NOT NULL
);

CREATE TABLE CommLogs (
    id SERIAL PRIMARY KEY,
    timestamp TIMESTAMPTZ NOT NULL,
    defaulter_id INT NOT NULL REFERENCES Issues(defaulter_id) ON DELETE CASCADE,
    channel TEXT(10) NOT NULL CHECK (channel IN ('call', 'email', 'text')),
    comm_id TEXT NOT NULL, 
    direction TEXT(10) NOT NULL CHECK (direction IN ('inbound', 'outbound'))
);

CREATE TABLE ActionItems (
    id SERIAL PRIMARY KEY,
    timestamp TIMESTAMPTZ NOT NULL,
    action_date DATE NOT NULL,
    action_time TIME NOT NULL,
    action_channel_id INT NOT NULL,
    action_channel_content TEXT NOT NULL,
    defaulter_id INT NOT NULL REFERENCES Issues(defaulter_id) ON DELETE CASCADE,
    action_reason TEXT NOT NULL,
    is_human_followup BOOLEAN DEFAULT FALSE
);
