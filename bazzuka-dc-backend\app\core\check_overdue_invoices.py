import os, time, logging, stripe

# --- one-time init ----------------------------------------------------------
stripe_api_key = os.environ.get("STRIPE_API_KEY")
if not stripe_api_key:
    raise RuntimeError("STRIPE_API_KEY environment variable not set")
stripe.api_key = stripe_api_key     # inject in Lambda console/TF/CDK
logger = logging.getLogger()
logger.setLevel(logging.INFO)

# --- Lambda entrypoint ------------------------------------------------------
def handler(event, context):
    """
    EventBridge-triggered Lambda: find invoices that are past their due_date
    and still in 'open' status (i.e. not paid, void, or uncollectible).
    """
    try:
        now_unix = int(time.time())

        # Stripe supports cursor-based auto-paging.
        overdue = stripe.Invoice.list(
            status="open",                 # not paid/void
            due_date={"lte": now_unix},    # past due
            limit=100                      # page size (max 100)
        )

        count = 0
        for inv in overdue.auto_paging_iter():
            count += 1
            # You could push to SNS/SQS, update your DB, etc.; here we just log.
            logger.info(
                "OVERDUE: %s customer=%s amount_due=%s%s due_date=%s",
                inv.id,
                inv.customer,
                inv.amount_due / 100,
                inv.currency.upper(),
                time.strftime("%Y-%m-%d", time.gmtime(inv.due_date)),
            )

        logger.info("Checked %d overdue invoices at %s", count, time.strftime("%Y-%m-%d %H:%M:%S", time.gmtime(now_unix)))
        return {"overdue_count": count, "checked_at": now_unix}
    except Exception as e:
        logger.error(f"Error checking overdue invoices: {e}")
        return {"error": str(e)}

if __name__ == "__main__":
    # Simulate a Lambda event and context (empty for this function)
    result = handler({}, None)
    print(result) 