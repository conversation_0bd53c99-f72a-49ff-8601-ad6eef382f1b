#!/bin/bash

# Deployment script for Bazzuka API to AWS ECS
# Usage: ./deploy.sh [environment] [region]
# Example: ./deploy.sh production us-east-1

set -e

# Configuration
ENVIRONMENT=${1:-production}
AWS_REGION=${2:-us-east-1}
ECR_REPOSITORY="bazzuka-api"
CLUSTER_NAME="bazzuka-cluster"
SERVICE_NAME="bazzuka-api-service"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

echo_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

echo_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if AWS CLI is installed and configured
if ! command -v aws &> /dev/null; then
    echo_error "AWS CLI is not installed. Please install it first."
    exit 1
fi

# Check if Docker is running
if ! docker info &> /dev/null; then
    echo_error "Docker is not running. Please start Docker first."
    exit 1
fi

# Get AWS account ID
ACCOUNT_ID=$(aws sts get-caller-identity --query Account --output text)
if [ -z "$ACCOUNT_ID" ]; then
    echo_error "Failed to get AWS account ID. Please check your AWS credentials."
    exit 1
fi

ECR_URI="${ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com/${ECR_REPOSITORY}"

echo_info "Starting deployment for environment: $ENVIRONMENT"
echo_info "AWS Region: $AWS_REGION"
echo_info "ECR Repository: $ECR_URI"

# Authenticate Docker to ECR
echo_info "Authenticating Docker to ECR..."
aws ecr get-login-password --region $AWS_REGION | docker login --username AWS --password-stdin $ECR_URI

# Create ECR repository if it doesn't exist
echo_info "Checking if ECR repository exists..."
if ! aws ecr describe-repositories --repository-names $ECR_REPOSITORY --region $AWS_REGION &> /dev/null; then
    echo_info "Creating ECR repository..."
    aws ecr create-repository --repository-name $ECR_REPOSITORY --region $AWS_REGION
fi

# Build Docker image
echo_info "Building Docker image..."
docker build -t $ECR_REPOSITORY:latest .

# Tag image for ECR
echo_info "Tagging image for ECR..."
docker tag $ECR_REPOSITORY:latest $ECR_URI:latest
docker tag $ECR_REPOSITORY:latest $ECR_URI:$ENVIRONMENT-$(date +%Y%m%d-%H%M%S)

# Push image to ECR
echo_info "Pushing image to ECR..."
docker push $ECR_URI:latest
docker push $ECR_URI:$ENVIRONMENT-$(date +%Y%m%d-%H%M%S)

# Update ECS service (if exists)
echo_info "Checking if ECS service exists..."
if aws ecs describe-services --cluster $CLUSTER_NAME --services $SERVICE_NAME --region $AWS_REGION &> /dev/null; then
    echo_info "Updating ECS service..."
    aws ecs update-service --cluster $CLUSTER_NAME --service $SERVICE_NAME --force-new-deployment --region $AWS_REGION
    
    echo_info "Waiting for service to stabilize..."
    aws ecs wait services-stable --cluster $CLUSTER_NAME --services $SERVICE_NAME --region $AWS_REGION
    
    echo_info "Deployment completed successfully!"
else
    echo_warn "ECS service not found. You'll need to create the service manually using the task definition."
    echo_info "Use the ecs-task-definition.json file to create your ECS service."
fi

echo_info "Image pushed successfully to: $ECR_URI:latest"
echo_info "Don't forget to update your task definition with the new image URI if needed." 