import React, { useState } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { useFormattedTimestamp } from "../utils/dateUtils";
import { CButton, CModal, CModalHeader, CModalBody, CModalFooter } from "@coreui/react";
import { cilTrash } from "@coreui/icons";
import { CIcon } from "@coreui/icons-react";
import { getApiUrl } from '../utils/apiConfig'

const UserInfoCard = ({ user: initialUser }) => {
  const location = useLocation();
  const navigate = useNavigate();
  const tableItem = location.state?.tableItem;
  const formatTimestamp = useFormattedTimestamp();
  const [showDeleteModal, setShowDeleteModal] = useState(false);

//   console.log('ti', tableItem?.action_time?.split("T")[0]);


  // Initialize state with tableItem data or fallback to initial user
  const [user, setUser] = useState({
    action_id: tableItem?.id, // Required ID
    action_channel: tableItem?.action_channel,
    action_date: tableItem?.action_date?.split("T")[0], // Extract date
    action_time: tableItem?.action_time?.split("T")[0], // Extract time
    action_channel_content: tableItem?.action_channel_content,
    action_reason: tableItem?.action_reason,
  });

  const [isEditing, setIsEditing] = useState(false);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setUser((prevUser) => ({ ...prevUser, [name]: value }));
  };

  const handleSave = async () => {
    setIsEditing(false);

    try {
      const payload = {
        id: user.action_id, // Required field
        ...(user.action_date && { action_date: user.action_date }), // Optional
        ...(user.action_time && { action_time: user.action_time }), // Optional
        ...(user.action_channel && { action_channel: user.action_channel }), // Optional
        ...(user.action_reason && { action_reason: user.action_reason }), // Optional
        ...(user.action_channel_content && { action_channel_content: user.action_channel_content }), // Optional
      };

      console.log('payload',payload);


      const response = await fetch(getApiUrl("/v0/drafts"), {
        method: "POST",
        headers: {
            "Content-Type": "application/json",
            "ngrok-skip-browser-warning": "true",
            "Access-Control-Allow-Origin": "*",
         },
        body: JSON.stringify(payload),
      });

      if (!response.ok) throw new Error("Failed to save data");

      const result = await response.json();
      console.log("Saved user data:", result);
    } catch (error) {
      console.error("Error saving data:", error);
    }
  };

  const handleDelete = async () => {
    try {
      // API call to delete the action item
      if (user.action_id) {
        const response = await fetch(getApiUrl(`/v0/drafts/${user.action_id}`), {
          method: "DELETE",
          headers: {
            "ngrok-skip-browser-warning": "true",
            "Access-Control-Allow-Origin": "*",
          },
        });
        
        if (!response.ok) throw new Error("Failed to delete item");
        
        // Close the modal
        setShowDeleteModal(false);
        
        // Navigate back to the dashboard
        navigate("/dashboard");
      }
    } catch (error) {
      console.error("Error deleting item:", error);
    }
  };

  return (
    <div className="flex items-center justify-center bg-gray-100 m-3">
      <div className="max-w-4xl w-full bg-white shadow-lg rounded-2xl p-6">
        <div className="mb-4">
          <h2 className="text-2xl font-bold text-gray-800">{tableItem?.contact?.name || initialUser.name}</h2>
          <p className="text-sm text-gray-500">Created: {tableItem?.timestamp ? formatTimestamp(tableItem.timestamp) : ''}</p>
        </div>

        <div className="mb-4">
          <h3 className="text-lg font-semibold text-gray-700">Channel</h3>
          {isEditing ? (
            <select
              name="action_channel"
              value={user.action_channel}
              onChange={handleChange}
              className="w-full border border-gray-300 rounded-md p-2"
            >
              <option value="call">Call</option>
              <option value="email">Email</option>
              <option value="text">Text</option>
            </select>
          ) : (
            <p className="text-gray-600">{user.action_channel}</p>
          )}
        </div>

        <div className="mb-4">
          <h3 className="text-lg font-semibold text-gray-700">Scheduled Date & Time</h3>
          {isEditing ? (
            <div className="flex space-x-4">
              <input
                type="date"
                name="action_date"
                value={user.action_date}
                onChange={handleChange}
                className="border border-gray-300 rounded-md p-2"
              />
              <input
                type="time"
                name="action_time"
                value={user.action_time}
                onChange={handleChange}
                className="border border-gray-300 rounded-md p-2"
              />
            </div>
          ) : (
            <p className="text-gray-600">
              {user.action_date && user.action_time
                ? formatTimestamp(`${user.action_date}T${user.action_time}`)
                : `${user.action_date || ''} ${user.action_time || ''}`}
            </p>
          )}
        </div>

        <div className="mb-4">
          <h3 className="text-lg font-semibold text-gray-700">Content</h3>
          {isEditing ? (
            <textarea
              name="action_channel_content"
              value={user.action_channel_content}
              onChange={handleChange}
              className="w-full border border-gray-300 rounded-md p-2"
            />
          ) : (
            <p className="text-gray-600">{user.action_channel_content}</p>
          )}
        </div>

        <div className="mb-4">
          <h3 className="text-lg font-semibold text-gray-700">Reason</h3>
          {isEditing ? (
            <textarea
              name="action_reason"
              value={user.action_reason}
              onChange={handleChange}
              className="w-full border border-gray-300 rounded-md p-2"
            />
          ) : (
                <p className="text-gray-600">{user.action_reason}</p>
          )}
        </div>

        <div className="flex space-x-4">
          {isEditing ? (
            <button
              onClick={handleSave}
              className="bg-blue-500 text-white px-4 py-2 rounded-md hover:bg-blue-600"
            >
              Save
            </button>
          ) : (
            <>
              <button
                onClick={() => setIsEditing(true)}
                className="bg-gray-500 text-white px-4 py-2 rounded-md hover:bg-gray-600"
              >
                Edit
              </button>
              <button
                onClick={() => setShowDeleteModal(true)}
                className="bg-red-500 text-white px-4 py-2 rounded-md hover:bg-red-600"
              >
                Delete
              </button>
            </>
          )}
        </div>
      </div>

      {/* Delete Confirmation Modal */}
      <CModal
        visible={showDeleteModal}
        onClose={() => setShowDeleteModal(false)}
        backdrop="static"
      >
        <CModalHeader closeButton>
          <h5>Confirm Delete</h5>
        </CModalHeader>
        <CModalBody>
          Are you sure you want to delete this action item? This action cannot be undone.
        </CModalBody>
        <CModalFooter>
          <CButton color="secondary" onClick={() => setShowDeleteModal(false)}>
            Cancel
          </CButton>
          <CButton color="danger" onClick={handleDelete}>
            Delete
          </CButton>
        </CModalFooter>
      </CModal>
    </div>
  );
};

const App = () => {
  const user = {
    name: "John Doe",
    createdAt: "2025-01-20T15:30:00Z",
    channel: "Email",
    scheduledAt: "2025-01-25T10:00:00Z",
    content: "Reminder email for overdue payment.",
    reason: "Payment overdue for invoice #12345.",
  };

  return <UserInfoCard user={user} />;
};

export default App;