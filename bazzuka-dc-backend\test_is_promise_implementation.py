#!/usr/bin/env python3
"""
Test script to verify the is_promise column implementation.
This script tests:
1. Database migration (is_promise column exists)
2. insert_payment functions accept is_promise parameter
3. get_payment_plans_by_issue_id filters by is_promise when requested
4. Stripe invoice creation sets is_promise=False
5. Promise-to-pay arrangements set is_promise=True
"""

import sys
import os

# Add the app directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

def test_database_schema():
    """Test that the is_promise column exists in the payment_links table"""
    print("Testing database schema...")
    try:
        from app.utils.supabase.queries import supabase
        
        # Try to select the is_promise column
        result = supabase.table("payment_links").select("id, is_promise").limit(1).execute()
        print("✓ is_promise column exists in payment_links table")
        return True
    except Exception as e:
        print(f"✗ Database schema test failed: {e}")
        return False

def test_insert_payment_functions():
    """Test that insert_payment functions accept is_promise parameter"""
    print("\nTesting insert_payment functions...")
    try:
        from app.utils.supabase.queries import insert_payment, insert_payment_duplicate
        
        # Test insert_payment signature
        import inspect
        sig = inspect.signature(insert_payment)
        if 'is_promise' in sig.parameters:
            print("✓ insert_payment accepts is_promise parameter")
        else:
            print("✗ insert_payment missing is_promise parameter")
            return False
            
        # Test insert_payment_duplicate signature
        sig = inspect.signature(insert_payment_duplicate)
        if 'is_promise' in sig.parameters:
            print("✓ insert_payment_duplicate accepts is_promise parameter")
        else:
            print("✗ insert_payment_duplicate missing is_promise parameter")
            return False
            
        return True
    except Exception as e:
        print(f"✗ Insert payment functions test failed: {e}")
        return False

def test_get_payment_plans_filtering():
    """Test that get_payment_plans_by_issue_id can filter by is_promise"""
    print("\nTesting get_payment_plans_by_issue_id filtering...")
    try:
        from app.utils.supabase.queries import get_payment_plans_by_issue_id
        
        # Test function signature
        import inspect
        sig = inspect.signature(get_payment_plans_by_issue_id)
        if 'promises_only' in sig.parameters:
            print("✓ get_payment_plans_by_issue_id accepts promises_only parameter")
            return True
        else:
            print("✗ get_payment_plans_by_issue_id missing promises_only parameter")
            return False
    except Exception as e:
        print(f"✗ Get payment plans filtering test failed: {e}")
        return False

def test_stripe_functions():
    """Test that Stripe functions exist and have correct signatures"""
    print("\nTesting Stripe functions...")
    try:
        from app.core.stripe import create_stripe_invoice_for_full_amount, create_payment_link
        
        # Test that the new function exists
        import inspect
        sig = inspect.signature(create_stripe_invoice_for_full_amount)
        if 'issue_id' in sig.parameters:
            print("✓ create_stripe_invoice_for_full_amount function exists")
        else:
            print("✗ create_stripe_invoice_for_full_amount missing issue_id parameter")
            return False
            
        print("✓ Stripe functions are properly defined")
        return True
    except Exception as e:
        print(f"✗ Stripe functions test failed: {e}")
        return False

def test_get_info_integration():
    """Test that get_info functions use promises_only filtering"""
    print("\nTesting get_info integration...")
    try:
        from app.core.get_info import sort_payment_plans_by_created_at
        
        # Test function signature
        import inspect
        sig = inspect.signature(sort_payment_plans_by_created_at)
        if 'promises_only' in sig.parameters:
            print("✓ sort_payment_plans_by_created_at accepts promises_only parameter")
            return True
        else:
            print("✗ sort_payment_plans_by_created_at missing promises_only parameter")
            return False
    except Exception as e:
        print(f"✗ Get info integration test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("Running is_promise implementation tests...\n")
    
    tests = [
        test_database_schema,
        test_insert_payment_functions,
        test_get_payment_plans_filtering,
        test_stripe_functions,
        test_get_info_integration
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print(f"\n{'='*50}")
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("✓ All tests passed! Implementation looks good.")
        return 0
    else:
        print("✗ Some tests failed. Please review the implementation.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
