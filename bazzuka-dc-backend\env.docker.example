# Flask Configuration
FLASK_ENV=production
FLASK_DEBUG=0
PYTHONPATH=/app

# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key_here

# Supabase Configuration
SUPABASE_URL=your_supabase_url_here
SUPABASE_ANON_KEY=your_supabase_anon_key_here
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key_here

# Stripe Configuration
STRIPE_API_KEY=your_stripe_api_key_here
STRIPE_WEBHOOK_SECRET=your_stripe_webhook_secret_here

# VAPI Configuration
VAPI_BASE_URL=https://api.vapi.ai
VAPI_API_KEY=your_vapi_api_key_here
VAPI_SECRET_KEY=your_vapi_secret_key_here

# SendGrid Configuration
SENDGRID_API_KEY=your_sendgrid_api_key_here

# Nylas Configuration
NYLAS_API_URI=https://api.us.nylas.com
NYLAS_CLIENT_ID=your_nylas_client_id_here
NYLAS_API_KEY=your_nylas_api_key_here
NYLAS_WEBHOOK_SECRET=your_nylas_webhook_secret_here
NYLAS_GRANT_ID=your_nylas_grant_id_here

# Application Configuration
ROOT_DOMAIN=http://localhost:8000
BACKGROUND_MAX_WORKERS=10

# Celery Configuration (if using Celery)
CELERY_BROKER_URL=redis://redis:6379/0
CELERY_RESULT_BACKEND=redis://redis:6379/0

# Security
SECRET_KEY=your_flask_secret_key_here

# Logging
LOG_LEVEL=INFO 