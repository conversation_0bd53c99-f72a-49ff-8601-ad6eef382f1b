// src/auth.js
import { supabase } from './supabaseClient'
import { useNavigate } from 'react-router-dom'

export const signUp = async (email, password) => {
  const { user, error } = await supabase.auth.signUp({
    email,
    password,
  })
  if (error) throw error
  return user
}

export const logIn = async (email, password) => {
  const { user, error } = await supabase.auth.signInWithPassword({
    email: email,
    password: password,
  })
  if (error) throw error
  return user
}

export const logOut = async () => {
  const { error } = await supabase.auth.signOut()
  if (error) throw error
}
