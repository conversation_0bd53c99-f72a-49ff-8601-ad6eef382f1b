"""
Direct test of the communication preferences prompt using OpenAI API.

This script tests the current prompt directly with OpenAI to identify false positives
without dealing with the application's dependency chain.
"""

import os
import json
import openai
from typing import List, Dict, Any

# Set up OpenAI client with API key
client = openai.OpenAI(api_key="********************************************************")

# The current prompt from the agent
COMM_PREFS_PROMPT = """Analyze the conversation transcript between a debt collector and a debtor to identify any explicit requests related to communication preferences.

**CRITICAL: Only call tools for EXPLICIT, DIRECT requests. Do NOT infer, assume, or interpret implicit preferences. When in doubt, DO NOT call any tools.**

- If—and only if—the debtor **explicitly** asks to stop receiving messages entirely through a specific channel (e.g., call, email, text), call the `opt_out_of_communications` tool with the appropriate channel.
    - You must always respect and act on any clear opt-out request.  
    - Example phrases include: "Please stop calling me," "Don't email me anymore," or "I no longer want to receive texts."
- If the debtor **explicitly** specifies limits on when they can be contacted (e.g., only during certain hours, days of the week, or not on holidays), call the `restrict_communications` tool with the relevant restrictions.  
    - Example phrases include: "Only call me after 5pm," "Do not contact me on weekends," or "Never call me at work."
- If you are given existing restrictions, you should append new restrictions before passing them into the `restrict_communications` tool.
- If the debtor requests not to be contacted at work, assume working hours are Monday to Friday, 9 AM to 5 PM, by default unless otherwise specified.
- **Never infer or assume a preference if the debtor does not directly state it.** For example, simply reaching voicemail, not answering a call, or automated greetings **do NOT** constitute a request or preference.
- Only act if the debtor makes a clear and affirmative request related to communication channels or timing.

**Key principle: If the debtor does not use explicit opt-out language or specific restriction requests, DO NOT call any tools.**

## Example

### Example Input
- **Defaulter ID**: 12345
- **Existing Restrictions**: Do not call on holidays.
- **Recent Conversation**: "Please stop calling me at work!"

### Example Tool Call Result
- **Tool Call**: restrict_communications
- **Tool Params**: {"defaulter_id": 12345, "description": "Do not call on holidays and do not call during working hours (Monday to Friday, 9 AM to 5 PM)."}

## Output Format
There should be no output, only tool calls if necessary."""

# Tool definitions
TOOLS = [
    {
        "type": "function",
        "function": {
            "name": "opt_out_of_communications",
            "description": "Opt a defaulter out entirely of specific communication channels. This should be used when a user requests to stop receiving communications through certain channels.",
            "parameters": {
                "type": "object",
                "properties": {
                    "defaulter_id": {
                        "type": "string",
                        "description": "The ID of the defaulter requesting the opt-out."
                    },
                    "channels": {
                        "type": "array",
                        "items": {"type": "string"},
                        "description": "List of channels to opt out of. Can include 'calls', 'emails', and/or 'texts'."
                    }
                },
                "required": ["defaulter_id", "channels"]
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "restrict_communications",
            "description": "Restrict a defaulter's communication by day of the week, time of the day, and/or holidays. This should be used when a user requests to only receive communications during certain times or on certain days.",
            "parameters": {
                "type": "object",
                "properties": {
                    "defaulter_id": {
                        "type": "string",
                        "description": "The ID of the defaulter to restrict communication for."
                    },
                    "description": {
                        "type": "string",
                        "description": "A description of the updated communication restrictions."
                    }
                },
                "required": ["defaulter_id", "description"]
            }
        }
    }
]


class PromptTester:
    """Test the communication preferences prompt directly."""
    
    def __init__(self, model="gpt-4.1-mini", temperature=0.0):
        self.model = model
        self.temperature = temperature
        self.test_cases = []
        self.results = []
    
    def add_test_case(self, name: str, conversation: str, description: str = ""):
        """Add a test case that should NOT trigger tool calls."""
        self.test_cases.append({
            'name': name,
            'conversation': conversation,
            'description': description,
            'defaulter_id': 'test_123'
        })
    
    def test_single_case(self, test_case: Dict[str, Any]) -> Dict[str, Any]:
        """Test a single case."""
        result = {
            'name': test_case['name'],
            'conversation': test_case['conversation'],
            'description': test_case['description'],
            'passed': False,
            'tools_called': [],
            'error': None,
            'response_content': None
        }
        
        try:
            # Create the user message
            user_message = f"""Here was the most recent communication transcript between the collector and the debtor: 

- **Defaulter ID**: {test_case['defaulter_id']}
- **Recent Conversation**: {test_case['conversation']}"""
            
            # Make the API call
            response = client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": COMM_PREFS_PROMPT},
                    {"role": "user", "content": user_message}
                ],
                tools=TOOLS,
                temperature=self.temperature
            )
            
            # Check the response
            message = response.choices[0].message
            result['response_content'] = message.content
            
            # Check if any tools were called
            if message.tool_calls:
                for tool_call in message.tool_calls:
                    result['tools_called'].append({
                        'tool': tool_call.function.name,
                        'arguments': tool_call.function.arguments
                    })
            
            # Test passes if NO tools were called
            result['passed'] = len(result['tools_called']) == 0
            
        except Exception as e:
            result['error'] = str(e)
            result['passed'] = False
            
        return result
    
    def run_all_tests(self) -> List[Dict[str, Any]]:
        """Run all test cases."""
        self.results = []
        
        print(f"Testing prompt with model: {self.model} (temperature: {self.temperature})")
        print(f"Running {len(self.test_cases)} false positive test cases...\n")
        
        for i, test_case in enumerate(self.test_cases, 1):
            print(f"Test {i}/{len(self.test_cases)}: {test_case['name']}")
            result = self.test_single_case(test_case)
            self.results.append(result)
            
            if result['passed']:
                print("✅ PASSED - No tools called")
            else:
                print("❌ FAILED - Tools called inappropriately:")
                for tool_call in result['tools_called']:
                    print(f"   - {tool_call['tool']}: {tool_call['arguments']}")
                if result['error']:
                    print(f"   - Error: {result['error']}")
            print()
        
        return self.results
    
    def print_summary(self):
        """Print test summary."""
        if not self.results:
            print("No test results available.")
            return
            
        total = len(self.results)
        passed = sum(1 for r in self.results if r['passed'])
        failed = total - passed
        
        print(f"{'='*60}")
        print(f"TEST SUMMARY")
        print(f"{'='*60}")
        print(f"Model: {self.model} (temperature: {self.temperature})")
        print(f"Total tests: {total}")
        print(f"Passed: {passed}")
        print(f"Failed (False Positives): {failed}")
        print(f"Success rate: {(passed/total)*100:.1f}%")
        
        if failed > 0:
            print(f"\nFALSE POSITIVE CASES:")
            for result in self.results:
                if not result['passed']:
                    print(f"\n- {result['name']}")
                    print(f"  Conversation: {result['conversation'][:100]}...")
                    for tool_call in result['tools_called']:
                        print(f"  Tool called: {tool_call['tool']}")


def create_false_positive_test_cases():
    """Create test cases that should NOT trigger tool calls."""
    return [
        {
            'name': 'Normal greeting',
            'conversation': "Collector: Hi Debby, this is John from ABC Collections regarding your account. How are you today?\nDebby: Hi John, I'm doing okay. What can I help you with?",
            'description': 'Simple greeting exchange'
        },
        {
            'name': 'Voicemail reached',
            'conversation': "Collector: Hi Debby, this is John from ABC Collections. I'm calling about your account. Please give me a call back at 555-0123.\n[Voicemail system]: You have reached Debby's voicemail...",
            'description': 'Reaching voicemail should not trigger opt-out'
        },
        {
            'name': 'Debby being rude but not opting out',
            'conversation': "Collector: Hi Debby, I'm calling about your overdue account.\nDebby: I don't want to talk to you right now. You people are so annoying!\nCollector: I understand you're frustrated, but we need to discuss this.\nDebby: Whatever, just make it quick.",
            'description': 'Rude behavior without explicit opt-out'
        },
        {
            'name': 'Debby acting annoyed about call',
            'conversation': "Collector: Hello Debby, this is Sarah from Collections.\nDebby: Ugh, not again. This is so frustrating. I'm having a terrible day and now this.\nCollector: I'm sorry to hear that. Can we talk about your account?\nDebby: Fine, but I'm not happy about it.",
            'description': 'Expressing annoyance without opt-out request'
        },
        {
            'name': 'Debby mentions being busy but continues',
            'conversation': "Collector: Hi Debby, calling about your account.\nDebby: Oh, I'm really busy right now with work stuff, but I guess we can talk quickly.\nCollector: I appreciate that. Let's discuss your payment options.\nDebby: Okay, what are my options?",
            'description': 'Mentioning being busy but agreeing to talk'
        },
        {
            'name': 'General complaint about debt collection',
            'conversation': "Collector: Hi Debby, this is about your outstanding balance.\nDebby: I hate dealing with debt collectors. This whole situation is stressful.\nCollector: I understand this is difficult.\nDebby: Yeah, it really is. But I know I need to deal with it.",
            'description': 'General complaints without opt-out requests'
        },
        {
            'name': 'Debby says she doesnt want to talk but continues',
            'conversation': "Collector: Hi Debby, this is about your account.\nDebby: I really don't want to talk about this right now.\nCollector: I understand, but we need to resolve this.\nDebby: Okay fine, what do I owe?",
            'description': 'Saying dont want to talk but continuing conversation'
        },
        {
            'name': 'Debby mentions work hours but no restriction request',
            'conversation': "Collector: Hi Debby, calling about your balance.\nDebby: I'm at work right now. Can we make this quick?\nCollector: Of course. Let's discuss payment options.\nDebby: What are my choices?",
            'description': 'Mentioning work without requesting restrictions'
        },
        {
            'name': 'Debby says stop but then continues talking',
            'conversation': "Collector: Hi Debby, about your account.\nDebby: Stop, stop. I'm stressed about this.\nCollector: I understand this is stressful.\nDebby: Yeah, but tell me what I need to do.",
            'description': 'Saying stop in context of stress, not communication'
        },
        {
            'name': 'Debby mentions not wanting calls but in different context',
            'conversation': "Collector: Hi Debby, calling about your debt.\nDebby: I don't want these kinds of problems in my life.\nCollector: I understand. Let's work together to resolve this.\nDebby: Okay, what can we do?",
            'description': 'Mentioning not wanting problems, not calls specifically'
        },
        {
            'name': 'Debby asks to call back later but continues',
            'conversation': "Collector: Hi Debby, about your account.\nDebby: Can you call me back later? Actually, no wait, let's just get this over with now.\nCollector: Sure, let's discuss it.\nDebby: What do I owe?",
            'description': 'Asking to call back but then continuing conversation'
        },
        {
            'name': 'Debby mentions being at work multiple times',
            'conversation': "Collector: Hi Debby.\nDebby: I'm at work. This is my workplace. I'm working right now.\nCollector: I understand you're busy.\nDebby: Yeah, but let's talk quickly.",
            'description': 'Multiple mentions of work without restriction request'
        }
    ]


if __name__ == "__main__":
    
    # Create and run tests
    tester = PromptTester(model="gpt-4.1-mini", temperature=0.0)
    
    # Add test cases
    test_cases = create_false_positive_test_cases()
    for case in test_cases:
        tester.add_test_case(case['name'], case['conversation'], case['description'])
    
    # Run tests
    results = tester.run_all_tests()
    
    # Print summary
    tester.print_summary()
    
    # Save results
    filename = f'prompt_test_results_{tester.model.replace(".", "_")}_temp_{tester.temperature}.json'
    with open(filename, 'w') as f:
        json.dump(results, f, indent=2)
    
    print(f"\nResults saved to '{filename}'")
