"""
Test suite for detecting false positives in the communication preferences agent.

This script tests various conversation scenarios to identify cases where the agent
inappropriately calls tools when no opt-out or restriction request was made.
"""

import sys
import os
import pytest
import json
from unittest.mock import Mock, patch, MagicMock
from typing import List, Dict, Any

# Mock environment variables and dependencies before importing
os.environ.setdefault('SUPABASE_URL', 'https://test.supabase.co')
os.environ.setdefault('SUPABASE_KEY', 'test_key')
os.environ.setdefault('OPENAI_API_KEY', 'test_key')

# Mock the supabase client creation
with patch('supabase.create_client') as mock_create_client:
    mock_create_client.return_value = MagicMock()

    # Add the backend directory to the path so we can import the modules
    sys.path.append(os.path.join(os.path.dirname(__file__), 'bazzuka-dc-backend'))

    from app.core.ai.comm_prefs_agent import analyze_communication_preferences


class FalsePositiveTestCase:
    """Represents a test case that should NOT trigger any tool calls."""
    
    def __init__(self, name: str, conversation: str, description: str = ""):
        self.name = name
        self.conversation = conversation
        self.description = description
        self.defaulter_id = "test_defaulter_123"


class CommPrefsTestFramework:
    """Framework for testing communication preferences agent for false positives."""
    
    def __init__(self):
        self.test_cases: List[FalsePositiveTestCase] = []
        self.results: List[Dict[str, Any]] = []
        
    def add_test_case(self, test_case: FalsePositiveTestCase):
        """Add a test case to the framework."""
        self.test_cases.append(test_case)
    
    def run_single_test(self, test_case: FalsePositiveTestCase) -> Dict[str, Any]:
        """
        Run a single test case and return results.
        
        Returns:
            dict: Test result with 'passed', 'tools_called', 'error' fields
        """
        result = {
            'name': test_case.name,
            'conversation': test_case.conversation,
            'description': test_case.description,
            'passed': False,
            'tools_called': [],
            'error': None
        }
        
        try:
            # Mock the database calls to avoid actual DB operations
            with patch('app.utils.supabase.queries.get_restrictions_by_defaulter_id') as mock_get_restrictions, \
                 patch('app.core.payments.PaymentsTool.opt_out_of_communications') as mock_opt_out, \
                 patch('app.core.payments.PaymentsTool.restrict_communication') as mock_restrict:
                
                # Setup mock returns
                mock_get_restrictions.return_value = Mock(data=[])
                mock_opt_out.return_value = "Success: Opted out of communications."
                mock_restrict.return_value = "Success: Communication restricted."
                
                # Run the analysis
                analysis_result = analyze_communication_preferences(
                    conversation=test_case.conversation,
                    defaulter_id=test_case.defaulter_id
                )
                
                # Check if any tools were called
                tools_called = []
                if mock_opt_out.called:
                    tools_called.append({
                        'tool': 'opt_out_of_communications',
                        'args': mock_opt_out.call_args
                    })
                if mock_restrict.called:
                    tools_called.append({
                        'tool': 'restrict_communications', 
                        'args': mock_restrict.call_args
                    })
                
                result['tools_called'] = tools_called
                result['passed'] = len(tools_called) == 0  # Test passes if NO tools were called
                
        except Exception as e:
            result['error'] = str(e)
            result['passed'] = False
            
        return result
    
    def run_all_tests(self) -> List[Dict[str, Any]]:
        """Run all test cases and return results."""
        self.results = []
        
        print(f"Running {len(self.test_cases)} false positive test cases...")
        
        for i, test_case in enumerate(self.test_cases, 1):
            print(f"\nTest {i}/{len(self.test_cases)}: {test_case.name}")
            result = self.run_single_test(test_case)
            self.results.append(result)
            
            if result['passed']:
                print("✅ PASSED - No tools called (correct behavior)")
            else:
                print("❌ FAILED - Tools were called inappropriately:")
                for tool_call in result['tools_called']:
                    print(f"   - {tool_call['tool']}: {tool_call['args']}")
                if result['error']:
                    print(f"   - Error: {result['error']}")
        
        return self.results
    
    def print_summary(self):
        """Print a summary of test results."""
        if not self.results:
            print("No test results available.")
            return
            
        total_tests = len(self.results)
        passed_tests = sum(1 for r in self.results if r['passed'])
        failed_tests = total_tests - passed_tests
        
        print(f"\n{'='*60}")
        print(f"TEST SUMMARY")
        print(f"{'='*60}")
        print(f"Total tests: {total_tests}")
        print(f"Passed: {passed_tests}")
        print(f"Failed (False Positives): {failed_tests}")
        print(f"Success rate: {(passed_tests/total_tests)*100:.1f}%")
        
        if failed_tests > 0:
            print(f"\nFALSE POSITIVE CASES:")
            for result in self.results:
                if not result['passed']:
                    print(f"\n- {result['name']}")
                    print(f"  Conversation: {result['conversation'][:100]}...")
                    print(f"  Tools called: {[t['tool'] for t in result['tools_called']]}")


# Define test cases that should NOT trigger any tool calls
def create_false_positive_test_cases() -> List[FalsePositiveTestCase]:
    """Create a collection of test cases that should not trigger tool calls."""
    
    test_cases = [
        FalsePositiveTestCase(
            name="Normal greeting conversation",
            conversation="Collector: Hi Debby, this is John from ABC Collections regarding your account. How are you today?\nDebby: Hi John, I'm doing okay. What can I help you with?",
            description="Simple greeting exchange with no opt-out requests"
        ),
        
        FalsePositiveTestCase(
            name="Voicemail reached",
            conversation="Collector: Hi Debby, this is John from ABC Collections. I'm calling about your account. Please give me a call back at 555-0123.\n[Voicemail system]: You have reached Debby's voicemail...",
            description="Reaching voicemail should not trigger opt-out"
        ),
        
        FalsePositiveTestCase(
            name="Debby being rude but not opting out",
            conversation="Collector: Hi Debby, I'm calling about your overdue account.\nDebby: I don't want to talk to you right now. You people are so annoying!\nCollector: I understand you're frustrated, but we need to discuss this.\nDebby: Whatever, just make it quick.",
            description="Rude behavior without explicit opt-out request"
        ),
        
        FalsePositiveTestCase(
            name="Debby acting annoyed about the call",
            conversation="Collector: Hello Debby, this is Sarah from Collections.\nDebby: Ugh, not again. This is so frustrating. I'm having a terrible day and now this.\nCollector: I'm sorry to hear that. Can we talk about your account?\nDebby: Fine, but I'm not happy about it.",
            description="Expressing annoyance without requesting to stop calls"
        ),
        
        FalsePositiveTestCase(
            name="Debby doesn't answer initially",
            conversation="Collector: Hello? Debby?\n[No response for 10 seconds]\nCollector: Hello, is this Debby?\nDebby: Yes, sorry, I was distracted. What do you need?",
            description="Not answering immediately should not trigger restrictions"
        ),
        
        FalsePositiveTestCase(
            name="Debby mentions being busy but continues conversation",
            conversation="Collector: Hi Debby, calling about your account.\nDebby: Oh, I'm really busy right now with work stuff, but I guess we can talk quickly.\nCollector: I appreciate that. Let's discuss your payment options.\nDebby: Okay, what are my options?",
            description="Mentioning being busy but agreeing to talk"
        ),
        
        FalsePositiveTestCase(
            name="Automated greeting message",
            conversation="[Automated message]: Thank you for calling. Your call is important to us. Please hold while we connect you.\nCollector: Hello, is this Debby?\nDebby: Yes, this is Debby.",
            description="Automated messages should not trigger any actions"
        ),
        
        FalsePositiveTestCase(
            name="Debby asks about payment but no scheduling request",
            conversation="Collector: Hi Debby, we need to discuss your overdue balance.\nDebby: How much do I owe exactly?\nCollector: Your current balance is $1,250.\nDebby: That's a lot. I need to figure out how to handle this.",
            description="Discussing payment without requesting specific follow-up timing"
        ),
        
        FalsePositiveTestCase(
            name="Debby mentions work but no restriction request",
            conversation="Collector: Hello Debby, calling about your account.\nDebby: I'm at work right now but I can talk for a few minutes.\nCollector: Thank you. Let's discuss your payment plan.\nDebby: Okay, what do you suggest?",
            description="Mentioning work location without requesting work-time restrictions"
        ),
        
        FalsePositiveTestCase(
            name="General complaint about debt collection",
            conversation="Collector: Hi Debby, this is about your outstanding balance.\nDebby: I hate dealing with debt collectors. This whole situation is stressful.\nCollector: I understand this is difficult.\nDebby: Yeah, it really is. But I know I need to deal with it.",
            description="General complaints about debt collection without opt-out requests"
        )
    ]
    
    return test_cases


if __name__ == "__main__":
    # Create and run the test framework
    framework = CommPrefsTestFramework()
    
    # Add all test cases
    test_cases = create_false_positive_test_cases()
    for test_case in test_cases:
        framework.add_test_case(test_case)
    
    # Run tests
    results = framework.run_all_tests()
    
    # Print summary
    framework.print_summary()
    
    # Save results to JSON file for analysis
    with open('false_positive_test_results.json', 'w') as f:
        json.dump(results, f, indent=2)
    
    print(f"\nDetailed results saved to 'false_positive_test_results.json'")
