<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Form</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            background-color: #f6f9fc;
            margin: 0;
        }
        .payment-form {
            background: white;
            padding: 60px;
            border-radius: 8px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
            width: 350px;
        }
        .payment-form h2 {
            text-align: center;
            margin-bottom: 10px;
        }
        .payment-form p {
            text-align: center;
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 20px;
        }
        .input-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            font-weight: bold;
            margin-bottom: 5px;
        }
        input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ccc;
            border-radius: 5px;
            font-size: 16px;
            box-sizing: border-box;
        }
        select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ccc;
            border-radius: 5px;
            font-size: 16px;
            box-sizing: border-box;
            appearance: none;
            -webkit-appearance: none;
            -moz-appearance: none;
            background: url('data:image/svg+xml;charset=US-ASCII,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="gray"><path d="M4 6l4 4 4-4z"/></svg>') no-repeat right 10px center;
            background-color: white;
            background-size: 12px;
        }
        .card-details {
            display: flex;
            justify-content: space-between;
            gap: 10px;
        }
        .card-details .input-group {
            flex: 1;
        }
        button {
            width: 100%;
            padding: 10px;
            background-color: #6772e5;
            color: white;
            font-size: 16px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin-top: 10px;
        }
        button:hover {
            background-color: #5469d4;
        }
    </style>
</head>
<body>
    <form class="payment-form" method="POST" action="/v0/payments">
        <h2>Payment Details</h2>
        <p>Amount Due: ${{ payment_amount }}</p>
        <input type="hidden" name="payment_id" value="{{ payment_id }}">
        <div class="input-group">
            <label for="first-name">First Name</label>
            <input type="text" autocomplete="cc-given-name" id="first-name" name="first_name" required>
        </div>
        <div class="input-group">
            <label for="last-name">Last Name</label>
            <input type="text" autocomplete="cc-last-name" id="last-name" name="last_name" required>
        </div>
        <div class="input-group">
            <label for="card-number">Card Number</label>
            <input type="text" autocomplete="cc-number" id="card-number" name="card_number" required>
        </div>
        <div class="card-details">
            <div class="input-group">
                <label for="expiry-month">Expiry Month</label>
                <select id="expiry-month" autocomplete="cc-exp-month" name="expiry_month" required>
                    <option value="" disabled selected>Month</option>
                    <option value="01">01</option>
                    <option value="02">02</option>
                    <option value="03">03</option>
                    <option value="04">04</option>
                    <option value="05">05</option>
                    <option value="06">06</option>
                    <option value="07">07</option>
                    <option value="08">08</option>
                    <option value="09">09</option>
                    <option value="10">10</option>
                    <option value="11">11</option>
                    <option value="12">12</option>
                </select>
            </div>
            <div class="input-group">
                <label for="expiry-year">Expiry Year</label>
                <select id="expiry-year"autocomplete="cc-exp-year" name="expiry_year" required>
                    <option value="" disabled selected>Year</option>
                    <script>
                        const yearSelect = document.getElementById('expiry-year');
                        const currentYear = new Date().getFullYear();
                        for (let i = 0; i < 12; i++) {
                            const year = currentYear + i;
                            const option = document.createElement('option');
                            option.value = year;
                            option.textContent = year;
                            yearSelect.appendChild(option);
                        }
                    </script>
                </select>
            </div>
            <div class="input-group">
                <label for="cvc">CVC</label>
                <input type="text" autocomplete="cc-csc"  id="cvc" name="card_code" required>
            </div>
        </div>
        <div class="input-group">
            <label for="address">Address</label>
            <input type="text" id="address" name="address" required>
        </div>
        <div class="input-group">
            <label for="city">City</label>
            <input type="text" id="city" name="city" required>
        </div>
        <div class="input-group">
            <label for="state">State</label>
            <input type="text" id="state" name="state" required>
        </div>
        <div class="input-group">
            <label for="zip">ZIP Code</label>
            <input type="text" id="zip" name="zip" required>
        </div>
        <div class="input-group">
            <label for="country">Country</label>
            <select id="country" name="country" required>
                <option value="" disabled selected>Country</option>
                <option value="US">United States</option>
                <!-- Add more countries as needed -->
            </select>
        </div>
        <button type="submit">Pay Now</button>
    </form>
</body>
</html>