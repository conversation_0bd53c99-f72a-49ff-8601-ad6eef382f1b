import React, { useEffect, useRef, useState } from "react";
import {
  CTable,
  CTableHead,
  CTableRow,
  CTableHeaderCell,
  CTableBody,
  CTableDataCell,
  CButton,
  CBadge,
  CCard,
  CCardBody,
  CCardHeader,
  CAccordion,
  CAccordionItem,
  CAccordionHeader,
  CAccordionBody,
  CModal,
  CModalHeader,
  CModalTitle,
  CModalBody,
  CModalFooter,
} from "@coreui/react";
import { cilCheck, cilTrash, cilChevronBottom, cilHistory, cilX, cilPencil } from "@coreui/icons";
import { CIcon } from "@coreui/icons-react";
import CommunicationHistoryModal from "./CommunicationHistoryModal";
import { getApiUrl } from '../utils/apiConfig'

const ApprovalsTable = () => {
  const [defaulters, setDefaulters] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [expandedRows, setExpandedRows] = useState(new Set());
  const [showCommHistory, setShowCommHistory] = useState(false);
  const [selectedDefaulterId, setSelectedDefaulterId] = useState(null);
  const [showPastDateModal, setShowPastDateModal] = useState(false);
  const [pastDateMessage, setPastDateMessage] = useState('');
  const [editingActionItem, setEditingActionItem] = useState(null);
  const [editedActionItem, setEditedActionItem] = useState(null);
  const [savingActionItem, setSavingActionItem] = useState(false);
  const observerRef = useRef(null);

  // Confirmation modal states
  const [showApproveModal, setShowApproveModal] = useState(false);
  const [showRejectModal, setShowRejectModal] = useState(false);
  const [selectedPaymentId, setSelectedPaymentId] = useState(null);
  const [selectedPaymentInfo, setSelectedPaymentInfo] = useState(null);

  // Edit payment states
  const [editingPayment, setEditingPayment] = useState(null);
  const [editedPayment, setEditedPayment] = useState(null);

  const [showNewArrangementRow, setShowNewArrangementRow] = useState({}); // { [defaulterId_accountId]: true }
  const [newArrangementForm, setNewArrangementForm] = useState({}); // { [defaulterId_accountId]: { ...fields } }
  const [newArrangementLoading, setNewArrangementLoading] = useState({}); // { [defaulterId_accountId]: bool }
  const [newArrangementError, setNewArrangementError] = useState({}); // { [defaulterId_accountId]: string }

  const fetchPayments = async () => {
    if (isLoading) return;
    setIsLoading(true);

    try {
      const response = await fetch(
        getApiUrl(`/v0/payments?approved=false`),
        {
          method: "GET",
          headers: {
            "ngrok-skip-browser-warning": "true",
            "Access-Control-Allow-Origin": "*",
            "Content-Type": "application/json",
          },
        }
      );
      const data = await response.json();
      console.log('Payments data:', data);

      // Group payments by defaulter and then by account (issue_id)
      const groupedByDefaulter = {};
      (data.payments || []).forEach(payment => {
        const defaulterId = payment.defaulter?.id;
        const issueId = payment.issue?.issue_id;
        if (!defaulterId || !issueId) return;

        // Create defaulter group if it doesn't exist
        if (!groupedByDefaulter[defaulterId]) {
          groupedByDefaulter[defaulterId] = {
            id: defaulterId,
            defaulter: payment.defaulter,
            accounts: {},
            totalOutstanding: 0,
            totalPendingPayments: 0,
            actionitems: payment.actionitems || []
          };
        }

        // Create account group if it doesn't exist
        if (!groupedByDefaulter[defaulterId].accounts[issueId]) {
          groupedByDefaulter[defaulterId].accounts[issueId] = {
            issueId: issueId,
            issue: payment.issue,
            pendingPayments: [],
            outstanding: payment.issue?.outstanding_amount || '$0.00'
          };
        }

        // Add this payment to the account's pending payments
        groupedByDefaulter[defaulterId].accounts[issueId].pendingPayments.push(payment);
      });

      // Calculate totals and convert accounts object to array
      const defaultersArray = Object.values(groupedByDefaulter).map(defaulter => {
        const accountsArray = Object.values(defaulter.accounts);

        // Calculate total outstanding across all accounts
        let totalOutstanding = 0;
        let totalPendingPayments = 0;

        accountsArray.forEach(account => {
          // Parse outstanding amount (remove $ and commas, convert to number)
          const outstandingStr = account.outstanding.replace(/[$,]/g, '');
          const outstandingNum = parseFloat(outstandingStr) || 0;
          totalOutstanding += outstandingNum;
          totalPendingPayments += account.pendingPayments.length;
        });

        return {
          ...defaulter,
          accounts: accountsArray,
          totalOutstanding: `$${totalOutstanding.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`,
          totalPendingPayments: totalPendingPayments
        };
      });

      console.log('Grouped defaulters with accounts:', defaultersArray);

      setDefaulters(defaultersArray);
      setHasMore(false);
    } catch (error) {
      console.error("Error fetching payments:", error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchPayments();
  }, []);

  // Function to check if any action items have past scheduled dates
  const checkForPastActionItems = (actionItems) => {
    if (!actionItems || actionItems.length === 0) {
      return { hasPastDates: false, pastItems: [] };
    }

    const now = new Date();
    const pastItems = [];

    actionItems.forEach(item => {
      if (item.action_date && item.action_time) {
        // Combine date and time into a single Date object
        const scheduledDateTime = new Date(`${item.action_date}T${item.action_time}`);

        if (scheduledDateTime < now) {
          pastItems.push({
            date: item.action_date,
            time: item.action_time,
            channel: item.action_channel,
            reason: item.action_reason
          });
        }
      }
    });

    return {
      hasPastDates: pastItems.length > 0,
      pastItems: pastItems
    };
  };

  const handleApproveClick = (e, payment) => {
    e.stopPropagation();

    // Find the defaulter that contains this payment to check their action items
    const defaulter = defaulters.find(d =>
      d.accounts.some(account =>
        account.pendingPayments.some(p => p.id === payment.id)
      )
    );

    if (defaulter && defaulter.actionitems) {
      const pastDateCheck = checkForPastActionItems(defaulter.actionitems);

      if (pastDateCheck.hasPastDates) {
        const pastItemsText = pastDateCheck.pastItems.map(item =>
          `• ${item.date} ${item.time} - ${item.channel} (${item.reason})`
        ).join('\n');

        setPastDateMessage(
          `Cannot approve payment because the following scheduled actions have past dates:\n\n${pastItemsText}\n\nPlease update the scheduled action dates before approving.`
        );
        setShowPastDateModal(true);
        return;
      }
    }

    setSelectedPaymentId(payment.id);
    setSelectedPaymentInfo(payment);
    setShowApproveModal(true);
  };

  const handleRejectClick = (e, payment) => {
    e.stopPropagation();
    setSelectedPaymentId(payment.id);
    setSelectedPaymentInfo(payment);
    setShowRejectModal(true);
  };

  const handleApproveAll = (e, defaulter) => {
    e.stopPropagation();

    // Check for past action items before approving all payments
    if (defaulter.actionitems) {
      const pastDateCheck = checkForPastActionItems(defaulter.actionitems);

      if (pastDateCheck.hasPastDates) {
        const pastItemsText = pastDateCheck.pastItems.map(item =>
          `• ${item.date} ${item.time} - ${item.channel} (${item.reason})`
        ).join('\n');

        setPastDateMessage(
          `Cannot approve all payments because the following scheduled actions have past dates:\n\n${pastItemsText}\n\nPlease update the scheduled action dates before approving.`
        );
        setShowPastDateModal(true);
        return;
      }
    }

    // Get all pending payments for this defaulter across all accounts
    const allPendingPayments = defaulter.accounts.flatMap(account => account.pendingPayments);
    
    // For bulk approvals, we don't need to set selectedPaymentId since we'll use the payments array
    setSelectedPaymentId(null); // Explicitly set to null for bulk approvals
    setSelectedPaymentInfo({
      defaulter: defaulter.defaulter,
      payments: allPendingPayments,
      totalAmount: allPendingPayments.reduce((sum, payment) => sum + payment.amount, 0),
      isBulkApproval: true // Add flag to indicate this is a bulk approval
    });
    setShowApproveModal(true);
  };

  const handleEditPayment = (e, payment) => {
    e.stopPropagation();
    setEditingPayment(payment.id);
    setEditedPayment({
      ...payment,
      amount: (payment.amount / 100).toFixed(2) // Convert to dollars for editing
    });
  };

  const handleSavePayment = async (originalPayment) => {
    try {
      const response = await fetch(
        getApiUrl(`/v0/payments/${editingPayment}`),
        {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
            "ngrok-skip-browser-warning": "true",
            "Access-Control-Allow-Origin": "*",
          },
          body: JSON.stringify({
            amount: Math.round(parseFloat(editedPayment.amount) * 100), // Convert back to cents
            recurring: editedPayment.recurring,
            settlement_discount: editedPayment.settlement_discount
          }),
        }
      );

      if (!response.ok) {
        throw new Error("Failed to update payment");
      }

      // Update the local state while maintaining the nested structure
      setDefaulters(prev => prev.map(defaulter => ({
        ...defaulter,
        accounts: defaulter.accounts.map(account => ({
          ...account,
          pendingPayments: account.pendingPayments.map(payment =>
            payment.id === editingPayment
              ? { ...payment, ...editedPayment, amount: Math.round(parseFloat(editedPayment.amount) * 100) }
              : payment
          )
        }))
      })));

      setEditingPayment(null);
      setEditedPayment(null);
    } catch (error) {
      console.error("Error updating payment:", error);
      alert("Failed to update payment. Please try again.");
    }
  };

  const handleCancelEditPayment = () => {
    setEditingPayment(null);
    setEditedPayment(null);
  };

  const handlePaymentInputChange = (field, value) => {
    setEditedPayment(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleApprove = async () => {
    try {
      // Check if this is a bulk approval (multiple payments) or single payment
      const isBulkApproval = selectedPaymentInfo?.isBulkApproval;

      if (isBulkApproval && selectedPaymentInfo.payments) {
        // Approve all payments for this defaulter
        const approvalPromises = selectedPaymentInfo.payments.map(payment =>
          fetch(getApiUrl(`/v0/payments/${payment.id}/approve`), {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              "ngrok-skip-browser-warning": "true",
              "Access-Control-Allow-Origin": "*",
            },
          })
        );

        const responses = await Promise.all(approvalPromises);

        // Check if all approvals were successful
        const failedApprovals = responses.filter(response => !response.ok);
        if (failedApprovals.length > 0) {
          throw new Error(`Failed to approve ${failedApprovals.length} payment(s)`);
        }

        console.log("All payments approved successfully");

        // Remove the defaulter completely since all payments are approved
        setDefaulters(prev => prev.filter(defaulter =>
          defaulter.defaulter?.id !== selectedPaymentInfo.defaulter?.id
        ));
      } else if (selectedPaymentId) { // Only proceed with single approval if we have a valid ID
        // Single payment approval
        const response = await fetch(
          getApiUrl(`/v0/payments/${selectedPaymentId}/approve`),
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              "ngrok-skip-browser-warning": "true",
              "Access-Control-Allow-Origin": "*",
            },
          }
        );

        if (!response.ok) {
          throw new Error("Failed to approve payment");
        }

        const result = await response.json();
        console.log("Payment approved:", result);

        // Remove the payment from the account's pending payments
        setDefaulters(prev => prev.map(defaulter => {
          const updatedAccounts = defaulter.accounts.map(account => ({
            ...account,
            pendingPayments: account.pendingPayments.filter(payment => payment.id !== selectedPaymentId)
          })).filter(account => account.pendingPayments.length > 0);

          const totalPendingPayments = updatedAccounts.reduce((sum, account) => sum + account.pendingPayments.length, 0);
          let totalOutstanding = 0;
          updatedAccounts.forEach(account => {
            const outstandingStr = account.outstanding.replace(/[$,]/g, '');
            const outstandingNum = parseFloat(outstandingStr) || 0;
            totalOutstanding += outstandingNum;
          });

          return {
            ...defaulter,
            accounts: updatedAccounts,
            totalPendingPayments,
            totalOutstanding: `$${totalOutstanding.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`
          };
        }).filter(defaulter => defaulter.accounts.length > 0));
      } else {
        throw new Error("No valid payment ID or payments array found");
      }

      setShowApproveModal(false);
    } catch (error) {
      console.error("Error approving payment(s):", error);
      alert(`Error approving payment(s): ${error.message}`);
      setShowApproveModal(false);
    }
  };

  const handleReject = async () => {
    try {
      const response = await fetch(
        getApiUrl(`/v0/payments/${selectedPaymentId}/reject`),
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "ngrok-skip-browser-warning": "true",
            "Access-Control-Allow-Origin": "*",
          },
        }
      );

      if (!response.ok) {
        throw new Error("Failed to reject payment");
      }

      const result = await response.json();
      console.log("Payment rejected:", result);

      // Remove the payment from the account's pending payments
      setDefaulters(prev => prev.map(defaulter => {
        const updatedAccounts = defaulter.accounts.map(account => ({
          ...account,
          pendingPayments: account.pendingPayments.filter(payment => payment.id !== selectedPaymentId)
        })).filter(account => account.pendingPayments.length > 0); // Remove accounts with no pending payments

        // Recalculate totals
        const totalPendingPayments = updatedAccounts.reduce((sum, account) => sum + account.pendingPayments.length, 0);
        let totalOutstanding = 0;
        updatedAccounts.forEach(account => {
          const outstandingStr = account.outstanding.replace(/[$,]/g, '');
          const outstandingNum = parseFloat(outstandingStr) || 0;
          totalOutstanding += outstandingNum;
        });

        return {
          ...defaulter,
          accounts: updatedAccounts,
          totalPendingPayments,
          totalOutstanding: `$${totalOutstanding.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`
        };
      }).filter(defaulter => defaulter.accounts.length > 0)); // Remove defaulters with no accounts

      setShowRejectModal(false);
    } catch (error) {
      console.error("Error rejecting payment:", error);
      setShowRejectModal(false);
    }
  };

  const toggleRow = (defaulterId) => {
    setExpandedRows(prev => {
      const newSet = new Set(prev);
      if (newSet.has(defaulterId)) {
        newSet.delete(defaulterId);
      } else {
        newSet.add(defaulterId);
      }
      return newSet;
    });
  };

  const handleViewCommHistory = (e, defaulterId) => {
    e.stopPropagation();
    setSelectedDefaulterId(defaulterId);
    setShowCommHistory(true);
  };

  const getActionTypeColor = (category) => {
    switch (category) {
      case "outreach":
        return "primary";
      case "payment-reminder":
        return "warning";
      case "overdue-followup":
        return "danger";
      case "payment-link":
        return "info";
      case "customer-requested":
        return "success";
      default:
        return "secondary";
    }
  };

  const handleEditActionItem = (actionItem) => {
    console.log("Starting edit for action item:", actionItem);
    setEditingActionItem(actionItem.id);
    setEditedActionItem({
      ...actionItem,
      // Ensure all required fields are present
      action_date: actionItem.action_date || '',
      action_time: actionItem.action_time || '',
      action_channel: actionItem.action_channel || 'call',
      action_reason: actionItem.action_reason || ''
    });

    // Set initial height after a small delay to ensure the textarea is rendered
    setTimeout(() => {
      const textarea = document.querySelector(`textarea[data-action-id="${actionItem.id}"]`);
      if (textarea) {
        textarea.style.height = 'auto';
        textarea.style.height = textarea.scrollHeight + 'px';
      }
    }, 100);
  };

  const handleSaveActionItem = async (actionItem) => {
    if (savingActionItem) return; // Prevent double-clicking

    try {
      setSavingActionItem(true);
      console.log("Saving action item:", actionItem.id, editedActionItem);

      // Validate required fields
      if (!editedActionItem.action_date || !editedActionItem.action_time || !editedActionItem.action_reason) {
        alert("Please fill in all required fields (date, time, and reason)");
        return;
      }

      // Validate date format
      const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
      if (!dateRegex.test(editedActionItem.action_date)) {
        alert("Please enter a valid date in YYYY-MM-DD format");
        return;
      }

      // Validate time format
      const timeRegex = /^\d{2}:\d{2}(:\d{2})?$/;
      if (!timeRegex.test(editedActionItem.action_time)) {
        alert("Please enter a valid time in HH:MM format");
        return;
      }

      // Ensure time has seconds
      let formattedTime = editedActionItem.action_time;
      if (formattedTime.length === 5) { // HH:MM format
        formattedTime += ":00"; // Add seconds
      }

      const response = await fetch(
        getApiUrl(`/v0/drafts/${actionItem.id}`),
        {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
            "ngrok-skip-browser-warning": "true",
            "Access-Control-Allow-Origin": "*",
          },
          body: JSON.stringify({
            action_date: editedActionItem.action_date,
            action_time: formattedTime,
            action_channel: editedActionItem.action_channel,
            action_reason: editedActionItem.action_reason.trim(),
          }),
        }
      );

      console.log("Response status:", response.status);

      if (!response.ok) {
        const errorText = await response.text();
        console.error("API Error:", errorText);
        throw new Error(`Failed to update action item: ${response.status} ${errorText}`);
      }

      const result = await response.json();
      console.log("Update result:", result);

      // Update the local state for defaulters
      setDefaulters(prev => prev.map(defaulter => {
        if (defaulter.actionitems) {
          return {
            ...defaulter,
            actionitems: defaulter.actionitems.map(item =>
              item.id === actionItem.id ? { ...item, ...editedActionItem } : item
            )
          };
        }
        return defaulter;
      }));

      setEditingActionItem(null);
      setEditedActionItem(null);

      // Show success message
      alert("Action item updated successfully!");

    } catch (error) {
      console.error("Error updating action item:", error);
      alert(`Error updating action item: ${error.message}`);
    } finally {
      setSavingActionItem(false);
    }
  };

  const handleCancelEdit = () => {
    setEditingActionItem(null);
    setEditedActionItem(null);
  };

  const handleInputChange = (field, value) => {
    console.log(`Updating ${field} to:`, value);
    setEditedActionItem(prev => {
      const updated = {
        ...prev,
        [field]: value
      };
      console.log("Updated editedActionItem:", updated);
      return updated;
    });
  };

  const handleNewArrangementInput = (key, field, value) => {
    setNewArrangementForm(prev => ({
      ...prev,
      [key]: { ...prev[key], [field]: value }
    }));
  };

  const handleAddArrangementClick = (defaulterId, accountId) => {
    const key = `${defaulterId}_${accountId}`;
    setShowNewArrangementRow(prev => ({ ...prev, [key]: true }));
    setNewArrangementForm(prev => ({
      ...prev,
      [key]: {
        defaulter_id: defaulterId,
        amount: '',
        recurrence: 'one-time',
        discount: '',
        due_date: ''
      }
    }));
    setNewArrangementError(prev => ({ ...prev, [key]: '' }));
  };

  const handleCancelNewArrangement = (key) => {
    setShowNewArrangementRow(prev => ({ ...prev, [key]: false }));
    setNewArrangementForm(prev => ({ ...prev, [key]: undefined }));
    setNewArrangementError(prev => ({ ...prev, [key]: '' }));
  };

  const handleSaveNewArrangement = async (key) => {
    setNewArrangementLoading(prev => ({ ...prev, [key]: true }));
    setNewArrangementError(prev => ({ ...prev, [key]: '' }));
    const form = newArrangementForm[key];
    try {
      const payload = {
        ...form,
        amount: parseFloat(form.amount),
        discount: parseFloat(form.discount) || 0
      };
      const response = await fetch(getApiUrl('/v0/payments/create-arrangement'), {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'ngrok-skip-browser-warning': 'true'
        },
        body: JSON.stringify(payload)
      });
      if (!response.ok) {
        const err = await response.json();
        setNewArrangementError(prev => ({ ...prev, [key]: err.error || 'Failed to create arrangement' }));
        setNewArrangementLoading(prev => ({ ...prev, [key]: false }));
        return;
      }
      setShowNewArrangementRow(prev => ({ ...prev, [key]: false }));
      setNewArrangementForm(prev => ({ ...prev, [key]: undefined }));
      setNewArrangementError(prev => ({ ...prev, [key]: '' }));
      fetchPayments();
    } catch (err) {
      setNewArrangementError(prev => ({ ...prev, [key]: 'Network error' }));
    } finally {
      setNewArrangementLoading(prev => ({ ...prev, [key]: false }));
    }
  };

  const hoverStyles = `
    table tbody tr.hover-row:hover:not([colspan]) {
      background-color: rgba(0, 0, 0, 0.075);
    }
  `;

  return (
    <>
      {/* Pending Approvals Table */}
      <h5 className="mb-3">Pending Approvals</h5>
      {defaulters.length === 0 ? (
        <p>No pending approvals.</p>
      ) : (
        <CTable hover responsive className="table-layout-fixed">
          <CTableHead>
            <CTableRow>
              <CTableHeaderCell style={{ width: '20%' }}>Name</CTableHeaderCell>
              <CTableHeaderCell style={{ width: '120px' }}>Pending Payments</CTableHeaderCell>
              <CTableHeaderCell style={{ width: '120px' }}>Outstanding</CTableHeaderCell>
              <CTableHeaderCell style={{ width: '120px' }}>Action Items</CTableHeaderCell>
              <CTableHeaderCell style={{ width: '120px' }}>Actions</CTableHeaderCell>
            </CTableRow>
          </CTableHead>
          <CTableBody>
            {defaulters.map(defaulter => (
              <React.Fragment key={defaulter.id}>
                <CTableRow
                  onClick={() => toggleRow(defaulter.id)}
                  style={{ cursor: 'pointer' }}
                >
                  <CTableDataCell style={{ width: '20%' }}>{defaulter.defaulter?.name || 'Unknown'}</CTableDataCell>
                  <CTableDataCell style={{ width: '120px' }}>
                    <CBadge color="primary">{defaulter.totalPendingPayments}</CBadge>
                  </CTableDataCell>
                  <CTableDataCell style={{ width: '120px' }}>{defaulter.totalOutstanding}</CTableDataCell>
                  <CTableDataCell style={{ width: '120px' }}>
                    <CBadge color="info">{defaulter.actionitems?.length || 0}</CBadge>
                  </CTableDataCell>
                  <CTableDataCell style={{ width: '120px' }}>
                    <div className="d-flex gap-2">
                      <CButton
                        color="success"
                        size="sm"
                        onClick={(e) => handleApproveAll(e, defaulter)}
                        disabled={defaulter.totalPendingPayments === 0}
                      >
                        <CIcon icon={cilCheck} />
                      </CButton>
                      <CButton
                        color="info"
                        size="sm"
                        onClick={(e) => handleViewCommHistory(e, defaulter.defaulter?.id)}
                      >
                        <CIcon icon={cilHistory} />
                      </CButton>
                    </div>
                  </CTableDataCell>
                </CTableRow>
                {expandedRows.has(defaulter.id) && (
                  <CTableRow>
                    <CTableDataCell colSpan={6} className="p-0">
                      <div className="p-3">
                        {/* Accounts Section */}
                        {defaulter.accounts?.map((account, accountIndex) => {
                          const pendingPayments = account.pendingPayments.filter(p => p._approved === 'pending');
                          const cancellationPayments = account.pendingPayments.filter(p => p._approved === 'pending_cancellation');
                          const key = `${defaulter.id}_${account.issueId}`;
                          return (
                            <div key={account.issueId} className="mb-4">
                              <div className="d-flex justify-content-between align-items-center mb-2">
                                <h6>Account #{account.issueId} - Outstanding: {account.outstanding}</h6>
                                <CButton color="primary" size="sm" onClick={() => handleAddArrangementClick(defaulter.id, account.issueId)}>
                                  +
                                </CButton>
                              </div>
                              {/* New Arrangement Row */}
                              {showNewArrangementRow[key] && (
                                <CCard className="mb-2 p-2 bg-light">
                                  <form onSubmit={e => { e.preventDefault(); handleSaveNewArrangement(key); }}>
                                    <div className="row g-2 align-items-end">
                                      <div className="col">
                                        <label className="form-label mb-0 text-black">Amount</label>
                                        <input type="number" step="0.01" className="form-control form-control-sm" value={newArrangementForm[key]?.amount || ''} onChange={e => handleNewArrangementInput(key, 'amount', e.target.value)} required />
                                      </div>
                                      <div className="col">
                                        <label className="form-label mb-0 text-black">Recurrence</label>
                                        <select className="form-select form-select-sm" value={newArrangementForm[key]?.recurrence || 'one-time'} onChange={e => handleNewArrangementInput(key, 'recurrence', e.target.value)} required>
                                          <option value="one-time">One-time</option>
                                          <option value="weekly">Weekly</option>
                                          <option value="monthly">Monthly</option>
                                        </select>
                                      </div>
                                      <div className="col">
                                        <label className="form-label mb-0 text-black">Discount (%)</label>
                                        <input type="number" step="0.01" className="form-control form-control-sm" value={newArrangementForm[key]?.discount || ''} onChange={e => handleNewArrangementInput(key, 'discount', e.target.value)} />
                                      </div>
                                      <div className="col">
                                        <label className="form-label mb-0 text-black">Due Date</label>
                                        <input type="date" className="form-control form-control-sm" value={newArrangementForm[key]?.due_date || ''} onChange={e => handleNewArrangementInput(key, 'due_date', e.target.value)} required />
                                      </div>
                                      <div className="col-auto d-flex gap-2">
                                        <button type="submit" className="btn btn-success btn-sm" disabled={newArrangementLoading[key]}>Save</button>
                                        <button type="button" className="btn btn-secondary btn-sm" onClick={() => handleCancelNewArrangement(key)} disabled={newArrangementLoading[key]}>Cancel</button>
                                      </div>
                                    </div>
                                    {newArrangementError[key] && <div className="text-danger small mt-1">{newArrangementError[key]}</div>}
                                  </form>
                                </CCard>
                              )}
                              {/* Pending Payments Box */}
                              {pendingPayments.length > 0 && (
                                <CCard className="mb-3">
                                  <CCardHeader>
                                    <CBadge color="primary">{pendingPayments.length} pending payments</CBadge>
                                  </CCardHeader>
                                  <CCardBody>
                                    <CTable hover responsive className="table-layout-fixed">
                                      <CTableHead>
                                        <CTableRow>
                                          <CTableHeaderCell style={{ width: '15%' }}>Amount</CTableHeaderCell>
                                          <CTableHeaderCell style={{ width: '15%' }}>Due Date</CTableHeaderCell>
                                          <CTableHeaderCell style={{ width: '12%' }}>Type</CTableHeaderCell>
                                          <CTableHeaderCell style={{ width: '12%' }}>Discounted</CTableHeaderCell>
                                          <CTableHeaderCell style={{ width: '14%' }}>Status</CTableHeaderCell>
                                          <CTableHeaderCell style={{ width: '120px', textAlign: 'right' }}>Actions</CTableHeaderCell>
                                        </CTableRow>
                                      </CTableHead>
                                      <CTableBody>
                                        {pendingPayments.map((payment, index) => (
                                          <CTableRow key={payment.id}>
                                            <CTableDataCell style={{ width: '15%' }}>
                                              {editingPayment === payment.id ? (
                                                <input
                                                  type="number"
                                                  step="0.01"
                                                  className="form-control form-control-sm"
                                                  value={editedPayment.amount}
                                                  onChange={(e) => handlePaymentInputChange('amount', e.target.value)}
                                                  style={{ width: '100px' }}
                                                />
                                              ) : (
                                                `$${(payment.amount / 100).toFixed(2)}`
                                              )}
                                            </CTableDataCell>
                                            <CTableDataCell style={{ width: '15%' }}>
                                              {payment.recurring
                                                ? (payment.start_date ? payment.start_date : '-')
                                                : (payment.due_date ? payment.due_date : '-')}
                                            </CTableDataCell>
                                            <CTableDataCell style={{ width: '12%' }}>
                                              {editingPayment === payment.id ? (
                                                <select
                                                  className="form-select form-select-sm"
                                                  value={editedPayment.recurring}
                                                  onChange={(e) => handlePaymentInputChange('recurring', e.target.value === 'true')}
                                                >
                                                  <option value="false">One-time</option>
                                                  <option value="true">Recurring</option>
                                                </select>
                                              ) : (
                                                payment.recurring ? (
                                                  <CBadge color="info">Recurring</CBadge>
                                                ) : (
                                                  <CBadge color="warning">One-time</CBadge>
                                                )
                                              )}
                                            </CTableDataCell>
                                            <CTableDataCell style={{ width: '12%' }}>
                                              {editingPayment === payment.id ? (
                                                <select
                                                  className="form-select form-select-sm"
                                                  value={editedPayment.settlement_discount}
                                                  onChange={(e) => handlePaymentInputChange('settlement_discount', e.target.value === 'true')}
                                                >
                                                  <option value="false">No</option>
                                                  <option value="true">Yes</option>
                                                </select>
                                              ) : (
                                                payment.settlement_discount ? (
                                                  <CBadge color="success">Yes</CBadge>
                                                ) : (
                                                  <CBadge color="secondary">No</CBadge>
                                                )
                                              )}
                                            </CTableDataCell>
                                            <CTableDataCell style={{ width: '14%' }}>
                                              <CBadge color="primary">Pending</CBadge>
                                            </CTableDataCell>
                                            <CTableDataCell style={{ width: '120px', textAlign: 'right' }}>
                                              {editingPayment === payment.id ? (
                                                <div className="d-flex gap-2 justify-content-end">
                                                  <CButton
                                                    color="success"
                                                    size="sm"
                                                    onClick={() => handleSavePayment(payment)}
                                                  >
                                                    <CIcon icon={cilCheck} /> Save
                                                  </CButton>
                                                  <CButton
                                                    color="secondary"
                                                    size="sm"
                                                    onClick={handleCancelEditPayment}
                                                  >
                                                    <CIcon icon={cilX} /> Cancel
                                                  </CButton>
                                                </div>
                                              ) : (
                                                <div className="d-flex gap-2 justify-content-end">
                                                  <CButton
                                                    color="primary"
                                                    size="sm"
                                                    onClick={(e) => handleEditPayment(e, payment)}
                                                  >
                                                    <CIcon icon={cilPencil} /> Edit
                                                  </CButton>
                                                  <CButton
                                                    color="warning"
                                                    size="sm"
                                                    onClick={(e) => handleRejectClick(e, payment)}
                                                  >
                                                    <CIcon icon={cilX} /> Reject
                                                  </CButton>
                                                </div>
                                              )}
                                            </CTableDataCell>
                                          </CTableRow>
                                        ))}
                                      </CTableBody>
                                    </CTable>
                                  </CCardBody>
                                </CCard>
                              )}
                              {/* Cancellation Requests Box */}
                              {cancellationPayments.length > 0 && (
                                <CCard className="mb-3">
                                  <CCardHeader>
                                    <CBadge color="danger">{cancellationPayments.length} pending cancellations</CBadge>
                                  </CCardHeader>
                                  <CCardBody>
                                    <CTable hover responsive className="table-layout-fixed">
                                      <CTableHead>
                                        <CTableRow>
                                          <CTableHeaderCell style={{ width: '15%' }}>Amount</CTableHeaderCell>
                                          <CTableHeaderCell style={{ width: '15%' }}>Due Date</CTableHeaderCell>
                                          <CTableHeaderCell style={{ width: '12%' }}>Type</CTableHeaderCell>
                                          <CTableHeaderCell style={{ width: '12%' }}>Discounted</CTableHeaderCell>
                                          <CTableHeaderCell style={{ width: '14%' }}>Status</CTableHeaderCell>
                                          <CTableHeaderCell style={{ width: '120px', textAlign: 'right' }}>Actions</CTableHeaderCell>
                                        </CTableRow>
                                      </CTableHead>
                                      <CTableBody>
                                        {cancellationPayments.map((payment, index) => (
                                          <CTableRow key={payment.id}>
                                            <CTableDataCell style={{ width: '15%' }}>
                                              {editingPayment === payment.id ? (
                                                <input
                                                  type="number"
                                                  step="0.01"
                                                  className="form-control form-control-sm"
                                                  value={editedPayment.amount}
                                                  onChange={(e) => handlePaymentInputChange('amount', e.target.value)}
                                                  style={{ width: '100px' }}
                                                />
                                              ) : (
                                                `$${(payment.amount / 100).toFixed(2)}`
                                              )}
                                            </CTableDataCell>
                                            <CTableDataCell style={{ width: '15%' }}>
                                              {payment.recurring
                                                ? (payment.start_date ? payment.start_date : '-')
                                                : (payment.due_date ? payment.due_date : '-')}
                                            </CTableDataCell>
                                            <CTableDataCell style={{ width: '12%' }}>
                                              {editingPayment === payment.id ? (
                                                <select
                                                  className="form-select form-select-sm"
                                                  value={editedPayment.recurring}
                                                  onChange={(e) => handlePaymentInputChange('recurring', e.target.value === 'true')}
                                                >
                                                  <option value="false">One-time</option>
                                                  <option value="true">Recurring</option>
                                                </select>
                                              ) : (
                                                payment.recurring ? (
                                                  <CBadge color="info">Recurring</CBadge>
                                                ) : (
                                                  <CBadge color="warning">One-time</CBadge>
                                                )
                                              )}
                                            </CTableDataCell>
                                            <CTableDataCell style={{ width: '12%' }}>
                                              {editingPayment === payment.id ? (
                                                <select
                                                  className="form-select form-select-sm"
                                                  value={editedPayment.settlement_discount}
                                                  onChange={(e) => handlePaymentInputChange('settlement_discount', e.target.value === 'true')}
                                                >
                                                  <option value="false">No</option>
                                                  <option value="true">Yes</option>
                                                </select>
                                              ) : (
                                                payment.settlement_discount ? (
                                                  <CBadge color="success">Yes</CBadge>
                                                ) : (
                                                  <CBadge color="secondary">No</CBadge>
                                                )
                                              )}
                                            </CTableDataCell>
                                            <CTableDataCell style={{ width: '14%' }}>
                                              <CBadge color="danger">Cancellation</CBadge>
                                            </CTableDataCell>
                                            <CTableDataCell style={{ width: '120px', textAlign: 'right' }}>
                                              {editingPayment === payment.id ? (
                                                <div className="d-flex gap-2 justify-content-end">
                                                  <CButton
                                                    color="success"
                                                    size="sm"
                                                    onClick={() => handleSavePayment(payment)}
                                                  >
                                                    <CIcon icon={cilCheck} /> Save
                                                  </CButton>
                                                  <CButton
                                                    color="secondary"
                                                    size="sm"
                                                    onClick={handleCancelEditPayment}
                                                  >
                                                    <CIcon icon={cilX} /> Cancel
                                                  </CButton>
                                                </div>
                                              ) : (
                                                <div className="d-flex gap-2 justify-content-end">
                                                  <CButton
                                                    color="primary"
                                                    size="sm"
                                                    onClick={(e) => handleEditPayment(e, payment)}
                                                  >
                                                    <CIcon icon={cilPencil} /> Edit
                                                  </CButton>
                                                  <CButton
                                                    color="warning"
                                                    size="sm"
                                                    onClick={(e) => handleRejectClick(e, payment)}
                                                  >
                                                    <CIcon icon={cilX} /> Reject
                                                  </CButton>
                                                </div>
                                              )}
                                            </CTableDataCell>
                                          </CTableRow>
                                        ))}
                                      </CTableBody>
                                    </CTable>
                                  </CCardBody>
                                </CCard>
                              )}
                            </div>
                          );
                        })}

                        {/* Action Items Section */}
                        {defaulter.actionitems?.length > 0 && (
                          <div>
                            <h6 className="mb-3">Scheduled Actions</h6>
                            <CTable hover responsive className="table-layout-fixed">
                              <CTableHead>
                                <CTableRow>
                                  <CTableHeaderCell style={{ width: '25%' }}>Date & Time</CTableHeaderCell>
                                  <CTableHeaderCell style={{ width: '10%' }}>Channel</CTableHeaderCell>
                                  <CTableHeaderCell style={{ width: '15%' }}>Type</CTableHeaderCell>
                                  <CTableHeaderCell style={{ width: '40%' }}>Reason</CTableHeaderCell>
                                  <CTableHeaderCell style={{ width: '10%' }}>Actions</CTableHeaderCell>
                                </CTableRow>
                              </CTableHead>
                              <CTableBody>
                                {defaulter.actionitems.map((action, index) => (
                                  <CTableRow key={index}>
                                <CTableDataCell style={{ width: '25%' }}>
                                  {editingActionItem === action.id ? (
                                    <div className="d-flex gap-2">
                                      <input
                                        type="date"
                                        className="form-control form-control-sm"
                                        value={editedActionItem.action_date || ''}
                                        onChange={(e) => handleInputChange('action_date', e.target.value)}
                                        style={{ width: '50%' }}
                                        required
                                      />
                                      <input
                                        type="time"
                                        className="form-control form-control-sm"
                                        value={editedActionItem.action_time || ''}
                                        onChange={(e) => handleInputChange('action_time', e.target.value)}
                                        style={{ width: '50%' }}
                                        required
                                      />
                                    </div>
                                  ) : (
                                    `${action.action_date} ${action.action_time}`
                                  )}
                                </CTableDataCell>
                                <CTableDataCell style={{ width: '10%' }}>
                                  {editingActionItem === action.id ? (
                                    <select
                                      className="form-select form-select-sm"
                                      value={editedActionItem.action_channel || 'call'}
                                      onChange={(e) => handleInputChange('action_channel', e.target.value)}
                                    >
                                      <option value="call">Call</option>
                                      <option value="email">Email</option>
                                    </select>
                                  ) : (
                                    <CBadge color="info">{action.action_channel}</CBadge>
                                  )}
                                </CTableDataCell>
                                <CTableDataCell style={{ width: '15%' }}>
                                  <CBadge color={getActionTypeColor(action.category)}>
                                    {action.category}
                                  </CBadge>
                                </CTableDataCell>
                                <CTableDataCell style={{ width: '40%' }}>
                                  {editingActionItem === action.id ? (
                                    <textarea
                                      className="form-control form-control-sm"
                                      value={editedActionItem.action_reason || ''}
                                      onChange={(e) => handleInputChange('action_reason', e.target.value)}
                                      rows={5}
                                      style={{ width: '100%' }}
                                      data-action-id={action.id}
                                      placeholder="Enter action reason..."
                                    />
                                  ) : (
                                    <div style={{ maxWidth: '100%', whiteSpace: 'normal' }}>
                                      {action.action_reason}
                                    </div>
                                  )}
                                </CTableDataCell>
                                <CTableDataCell>
                                  {editingActionItem === action.id ? (
                                    <div className="d-flex gap-2 align-items-center">
                                      <CButton
                                        color="success"
                                        size="sm"
                                        onClick={() => handleSaveActionItem(action)}
                                        className="rounded-circle p-1 d-flex align-items-center justify-content-center"
                                        style={{ width: '24px', height: '24px' }}
                                        disabled={savingActionItem}
                                      >
                                        {savingActionItem ? (
                                          <div className="spinner-border spinner-border-sm text-white" role="status" style={{ width: '12px', height: '12px' }}>
                                            <span className="visually-hidden">Loading...</span>
                                          </div>
                                        ) : (
                                          <CIcon icon={cilCheck} className="text-white" style={{ fontSize: '0.8rem' }} />
                                        )}
                                      </CButton>
                                      <CButton
                                        color="danger"
                                        size="sm"
                                        onClick={handleCancelEdit}
                                        className="rounded-circle p-1 d-flex align-items-center justify-content-center"
                                        style={{ width: '24px', height: '24px' }}
                                        disabled={savingActionItem}
                                      >
                                        <CIcon icon={cilX} className="text-white" style={{ fontSize: '0.8rem' }} />
                                      </CButton>
                                    </div>
                                  ) : (
                                    <CButton
                                      color="primary"
                                      size="sm"
                                      onClick={() => handleEditActionItem(action)}
                                    >
                                      Edit
                                    </CButton>
                                  )}
                                </CTableDataCell>
                                  </CTableRow>
                                ))}
                              </CTableBody>
                            </CTable>
                          </div>
                        )}
                      </div>
                    </CTableDataCell>
                  </CTableRow>
                )}
              </React.Fragment>
            ))}
          </CTableBody>
        </CTable>
      )}
      <CommunicationHistoryModal
        show={showCommHistory}
        onClose={() => setShowCommHistory(false)}
        defaulterId={selectedDefaulterId}
      />

      {/* Approve Confirmation Modal */}
      <CModal visible={showApproveModal} onClose={() => setShowApproveModal(false)} alignment="center">
        <CModalHeader>
          <CModalTitle>Confirm Approval</CModalTitle>
        </CModalHeader>
        <CModalBody>
          {selectedPaymentInfo && selectedPaymentInfo.payments ? (
            // Multiple payments approval
            <div>
              <p>Are you sure you want to approve all pending payments for this customer?</p>
              <div>
                <p><strong>Customer:</strong> {selectedPaymentInfo.defaulter?.name || 'Unknown'}</p>
                <p><strong>Total Payments:</strong> {selectedPaymentInfo.payments.length}</p>
                <p><strong>Total Amount:</strong> ${(selectedPaymentInfo.totalAmount / 100).toFixed(2)}</p>
                <div className="mt-3">
                  <strong>Payments to approve:</strong>
                  <ul className="mt-2">
                    {selectedPaymentInfo.payments.map((payment, index) => (
                      <li key={payment.id}>
                        ${(payment.amount / 100).toFixed(2)}
                        {payment.recurring ? ' (Recurring)' : ' (One-time)'}
                        {payment.settlement_discount ? ' - Settlement Discount' : ''}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>
          ) : (
            // Single payment approval
            <div>
              <p>Are you sure you want to approve this payment?</p>
              {selectedPaymentInfo && (
                <div>
                  <p><strong>Name:</strong> {selectedPaymentInfo.defaulter?.name || 'Unknown'}</p>
                  <p><strong>Amount:</strong> ${(selectedPaymentInfo.amount / 100).toFixed(2)}</p>
                  {selectedPaymentInfo.settlement_discount && (
                    <p><strong>Note:</strong> This payment includes a settlement discount.</p>
                  )}
                </div>
              )}
            </div>
          )}
        </CModalBody>
        <CModalFooter>
          <CButton color="secondary" onClick={() => setShowApproveModal(false)}>
            Cancel
          </CButton>
          <CButton color="success" onClick={handleApprove}>
            Approve
          </CButton>
        </CModalFooter>
      </CModal>

      {/* Reject Confirmation Modal */}
      <CModal visible={showRejectModal} onClose={() => setShowRejectModal(false)} alignment="center">
        <CModalHeader>
          <CModalTitle>Confirm Rejection</CModalTitle>
        </CModalHeader>
        <CModalBody>
          <p>Are you sure you want to reject this payment?</p>
          {selectedPaymentInfo && (
            <div>
              <p><strong>Name:</strong> {selectedPaymentInfo.defaulter?.name || 'Unknown'}</p>
              <p><strong>Amount:</strong> ${(selectedPaymentInfo.amount / 100).toFixed(2)}</p>
            </div>
          )}
        </CModalBody>
        <CModalFooter>
          <CButton color="secondary" onClick={() => setShowRejectModal(false)}>
            Cancel
          </CButton>
          <CButton color="danger" onClick={handleReject}>
            Reject
          </CButton>
        </CModalFooter>
      </CModal>

      {/* Past Date Error Modal */}
      <CModal visible={showPastDateModal} onClose={() => setShowPastDateModal(false)} alignment="center">
        <CModalHeader>
          <CModalTitle>Cannot Approve - Past Scheduled Date</CModalTitle>
        </CModalHeader>
        <CModalBody>
          <div style={{ whiteSpace: 'pre-line' }}>
            {pastDateMessage}
          </div>
        </CModalBody>
        <CModalFooter>
          <CButton color="primary" onClick={() => setShowPastDateModal(false)}>
            OK
          </CButton>
        </CModalFooter>
      </CModal>

    </>
  );
};

export default ApprovalsTable;