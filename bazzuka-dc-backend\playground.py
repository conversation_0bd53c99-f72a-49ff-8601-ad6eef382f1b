from datetime import datetime, timezone

from dotenv import load_dotenv

load_dotenv()


def test_tools_engine():
    from app.tools import makeToolsEngine

    engine = makeToolsEngine()

    assert (
        engine.execute("get_issue", {"email_address": "<EMAIL>"})[
            "defaulter_id"
        ]
        == 2
    )

    print(engine.execute("get_comm_history", "2"))
    print(engine.getTools())


def test_inbound_email():
    from app.core.comm_manager import CommManager

    manager = CommManager()

    email_data = {
        "text": "Hello I am wondering how much I owe on my account.",
        "subject": "Account Inquiry",
        "senderAddress": "<EMAIL>",
        "recipientAddress": "<EMAIL>",
        "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S%z"),
    }
    manager.process("email", email_data)


def test_get_comm_history():
    from app.core.issues import IssueRepository

    manager = IssueRepository()
    print(manager.get_comm_history(0))


def test_tools_new():
    from app.tools import makeToolsEngine

    engine = makeToolsEngine()
    print(
        engine.execute("get_issue", {"email_address": "<EMAIL>"})[
            "defaulter_id"
        ]
    )


def test_nylas():
    from app.core.messenger import Messenger

    messenger = Messenger()
    data = {}
    data["subject"] = "Hello World"
    data["text"] = "This is a test."
    # reply_to = data.get("replyTo", "<EMAIL>")   # NOTE: Do we need a replyTo field?
    data["recipientName"] = "Michael Womick"
    data["recipientAddress"] = "<EMAIL>"

    messenger.send("email", data)


def process_email():
    from app.core.comm_manager import CommManager

    manager = CommManager()

    email_data = {
        "text": "Hello I was wondering if you could call me at the next hour to discuss my account.",
        "subject": "Call Request",
        "senderAddress": "<EMAIL>",
        "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S%z"),
    }

    manager.process("email", email_data)


def make_outbound_call():
    from app.core.comm_manager import CommManager

    manager = CommManager()

    manager.execute(
        "1",
        "call",
        "This is Karan's first contact.",
        action_channel_reason="As the previous communication was an outbound email, a follow-up call is scheduled to ensure compliance and engagement. Contact is planned after the 7-day period since the initial email. Given the 146 DPD, a call is appropriate to increase urgency.",
    )


def send_outbound_email():
    from app.core.messenger import Messenger

    messenger = Messenger()

    email_data = {
        "text": "Hello Michael,\n\nThis is a reminder that you have a balance of $50 past due.\nPlease get in touch with us to resolve this issue as soon as possible.\n\nThank you,\nDebby",
        "subject": "Account Inquiry",
        "recipientAddress": "<EMAIL>",
        "recipientName": "Michael Womick",
    }

    messenger.send("email", email_data)


def _test_duplicate_email_part(manager):
    today = datetime.now(timezone.utc)
    post_data = {
        "id": "testingtesting123",
        "timestamp": today.strftime("%Y-%m-%d %H:%M:%S%z"),
        "subject": "Pay my debt in full",
        "text": "Hey Debby, Can I pay my debt in full?",
        "senderAddress": "<EMAIL>",
        "senderName": "Michael Womick",
        "recipientAddress": "<EMAIL>",
    }

    manager.process("email", post_data)


def test_duplicate_email():
    from app.core.comm_manager import CommManager

    manager = CommManager()
    print("Inserting email...")
    _test_duplicate_email_part(manager)
    print("Inserting another email")
    _test_duplicate_email_part(manager)
    print("Done.")


def test_defaulters():
    from app.core.defaulters import Defaulters

    caller_number = "+***********"
    defaulters = Defaulters()
    defaulter_id = str(defaulters.get_defaulter_by_phone(caller_number)["defaulter_id"])
    issue = defaulters.get_defaulter_by_issue(defaulter_id)
    print(issue)


def test_count_calls():
    from app.utils.supabase.queries import get_seven_day_call_count

    print(get_seven_day_call_count())


def test_comm_stats():
    from app.utils.supabase.queries import get_comm_stats

    print(get_comm_stats())


def test_new_ai():
    from app.core.ai import client

    print(
        client.do("compose_email")
        .with_context(
            {
                "summary": "Michael called regarding an outstanding debt with Rabidoo's trading post. He was informed about his overdue payment of $50 for 10 gallons of whiskey, which was due on September 11, 2024. Michael proposed splitting the payment into two installments, which was agreed upon as $25 to be paid today and another $25 next week. He plans to make the payments online and requested a reminder for the second payment, but was advised to set an alert himself. Michael accepted this suggestion and the call ended amicably."
            }
        )
        .execute()
    )


def test_insert_defaulters():
    from app.utils.supabase.queries import insert_defaulters

    defaulters = [
        {
            "name": "Michael Womick",
            "phone": "+***********",
            "email": "<EMAIL>",
            "location": "New York, NY",
            "outstanding_amount": 50,
            "delinquency_date": "2024-09-11",
            "age": 26,
            "due_date": "2024-09-11",
            "principal_amount": 50,
        },
    ]
    print(insert_defaulters(defaulters))


def test_get_issue():
    from app.utils.supabase.queries import get_issues_by_phone

    print(get_issues_by_phone("+***********"))


def test_get_defaulter_for_issue():
    from app.utils.supabase.queries import get_defaulter_for_issue

    print(get_defaulter_for_issue(20))


def test_ai_client():
    from app.core.ai import client

    args = {
        "previous_summary": "This is a new user.",
        "case_info": "Name is Michael, owes $500 since Jan 11, 2025",
        "channel": "Email",
        "timestamp": "Jan 26, 2025",
        "direction": "outbound",
        "transcript": "Hello Michael, This is a reminder that you have $500 overdue on your account. Please reach out to resolve this quickly. Best, Debby.",
    }

    print(client.do("summarize").with_context(args).execute())

    new_args = {
        "current_conversation_summary": "On January 26, 2025, an outbound email reminder was sent to Michael regarding the $500 overdue on his account since January 11, 2025. The email, sent by Debby, urged Michael to resolve the matter promptly. No response or commitment from Michael was documented in this interaction.",
        "current_conversation_timestamp": "Jan 26, 2025",
        "current_conversation_direction": "outbound",
        "previous_conversation_summary": "This is a new issue. No contact has been made.",
        "previous_conversation_logs": "Email(Hello Michael, This is a reminder that you have $500 overdue on your account. Please reach out to resolve this quickly. Best, Debby.)",
    }

    print(client.do("analyze").with_context(new_args).execute())
    print(client.do("summarize").with_context(args).execute())


def pay():
    from app.utils.supabase.queries import insert_payment

    response = insert_payment(184, 2000)
    print("inserted payment: " + str(response))
    print(str(response.data[0]["uuid"]))

    uuid = response.data[0]["uuid"]


def test_get_defaulter_by_phone():
    from app.core.defaulters import defaulters

    defaulter_id = "123456"

    print("Test 1:")
    print(defaulters.get_defaulter_by_id(defaulter_id))

    print("Test 2:")
    print(defaulters.get_defaulter_by_phone("+***********"))


def test_get_defaulter_by_email():
    from app.core.defaulters import defaulters

    defaulter_id = "123456"

    print("Test 1:")
    print(defaulters.get_defaulter_by_id(defaulter_id))

    print("Test 2:")
    print(defaulters.get_defaulter_by_email("<EMAIL>"))


def test_get_defaulter_by_id():
    from app.core.defaulters import defaulters

    print(defaulters.get_defaulter_by_id("123456"))


def test_execute_action_item():
    from app.core.comm_manager import CommManager

    manager = CommManager()
    print(
        manager.execute(
            "456789",
            "call",
            action_channel_reason="",
        )
    )


def test_create_and_send_payment_link():
    from app.core.payments import PaymentsTool

    payments_tool = PaymentsTool()
    print(
        payments_tool.create_and_send_payment_link(
            651, [10000, 10000, 10000], ["2025-05-01", "2025-06-01", "2025-07-01"]
        )
    )


def test_delete_payment_plan():
    from app.core.payments import PaymentsTool

    payments_tool = PaymentsTool()
    print(payments_tool.delete_payment_plan(651))


transcript = """
User
Thank you for calling our school. To ensure we provide you with the best possible service, this call will be recorded.
AI
Hi. This is Debbie calling from Bazooka speaking on a recorded Am I speaking with Nadivo?
User
Yes. This is her.
AI
Hi, Nadivo. Are you doing today?
User
I'm doing good. How are you doing?
AI
I'm glad to hear you're doing good. I'm doing well too. Thanks for asking. I wanted to speak to you about an important matter. But before I can share any
User
Sure.
AI
thank you. Before we continue, I need to verify your identity just to make sure I'm speaking to the right person. Any information you provide will be used for verification purposes only. Could you please provide either the last 4 digits of your Social Security number or your date of birth?
User
9 8 77.
AI
This will just take a sec.
AI
Thanks. I found your account. Before we go any further, I'm required to let you know this is an attempt to collect a debt, and any information obtained will be used for that purpose. This communication is from a debt collector. So Neddy, I'm calling about your account with Behzuka. You currently have an unresolved balance of 3500 dollars that's been overdue since last August. I noticed we spoke a little while back, You had asked for a lower payment plan and a bigger discount, but nothing's been set up yet. Can you pay the full balance today, or what would you like to do?
User
Like I mentioned last time, I'm able to make a small payment but not the 1 that you're asking for. And when I asked you if I can make that payment, you told me no.
AI
I totally remember our last conversation, Nadi. You mentioned you could
User
I'm sorry. I mentioned I can pay what?
AI
Last time, you said you'd be able to pay 25 dollars per month since you're currently unemployed. But our policy requires a minimum payment of 50 dollars each month we can offer a 5 percent discount, so 33 25. If you're able to pay in a 1 time lump sum could you let me know if either of these options sound possible?
User
Yeah. I can pay the 33.
AI
Or if your situation Okay. Just to be clear, you'd like to pay 3325 dollars as a 1 time payment to settle your account. Correct? If so,
User
No. No. No. No. I didn't say that.
AI
oh, sorry about that. Um, just to confirm, you said you can pay 33 dollars? Is that as a monthly payment?
User
Correct.
AI
Thanks for clarifying, Nati. So just to be upfront, our lowest monthly payment allowed by policy is 50 dollars per month. Any payment under that can't be set up on a plan. Sorry. Would you be able to do 50 per month right now?
User
No. Like I told you, I can't do that.
AI
I understand, Nadia, and I know things can get pretty tight, especially if you're not working right now. Um, is your situation about the same since last time, or has anything changed that might help you make the minimum payment? Or, um, is there any way
User
No. My situation is the same. I can make a 25 dollar payment and that's it.
AI
Thanks for letting me know, Nadia. I wish I could help more with that, but 25 dollars is below the minimum payment our policy allows so I can't set up a plan for that amount. If things change and you can do it
User
Okay.
AI
I know that's frustrating, and I'm really sorry I can't make an exception here. If anything changes, or when you're able to do at least 50 dollars per month, please reach out. Would you want me to schedule a follow-up call to check-in with you later, or is there another way I can help today?
User
No.
AI
Alright, Nat.
User
No. I don't wanna schedule a follow-up.
AI
Understood, Nat. I want
User
Don't call me at work.
AI
Alright. I won't call you at work anymore. If you ever wanna discuss your account or able to resolve the payment, you can reach out us whenever you're ready. Take care, Nat.
User
I'm not gonna reach out. And my name's not Nat.
AI
That's under I'm sorry about that. I meant naughty. Thank you for letting me know. I won't contact you by phone anymore. If you ever change your mind or need assistance, you can always connect with us directly. Take care, Nadia.
User
Thank you. Bye.
"""


def test_analyze_communication_preferences():
    from app.core.ai.comm_prefs_agent import analyze_communication_preferences

    print(analyze_communication_preferences(transcript, "456789"))


if __name__ == "__main__":
    # test_delete_payment_plan()
    # test_create_and_send_payment_link()
    test_analyze_communication_preferences()
