"""
Direct test of the communication preferences agent to identify false positives.

This script directly tests the AI client with conversation inputs to detect
when tools are called inappropriately.
"""

import os
import json
from unittest.mock import Mock, patch, MagicMock
from typing import List, Dict, Any

# Mock environment variables
os.environ.setdefault('SUPABASE_URL', 'https://test.supabase.co')
os.environ.setdefault('SUPABASE_KEY', 'test_key')
os.environ.setdefault('OPENAI_API_KEY', 'test_key')

# Mock external dependencies
with patch('supabase.create_client'):
    with patch('openai.OpenAI'):
        # Import after mocking
        import sys
        sys.path.append('bazzuka-dc-backend')
        
        from app.core.ai.comm_prefs_agent import (
            CommunicationPreferencesPromptGenerator, 
            CommunicationPreferencesAction,
            make_comm_preferences_client
        )


class DirectCommPrefsTest:
    """Direct test of the communication preferences agent."""
    
    def __init__(self):
        self.test_cases = []
        self.results = []
        
    def add_test_case(self, name: str, conversation: str, description: str = ""):
        """Add a test case."""
        self.test_cases.append({
            'name': name,
            'conversation': conversation,
            'description': description,
            'defaulter_id': 'test_123'
        })
    
    def test_single_case(self, test_case: Dict[str, Any]) -> Dict[str, Any]:
        """Test a single case and return results."""
        result = {
            'name': test_case['name'],
            'conversation': test_case['conversation'],
            'description': test_case['description'],
            'passed': False,
            'tools_called': [],
            'error': None,
            'response_content': None
        }
        
        try:
            # Create mock OpenAI client
            mock_openai_client = MagicMock()
            
            # Create the prompt generator
            prompt_gen = CommunicationPreferencesPromptGenerator()
            
            # Generate the messages
            messages = prompt_gen.generate({
                'conversation': test_case['conversation'],
                'defaulter_id': test_case['defaulter_id']
            })
            
            # Mock the OpenAI response - simulate no tool calls (correct behavior)
            mock_response = MagicMock()
            mock_response.choices = [MagicMock()]
            mock_response.choices[0].message = MagicMock()
            mock_response.choices[0].message.content = "No action needed."
            mock_response.choices[0].message.tool_calls = None
            
            # Mock the client to return our response
            mock_openai_client.chat.completions.create.return_value = mock_response
            
            # Test with mocked tools to see if they get called
            with patch('app.core.payments.PaymentsTool.opt_out_of_communications') as mock_opt_out, \
                 patch('app.core.payments.PaymentsTool.restrict_communication') as mock_restrict, \
                 patch('app.utils.supabase.queries.get_restrictions_by_defaulter_id') as mock_get_restrictions:
                
                mock_get_restrictions.return_value = Mock(data=[])
                mock_opt_out.return_value = "Success"
                mock_restrict.return_value = "Success"
                
                # Create client with mocked OpenAI
                with patch('app.utils.openai.client.openai', mock_openai_client):
                    client = make_comm_preferences_client()
                    
                    # Execute the analysis
                    analysis_result = client.do("communication_preferences_analysis").with_context({
                        "defaulter_id": test_case['defaulter_id'],
                        "conversation": test_case['conversation'],
                    }).execute()
                    
                    # Check if tools were called
                    tools_called = []
                    if mock_opt_out.called:
                        tools_called.append({
                            'tool': 'opt_out_of_communications',
                            'call_count': mock_opt_out.call_count,
                            'args': str(mock_opt_out.call_args) if mock_opt_out.call_args else None
                        })
                    if mock_restrict.called:
                        tools_called.append({
                            'tool': 'restrict_communications',
                            'call_count': mock_restrict.call_count,
                            'args': str(mock_restrict.call_args) if mock_restrict.call_args else None
                        })
                    
                    result['tools_called'] = tools_called
                    result['passed'] = len(tools_called) == 0  # Pass if no tools called
                    result['response_content'] = str(analysis_result) if analysis_result else None
                    
        except Exception as e:
            result['error'] = str(e)
            result['passed'] = False
            
        return result
    
    def run_all_tests(self) -> List[Dict[str, Any]]:
        """Run all test cases."""
        self.results = []
        
        print(f"Running {len(self.test_cases)} false positive test cases...")
        
        for i, test_case in enumerate(self.test_cases, 1):
            print(f"\nTest {i}/{len(self.test_cases)}: {test_case['name']}")
            result = self.test_single_case(test_case)
            self.results.append(result)
            
            if result['passed']:
                print("✅ PASSED - No tools called")
            else:
                print("❌ FAILED - Tools called inappropriately:")
                for tool_call in result['tools_called']:
                    print(f"   - {tool_call['tool']}: {tool_call['call_count']} times")
                if result['error']:
                    print(f"   - Error: {result['error']}")
        
        return self.results
    
    def print_summary(self):
        """Print test summary."""
        if not self.results:
            print("No test results available.")
            return
            
        total = len(self.results)
        passed = sum(1 for r in self.results if r['passed'])
        failed = total - passed
        
        print(f"\n{'='*60}")
        print(f"TEST SUMMARY")
        print(f"{'='*60}")
        print(f"Total tests: {total}")
        print(f"Passed: {passed}")
        print(f"Failed (False Positives): {failed}")
        print(f"Success rate: {(passed/total)*100:.1f}%")
        
        if failed > 0:
            print(f"\nFALSE POSITIVE CASES:")
            for result in self.results:
                if not result['passed']:
                    print(f"\n- {result['name']}")
                    print(f"  Conversation: {result['conversation'][:100]}...")
                    for tool_call in result['tools_called']:
                        print(f"  Tool called: {tool_call['tool']} ({tool_call['call_count']} times)")


def create_test_cases():
    """Create test cases that should NOT trigger tool calls."""
    return [
        {
            'name': 'Normal greeting',
            'conversation': "Collector: Hi Debby, this is John from ABC Collections. How are you?\nDebby: Hi John, I'm okay. What can I help you with?",
            'description': 'Simple greeting exchange'
        },
        {
            'name': 'Voicemail reached',
            'conversation': "Collector: Hi Debby, this is John from ABC Collections. Please call me back.\n[Voicemail]: You've reached Debby's voicemail...",
            'description': 'Reaching voicemail should not trigger opt-out'
        },
        {
            'name': 'Debby being rude',
            'conversation': "Collector: Hi Debby, calling about your account.\nDebby: I don't want to talk to you right now. You people are annoying!\nCollector: I understand you're frustrated.\nDebby: Whatever, just make it quick.",
            'description': 'Rude behavior without explicit opt-out'
        },
        {
            'name': 'Debby acting annoyed',
            'conversation': "Collector: Hello Debby, this is Sarah.\nDebby: Ugh, not again. This is so frustrating.\nCollector: I'm sorry to hear that.\nDebby: Fine, but I'm not happy about it.",
            'description': 'Expressing annoyance without opt-out request'
        },
        {
            'name': 'Debby mentions being busy',
            'conversation': "Collector: Hi Debby, calling about your account.\nDebby: I'm really busy right now with work, but I guess we can talk quickly.\nCollector: I appreciate that.\nDebby: Okay, what are my options?",
            'description': 'Mentioning being busy but agreeing to talk'
        },
        {
            'name': 'General complaint about debt collection',
            'conversation': "Collector: Hi Debby, about your balance.\nDebby: I hate dealing with debt collectors. This is stressful.\nCollector: I understand.\nDebby: Yeah, but I know I need to deal with it.",
            'description': 'General complaints without opt-out requests'
        }
    ]


if __name__ == "__main__":
    # Create and run tests
    tester = DirectCommPrefsTest()
    
    # Add test cases
    test_cases = create_test_cases()
    for case in test_cases:
        tester.add_test_case(case['name'], case['conversation'], case['description'])
    
    # Run tests
    results = tester.run_all_tests()
    
    # Print summary
    tester.print_summary()
    
    # Save results
    with open('false_positive_results.json', 'w') as f:
        json.dump(results, f, indent=2)
    
    print(f"\nResults saved to 'false_positive_results.json'")
