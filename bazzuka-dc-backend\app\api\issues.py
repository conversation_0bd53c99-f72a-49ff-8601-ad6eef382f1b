from flask import Blueprint, jsonify

from app.core.issues import IssueRepository

issues_bp = Blueprint("issues", __name__)

manager = IssueRepository()


@issues_bp.route("/<defaulter_id>", methods=["GET", "OPTIONS"])
def get_issue(defaulter_id):
    # for production, assert UUID:
    # assert(len(defaulter_id) == 36)
    # lookup issue by defaulter_id and org_id not just defaulter_id

    # get the defaulter info, summary
    # issue = manager.get_debt_info(defaulter_id)
    # issue = {"summary": manager.get_summaries(defaulter_id)[0]["summary"]}
    return jsonify({"error": "This endpoint is temporarily deprecated."}), 200
