-- Minimal setup script for actionitems notifications in dev schema
-- This script <PERSON><PERSON><PERSON> sets up the Postgres Changes publication without any custom triggers or functions

-- Add the actionitems table to the publication
ALTER PUBLICATION supabase_realtime ADD TABLE dev.actionitems;

-- Enable RLS on the actionitems table
ALTER TABLE dev.actionitems ENABLE ROW LEVEL SECURITY;

-- Create a policy to allow authenticated users to select from the actionitems table
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies 
        WHERE schemaname = 'dev' 
        AND tablename = 'actionitems' 
        AND policyname = 'Allow authenticated users to view actionitems'
    ) THEN
        CREATE POLICY "Allow authenticated users to view actionitems"
        ON dev.actionitems
        FOR SELECT
        TO authenticated
        USING (true);
        RAISE NOTICE 'Created policy for actionitems';
    ELSE
        RAISE NOTICE 'Policy for actionitems already exists';
    END IF;
END
$$;

-- Verify the setup
SELECT pubname, schemaname, tablename 
FROM pg_publication_tables 
WHERE pubname = 'supabase_realtime';

-- List RLS policies on the actionitems table
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual
FROM pg_policies
WHERE schemaname = 'dev' AND tablename = 'actionitems';

-- Test the setup with an insert (uncomment to test)
-- INSERT INTO dev.actionitems (timestamp, action_date, action_time, action_channel_id, action_channel_content, defaulter_id, action_reason, is_human_followup)
-- VALUES (NOW(), '2024-06-01', '10:00', 1, '<EMAIL>', 1, 'Test human follow-up', true)
-- RETURNING id, action_date, action_reason, is_human_followup; 