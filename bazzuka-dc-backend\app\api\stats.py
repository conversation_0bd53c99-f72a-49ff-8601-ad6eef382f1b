from flask import Blueprint, jsonify, request
from datetime import datetime

from app.utils.supabase.queries import get_comm_stats, get_payments_collected

stats_bp = Blueprint("stats", __name__)


@stats_bp.route("", methods=["GET", "OPTIONS"])
@stats_bp.route("/", methods=["GET", "OPTIONS"])
def get_stats():
    if request.method == "OPTIONS":
        return jsonify({}), 200

    # Get date range parameters
    start_date = request.args.get("start_date")
    end_date = request.args.get("end_date")

    # Convert string dates to datetime objects if provided
    start_date = datetime.fromisoformat(start_date) if start_date else None
    end_date = datetime.fromisoformat(end_date) if end_date else None

    print("Fetching dashboard stats...")

    # Use a fixed org_id for now
    org_id = "8241d390-a8b5-4f59-b0a9-b95c074db3f5"

    stats = get_comm_stats(org_id, start_date, end_date)
    payments_cents = get_payments_collected(start_date, end_date)
    print(f"Payments total cents: {payments_cents}")
    stats["payments"] = payments_cents / 100 if payments_cents else 0

    return jsonify({"data": stats}), 200
