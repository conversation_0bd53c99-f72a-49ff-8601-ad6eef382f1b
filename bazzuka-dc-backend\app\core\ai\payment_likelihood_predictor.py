from app.ai.client import AIClient, Action, PromptGenerator
from app.utils.openai.client import openai as openai_client
from datetime import datetime
from pydantic import BaseModel, Field
import json


class PaymentLikelihoodOutput(BaseModel):
    likelihood: int = Field(description="Payment likelihood score from 0-5")
    # reason: str = Field(description="Reason for the payment likelihood score")


class PaymentLikelihoodToolPromptGenerator(PromptGenerator):
    def __init__(self):
        super().__init__()
        self.prompt = ""
        with open("data/prompts/payment_likelihood.md", "r", encoding="utf-8") as file:
            self.prompt = file.read()
        self.inputs_prologue = ""

    def make_inputs(self, args):
        conversation = args.get("conversation", "The conversation is empty.")
        inputs = f"""
        - **Channel**: {args["channel"]}
        - **Communication History**: {args["commlogs"]}
        - **Recent Conversation**: {args["conversation"]}
        - **Today's Date**: {datetime.now().strftime("%m/%d/%Y")}
        """
        return inputs

    def generate(self, args):
        messages = []
        messages.append(
            {
                "role": "system",
                "content": self.prompt + self.inputs_prologue + self.make_inputs(args),
            }
        )
        return messages


class PaymentLikelihoodToolAction(Action):
    def __init__(self):
        super().__init__()
        self.prompt_generator = PaymentLikelihoodToolPromptGenerator()
        self.tool_engine = None
        self.structured_output_type = PaymentLikelihoodOutput


def make_payment_likelihood_predictor_client():
    client = AIClient(openai_client)
    payment_likelihood_predictor_action = PaymentLikelihoodToolAction()
    client.register("predict_payment_likelihood", payment_likelihood_predictor_action)
    return client


payment_likelihood_predictor_client = make_payment_likelihood_predictor_client()


def predict_payment_likelihood(
    conversation: str, channel: str = "email", commlogs: str = ""
) -> int:
    """
    Predict the likelihood of payment based on the conversation.

    Args:
        conversation: The conversation text to analyze
        channel: The communication channel (default: "email")
        commlogs: Previous communication logs (default: "")

    Returns:
        int: Payment likelihood score from 1-5
    """
    result = (
        payment_likelihood_predictor_client.do("predict_payment_likelihood")
        .with_context(
            {"conversation": conversation, "channel": channel, "commlogs": commlogs}
        )
        .execute()
    )

    result = json.loads(result)

    return result["likelihood"]
