import os
import requests

VAPI_BASE_URL = os.environ.get("VAPI_BASE_URL", "https://api.vapi.ai")
VAPI_API_KEY = os.environ.get("VAPI_API_KEY", "")


def get_call_transcripts(calls):
    """
    Get call transcripts by call ids.
    """
    transcripts = []
    for call in calls:
        response = requests.get(
            f"{VAPI_BASE_URL}/call/{call['comm_id']}",
            headers={
                "Content-Type": "application/json",
                "Authorization": f"Bearer {VAPI_API_KEY}",
            },
        )
        response.raise_for_status()
        result = response.json()

        try:
            transcript = result["transcript"]
        except KeyError:
            transcript = "Transcript not available."

        log = {
            "type": "call",
            "endedReason": str(result["endedReason"]),
            "direction": str(call["direction"]),
            "timestamp": str(result["createdAt"]),
            "transcript": transcript,
        }

        transcripts.append(log)

    return transcripts


def generate_call_forwarding_payload(forwarding_list):
    destinations = [
        {
            "type": "number",
            "number": item["number"],
            "message": f"I am forwarding your call to {item['name']}. Please stay on the line.",
            "transferPlan": {
                "mode": "warm-transfer-with-summary",
                "summaryPlan": {
                    "enabled": True,
                    "messages": [
                        {
                            "role": "system",
                            "content": "Please provide a summary of the call."
                        },
                        {
                            "role": "user",
                            "content": "Here is the transcript:\n\n{{transcript}}\n\n"
                        }
                    ]
                }
            }
        }
        for item in forwarding_list
    ]

    function_parameters = {
        "type": "object",
        "properties": {
            "destination": {
                "type": "string",
                "enum": [item["number"] for item in forwarding_list],
                "description": "The destination to transfer the call to.",
            }
        },
        "required": ["destination"],
    }

    messages = [
        {
            "type": "request-start",
            "content": f"I am forwarding your call to {item['name']}. Please stay on the line.",
            "conditions": [
                {"param": "destination", "operator": "eq", "value": item["number"]}
            ],
        }
        for item in forwarding_list
    ]

    payload = {
        "type": "transferCall",
        "destinations": destinations,
        "function": {
            "name": "transferCall",
            "description": "Use this function to transfer the call. Only use it when following instructions that explicitly ask you to use the transferCall function. DO NOT call this function unless you are instructed to do so.",
            "parameters": function_parameters,
        }
    }

    return payload
