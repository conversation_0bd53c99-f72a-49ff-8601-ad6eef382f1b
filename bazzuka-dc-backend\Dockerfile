# Use Python 3.12 slim image for smaller size and security
FROM python:3.12-slim as production

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    FLASK_ENV=production \
    FLASK_APP=app.main:app

# Create non-root user for security
RUN groupadd --gid 1000 appuser && \
    useradd --uid 1000 --gid appuser --shell /bin/bash --create-home appuser

# Install system dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    curl \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Install Poetry
RUN pip install poetry==1.8.3

# Set work directory
WORKDIR /app

# Copy Poetry files first as root, then change ownership
COPY pyproject.toml poetry.lock ./
RUN chown appuser:appuser pyproject.toml poetry.lock

# Switch to non-root user before installing Poetry packages
USER appuser

# Configure Poetry to not create virtual environment (install to system Python)
ENV POETRY_VENV_IN_PROJECT=false

# Install dependencies
RUN poetry install --only=main --no-root

# Switch back to root to copy files and set permissions
USER root

# Copy application code and set ownership
COPY --chown=appuser:appuser . .

# Create necessary directories and set permissions
RUN mkdir -p /app/logs && \
    chown -R appuser:appuser /app && \
    chmod -R 755 /app

# Switch back to non-root user for runtime
USER appuser

# Expose port
EXPOSE 8000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=120s --retries=3 \
    CMD curl -f http://localhost:8000/v0/status/health || curl -f http://localhost:8000/ || exit 1

# Use Poetry to run gunicorn
CMD ["poetry", "run", "gunicorn", "--bind", "0.0.0.0:8000", "--workers", "4", "--worker-class", "gthread", "--threads", "2", "--timeout", "120", "--keep-alive", "2", "--max-requests", "1000", "--max-requests-jitter", "100", "--access-logfile", "-", "--error-logfile", "-", "app.main:app"] 