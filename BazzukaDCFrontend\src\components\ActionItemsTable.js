import React, { useEffect, useRef, useState } from "react";
import { useNavigate } from "react-router-dom";
import {
  CTable,
  CTableHead,
  CTableRow,
  CTableHeaderCell,
  CTableBody,
  CTableDataCell,
  CAvatar,
  CProgress,
  CButton,
  CPopover,
  CBadge,
  CModal,
  CModalHeader,
  CModalTitle,
  CModalBody,
  CModalFooter,
  CForm,
  CFormLabel,
  CFormInput,
  CFormSelect,
  CFormTextarea,
  CButtonGroup
} from "@coreui/react";
import { cilPeople, cilPlus, cilTrash, cilReload, cilOptions } from "@coreui/icons";
import { CIcon } from "@coreui/icons-react";
import OutboundCallIcon from "../assets/icons/outgoing-call.png";
import OutboundEmailIcon from "../assets/icons/outgoing-email.png";
import OutboundTextIcon from "../assets/icons/outgoing-message.png";
import UserIcon from "../assets/icons/user.png";
import { useFormattedTimestamp } from "../utils/dateUtils";
import { getApiUrl } from '../utils/apiConfig'

// Add CSS for spinning icon
import './styles.css';

const communicationIcons = {
  "call": OutboundCallIcon,
  "email": OutboundEmailIcon,
  "text": OutboundTextIcon,
};

const getTypeColor = (type) => {
  // Add console log to debug type values
  console.log("Type value:", type);

  if (!type) return "secondary";

  // Convert to lowercase and trim for more robust comparison
  const normalizedType = type.toLowerCase().trim();

  switch (normalizedType) {
    case "outreach":
      return "primary";
    case "payment-reminder":
    case "payment reminder":
      return "warning";
    case "overdue-followup":
    case "overdue followup":
      return "danger";
    case "payment-link":
    case "payment link":
      return "info";
    case "customer-requested":
    case "customer requested":
      return "success";
    default:
      console.log("Using default color for type:", normalizedType);
      return "secondary";
  }
};

const getActionTypeBadge = (type) => {
  const badgeColors = {
    "follow_up": "info",
    "payment": "success",
    "verification": "warning",
    "default": "secondary"
  };

  return (
    <CBadge color={badgeColors[type] || badgeColors.default} className="ms-2">
      {type.replace('_', ' ')}
    </CBadge>
  );
};

const ActionItemsTable = () => {
  const navigate = useNavigate();
  const [searchQuery, setSearchQuery] = useState("");
  const [tableExample, setTableExample] = useState([]);
  const [editedContent, setEditedContent] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const observerRef = useRef(null);
  const formatTimestamp = useFormattedTimestamp();

  // Added state for modals
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [successMessage, setSuccessMessage] = useState("");

  // Form state
  const [formData, setFormData] = useState({
    action_date: new Date().toISOString().split('T')[0], // Default to today
    action_time: new Date().toTimeString().slice(0, 8), // Default to current time
    action_channel: "call",
    action_reason: "",
    action_channel_content: "",
    defaulter_id: "",
    category: "outreach"
  });

  // Update the form validation state
  const [formErrors, setFormErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [apiError, setApiError] = useState("");

  const limit = 10; // Number of items to fetch per request
  const [offset, setOffset] = useState(0); // Current offset for pagination

  // Update state for edit modal
  const [showEditModal, setShowEditModal] = useState(false);
  const [editItemId, setEditItemId] = useState(null);
  const [editFormData, setEditFormData] = useState({
    action_date: "",
    action_time: "",
    action_channel: "",
    action_reason: "",
    action_channel_content: "",
    category: ""
  });

  // Add state for view more modal
  const [showViewModal, setShowViewModal] = useState(false);
  const [currentItem, setCurrentItem] = useState(null);

  const fetchData = async () => {
    if (isLoading) return;
    setIsLoading(true);

    try {
        const response = await fetch(getApiUrl(`/v0/drafts?limit=${limit}&offset=${offset}`),
          {
            method: "GET",
            headers: {
              "ngrok-skip-browser-warning": "true",
              "Access-Control-Allow-Origin": "*",
              "Application-Type": "application/json",
            },
          }
        );
        const json_data = await response.json();
        console.log(json_data);

        const data = json_data.data;
        // const data = [
        //   { name: "John Doe", action_channel: "call", type:"outreach", action_date: "2023-10-01" },
        //   { name: "Jane Smith", action_channel: "email", type:"payment-reminder", action_date: "2023-10-02" },
        //   { name: "Alice Johnson", action_channel: "text", type:"overdue-followup", action_date: "2023-10-03" },
        //   { name: "Bob Brown", action_channel: "call", type:"payment-link", action_date: "2023-10-04" },
        //   { name: "Charlie Davis", action_channel: "email", type:"customer-requested", action_date: "2023-10-05" },
        //   { name: "Diana Evans", action_channel: "text", type:"outreach", action_date: "2023-10-06" },
        //   { name: "Frank Green", action_channel: "call", type:"payment-reminder", action_date: "2023-10-07" },
        // ];

        if (data.length < limit) {
          setHasMore(false); // No more data to fetch
        }

        setTableExample((prev) => [...prev, ...data]);
        setOffset((prev) => prev + limit); // Increment offset
      } catch (error) {
        console.error("Error fetching data:", error);
      } finally {
        setIsLoading(false);
      }
  };

  // Function to refresh data
  const refreshData = async () => {
    console.log("Refreshing data...");
    setIsLoading(true);
    setOffset(0); // Reset offset
    setHasMore(true); // Reset hasMore
    setTableExample([]); // Clear existing data

    try {
      const response = await fetch(getApiUrl(`/v0/drafts?limit=${limit}&offset=0`),
        {
          method: "GET",
          headers: {
            "ngrok-skip-browser-warning": "true",
            "Access-Control-Allow-Origin": "*",
            "Application-Type": "application/json",
          },
        }
      );
      const json_data = await response.json();

      // Debug log
      console.log("Refresh data received:", json_data);

      const data = json_data.data || [];

      // Debug log each item's type/category
      data.forEach((item, index) => {
        console.log(`Item ${index} type:`, item.type, "category:", item.category);
      });

      if (data.length < limit) {
        setHasMore(false);
      }

      // Add a default type if missing
      const processedData = data.map(item => {
        if (!item.category && !item.type) {
          return {
            ...item,
            category: "outreach" // Default value
          };
        }
        return item;
      });

      setTableExample(processedData);
      setOffset(data.length); // Set offset to length of fetched data
    } catch (error) {
      console.error("Error refreshing data:", error);
      alert("Failed to refresh data. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        const target = entries[0];
        if (target.isIntersecting && hasMore && !isLoading) {
          fetchData();
        }
      },
      { threshold: 1 }
    );

    if (observerRef.current) observer.observe(observerRef.current);
    return () => observer.disconnect();
  }, [hasMore]); // Removed offset from dependency array to prevent observer recreation


    // Function to load the existing content into a modal instead of navigating
   const viewMore = (item) => {
    setCurrentItem(item);
    setShowViewModal(true);
  };

  // Save the updated content
  const handleSave = (index) => {
    console.log('save clicked');

    console.log("Saved content for row:", index, editedContent);

    // Update the existing content (e.g., in state or via an API call)
    summaryPopOverContent[index] = editedContent;

    // Optionally clear the state after saving
    setEditedContent("");
  };

  // Function to handle form changes
  const handleFormChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });

    // Clear error for this field when changed
    if (formErrors[name]) {
      setFormErrors({
        ...formErrors,
        [name]: null
      });
    }

    // Clear API error when user starts typing
    if (apiError) {
      setApiError("");
    }
  };

  // Update form validation
  const validateForm = () => {
    const errors = {};

    // Check required fields
    if (!formData.action_date) errors.action_date = "Date is required";
    if (!formData.action_time) errors.action_time = "Time is required";
    if (!formData.action_channel) errors.action_channel = "Channel is required";
    if (!formData.action_reason) errors.action_reason = "Reason is required";
    if (!formData.defaulter_id) errors.defaulter_id = "Customer ID is required";

    // Date validation
    const today = new Date();
    const selectedDate = new Date(formData.action_date);
    if (selectedDate < today && selectedDate.toDateString() !== today.toDateString()) {
      errors.action_date = "Date cannot be in the past";
    }

    return errors;
  };

  // Update handleSubmit function for the new API
  const handleSubmit = async (e) => {
    if (e) e.preventDefault();

    // Clear previous API error
    setApiError("");

    // Validate form
    const errors = validateForm();
    setFormErrors(errors);

    if (Object.keys(errors).length > 0) {
      return;
    }

    setIsSubmitting(true);

    try {
      // Format data for API
      const payload = {
        action_date: formData.action_date,
        action_time: formData.action_time,
        action_channel: formData.action_channel,
        action_reason: formData.action_reason,
        action_channel_content: formData.action_channel_content || "",
        defaulter_id: formData.defaulter_id,
        payment_likelihood: 3, // Default value since user doesn't set this
        category: formData.category || "outreach"
      };

      // Send to API
      const response = await fetch(getApiUrl("/v0/drafts"), {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "ngrok-skip-browser-warning": "true"
        },
        body: JSON.stringify(payload)
      });

      const result = await response.json();

      if (response.ok) {
        // Close the modal
        setShowCreateModal(false);

        // Reset form
        setFormData({
          action_date: new Date().toISOString().split('T')[0],
          action_time: new Date().toTimeString().slice(0, 8),
          action_channel: "call",
          action_reason: "",
          action_channel_content: "",
          defaulter_id: "",
          category: "outreach"
        });

        // Clear any errors
        setFormErrors({});
        setApiError("");

        // Show success modal
        setSuccessMessage("Action item created successfully!");
        setShowSuccessModal(true);
      } else {
        // Set API error message to display in the form
        setApiError(result.message || "Failed to create action item");
      }
    } catch (error) {
      console.error("Error creating action item:", error);
      setApiError("Network error. Please check your connection and try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Function to delete an action item
  const handleDelete = async (id) => {
    if (window.confirm("Are you sure you want to delete this action item?")) {
      try {
        const response = await fetch(getApiUrl(`/v0/drafts/${id}`), {
          method: "DELETE",
          headers: {
            "Content-Type": "application/json",
            "ngrok-skip-browser-warning": "true"
          }
        });

        if (response.ok) {
          // Remove the item from the table
          setTableExample((prevItems) => prevItems.filter(item => item.id !== id));
          // Close the view modal if it's open
          setShowViewModal(false);
        } else {
          const result = await response.json();
          alert(`Error: ${result.message || "Failed to delete action item"}`);
        }
      } catch (error) {
        console.error("Error deleting action item:", error);
        alert("Failed to delete action item. See console for details.");
      }
    }
  };

  // Function to open edit modal
  const handleEdit = (item) => {
    setEditItemId(item.id);
    setEditFormData({
      action_date: item.action_date || "",
      action_time: item.action_time || "",
      action_channel: item.action_channel || "call",
      action_reason: item.action_reason || "",
      action_channel_content: item.action_channel_content || "",
      category: item.category || item.type || "outreach"
    });
    setFormErrors({});
    setShowEditModal(true);
  };

  // Function to handle edit form changes
  const handleEditFormChange = (e) => {
    const { name, value } = e.target;
    setEditFormData({
      ...editFormData,
      [name]: value
    });

    // Clear error for this field when changed
    if (formErrors[name]) {
      setFormErrors({
        ...formErrors,
        [name]: null
      });
    }
  };

  // Function to validate edit form
  const validateEditForm = () => {
    const errors = {};

    // Check required fields
    if (!editFormData.action_date) errors.action_date = "Date is required";
    if (!editFormData.action_time) errors.action_time = "Time is required";
    if (!editFormData.action_channel) errors.action_channel = "Channel is required";
    if (!editFormData.action_reason) errors.action_reason = "Reason is required";

    return errors;
  };

  // Function to handle edit form submission
  const handleEditSubmit = async () => {
    // Validate form
    const errors = validateEditForm();
    setFormErrors(errors);

    if (Object.keys(errors).length > 0) {
      return;
    }

    setIsSubmitting(true);

    try {
      // Format data for API
      const payload = {
        action_date: editFormData.action_date,
        action_time: editFormData.action_time,
        action_channel: editFormData.action_channel,
        action_reason: editFormData.action_reason,
        action_channel_content: editFormData.action_channel_content || ""
      };

      // Send to API
      const response = await fetch(getApiUrl(`/v0/drafts/${editItemId}`), {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
          "ngrok-skip-browser-warning": "true"
        },
        body: JSON.stringify(payload)
      });

      const result = await response.json();

      if (response.ok) {
        // Update the table with the edited item
        setTableExample((prevItems) =>
          prevItems.map((item) =>
            item.id === editItemId
              ? {
                  ...item,
                  ...editFormData,
                  type: editFormData.category // For display in table
                }
              : item
          )
        );

        setShowEditModal(false);
      } else {
        alert(`Error: ${result.message || "Failed to update action item"}`);
      }
    } catch (error) {
      console.error("Error updating action item:", error);
      alert("Failed to update action item. See console for details.");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Update the filtering logic
  const filteredData = tableExample.filter((item) => {
    const searchLower = searchQuery.toLowerCase();

    // Search in various fields
    const nameMatch = item.name?.toLowerCase().includes(searchLower) || false;
    const idMatch = item.defaulter_id?.toString().toLowerCase().includes(searchLower) || false;
    const reasonMatch = item.action_reason?.toLowerCase().includes(searchLower) || false;
    const channelMatch = item.action_channel?.toLowerCase().includes(searchLower) || false;
    const typeMatch = (item.category || item.type || "")?.toLowerCase().includes(searchLower) || false;

    return nameMatch || idMatch || reasonMatch || channelMatch || typeMatch;
  });

  // Function to handle modal close
  const handleModalClose = () => {
    setShowCreateModal(false);
    setApiError("");
    setFormErrors({});
  };

  // Update the modal component for better UI/UX
  const renderCreateModal = () => (
    <CModal visible={showCreateModal} onClose={handleModalClose}>
      <CModalHeader>
        <CModalTitle>Create New Action Item</CModalTitle>
      </CModalHeader>
      <CModalBody>
        {/* API Error Display */}
        {apiError && (
          <div className="alert alert-danger mb-3" role="alert">
            <strong>Error:</strong> {apiError}
          </div>
        )}

        <CForm onSubmit={handleSubmit}>
          <div className="mb-3">
            <CFormLabel htmlFor="defaulter_id">Customer ID*</CFormLabel>
            <CFormInput
              type="text"
              id="defaulter_id"
              name="defaulter_id"
              value={formData.defaulter_id}
              onChange={handleFormChange}
              invalid={!!formErrors.defaulter_id}
              feedback={formErrors.defaulter_id}
              placeholder="Enter customer ID"
              required
            />
          </div>

          <div className="row mb-3">
            <div className="col-md-6">
              <CFormLabel htmlFor="action_date">Date*</CFormLabel>
              <CFormInput
                type="date"
                id="action_date"
                name="action_date"
                value={formData.action_date}
                onChange={handleFormChange}
                invalid={!!formErrors.action_date}
                feedback={formErrors.action_date}
                required
              />
            </div>
            <div className="col-md-6">
              <CFormLabel htmlFor="action_time">Time*</CFormLabel>
              <CFormInput
                type="time"
                id="action_time"
                name="action_time"
                step="1"
                value={formData.action_time}
                onChange={handleFormChange}
                invalid={!!formErrors.action_time}
                feedback={formErrors.action_time}
                required
              />
            </div>
          </div>

          <div className="row mb-3">
            <div className="col-md-6">
              <CFormLabel htmlFor="action_channel">Channel*</CFormLabel>
              <CFormSelect
                id="action_channel"
                name="action_channel"
                value={formData.action_channel}
                onChange={handleFormChange}
                invalid={!!formErrors.action_channel}
                feedback={formErrors.action_channel}
                required
              >
                <option value="call">Call</option>
                <option value="email">Email</option>
              </CFormSelect>
            </div>
            <div className="col-md-6">
              <CFormLabel htmlFor="category">Category</CFormLabel>
              <CFormSelect
                id="category"
                name="category"
                value={formData.category}
                onChange={handleFormChange}
              >
                <option value="outreach">Outreach</option>
                <option value="payment-reminder">Payment Reminder</option>
                <option value="overdue-followup">Overdue Followup</option>
                <option value="customer-requested">Customer Requested</option>
              </CFormSelect>
            </div>
          </div>

          <div className="mb-3">
            <CFormLabel htmlFor="action_reason">Reason*</CFormLabel>
            <CFormTextarea
              id="action_reason"
              name="action_reason"
              value={formData.action_reason}
              onChange={handleFormChange}
              invalid={!!formErrors.action_reason}
              feedback={formErrors.action_reason}
              rows="2"
              placeholder="Enter the reason for this action item"
              required
            />
          </div>

          {formData.action_channel === "email" && (
            <div className="mb-3">
              <CFormLabel htmlFor="action_channel_content">Email Content</CFormLabel>
              <CFormTextarea
                id="action_channel_content"
                name="action_channel_content"
                value={formData.action_channel_content}
                onChange={handleFormChange}
                rows="4"
                placeholder="Enter email content (optional)"
              />
            </div>
          )}


        </CForm>
      </CModalBody>
      <CModalFooter>
        <CButton color="secondary" onClick={handleModalClose}>
          Cancel
        </CButton>
        <CButton color="primary" onClick={handleSubmit} disabled={isSubmitting}>
          {isSubmitting ? "Creating..." : "Create Action Item"}
        </CButton>
      </CModalFooter>
    </CModal>
  );

  // Function to render the edit modal
  const renderEditModal = () => (
    <CModal visible={showEditModal} onClose={() => setShowEditModal(false)}>
      <CModalHeader>
        <CModalTitle>Edit Action Item</CModalTitle>
      </CModalHeader>
      <CModalBody>
        <CForm>
          <div className="row mb-3">
            <div className="col-md-6">
              <CFormLabel htmlFor="edit_action_date">Date*</CFormLabel>
              <CFormInput
                type="date"
                id="edit_action_date"
                name="action_date"
                value={editFormData.action_date}
                onChange={handleEditFormChange}
                invalid={!!formErrors.action_date}
                feedback={formErrors.action_date}
                required
              />
            </div>
            <div className="col-md-6">
              <CFormLabel htmlFor="edit_action_time">Time*</CFormLabel>
              <CFormInput
                type="time"
                id="edit_action_time"
                name="action_time"
                step="1"
                value={editFormData.action_time}
                onChange={handleEditFormChange}
                invalid={!!formErrors.action_time}
                feedback={formErrors.action_time}
                required
              />
            </div>
          </div>

          <div className="row mb-3">
            <div className="col-md-6">
              <CFormLabel htmlFor="edit_action_channel">Channel*</CFormLabel>
              <CFormSelect
                id="edit_action_channel"
                name="action_channel"
                value={editFormData.action_channel}
                onChange={handleEditFormChange}
                invalid={!!formErrors.action_channel}
                feedback={formErrors.action_channel}
                required
              >
                <option value="call">Call</option>
                <option value="email">Email</option>
              </CFormSelect>
            </div>
            <div className="col-md-6">
              <CFormLabel htmlFor="edit_category">Category</CFormLabel>
              <CFormSelect
                id="edit_category"
                name="category"
                value={editFormData.category}
                onChange={handleEditFormChange}
              >
                <option value="outreach">Outreach</option>
                <option value="payment-reminder">Payment Reminder</option>
                <option value="overdue-followup">Overdue Followup</option>
                <option value="customer-requested">Customer Requested</option>
              </CFormSelect>
            </div>
          </div>

          <div className="mb-3">
            <CFormLabel htmlFor="edit_action_reason">Reason*</CFormLabel>
            <CFormTextarea
              id="edit_action_reason"
              name="action_reason"
              value={editFormData.action_reason}
              onChange={handleEditFormChange}
              invalid={!!formErrors.action_reason}
              feedback={formErrors.action_reason}
              rows="2"
              placeholder="Enter the reason for this action item"
              required
            />
          </div>

          {editFormData.action_channel === "email" && (
            <div className="mb-3">
              <CFormLabel htmlFor="edit_action_channel_content">Email Content</CFormLabel>
              <CFormTextarea
                id="edit_action_channel_content"
                name="action_channel_content"
                value={editFormData.action_channel_content}
                onChange={handleEditFormChange}
                rows="4"
                placeholder="Enter email content (optional)"
              />
            </div>
          )}
        </CForm>
      </CModalBody>
      <CModalFooter>
        <CButton color="secondary" onClick={() => setShowEditModal(false)}>
          Cancel
        </CButton>
        <CButton color="primary" onClick={handleEditSubmit} disabled={isSubmitting}>
          {isSubmitting ? "Updating..." : "Update Action Item"}
        </CButton>
      </CModalFooter>
    </CModal>
  );

  // Function to render the view more modal
  const renderViewModal = () => {
    if (!currentItem) return null;

    return (
      <CModal visible={showViewModal} onClose={() => setShowViewModal(false)}>
        <CModalHeader>
          <CModalTitle>Action Item Details</CModalTitle>
        </CModalHeader>
        <CModalBody>
          <div className="mb-3">
            <strong>Customer: </strong>
            <span>{currentItem.name || 'Customer #' + currentItem.defaulter_id}</span>
          </div>
          <div className="mb-3">
            <strong>ID: </strong>
            <span>{currentItem.defaulter_id}</span>
          </div>
          <div className="mb-3">
            <strong>Channel: </strong>
            <span>{currentItem.action_channel}</span>
          </div>
          <div className="mb-3">
            <strong>Type: </strong>
            <span className={`badge rounded-pill bg-${getTypeColor(currentItem.category || currentItem.type)}`}>
              {(currentItem.category || currentItem.type || "").replace('-', ' ')}
            </span>
          </div>
          <div className="mb-3">
            <strong>Date & Time: </strong>
            <span>
              {currentItem.action_date && currentItem.action_time
                ? formatTimestamp(`${currentItem.action_date}T${currentItem.action_time}`)
                : `${currentItem.action_date || 'Not set'} ${currentItem.action_time || ''}`}
            </span>
          </div>
          <div className="mb-3">
            <strong>Reason: </strong>
            <p>{currentItem.action_reason}</p>
          </div>
          {currentItem.action_channel === "email" && currentItem.action_channel_content && (
            <div className="mb-3">
              <strong>Email Content: </strong>
              <p>{currentItem.action_channel_content}</p>
            </div>
          )}
        </CModalBody>
        <CModalFooter>
          <CButton color="primary" onClick={() => {
            setShowViewModal(false);
            handleEdit(currentItem);
          }}>
            Edit
          </CButton>
          <CButton color="danger" style={{ color: 'white' }} onClick={() => {
            // Remove nested confirmation to avoid double prompts
            handleDelete(currentItem.id);
            // setShowViewModal(false) will be called inside handleDelete if successful
          }}>
            Delete
          </CButton>
          <CButton color="secondary" onClick={() => setShowViewModal(false)}>
            Close
          </CButton>
        </CModalFooter>
      </CModal>
    );
  };

  // Function to render the success modal
  const renderSuccessModal = () => (
    <CModal visible={showSuccessModal} onClose={() => setShowSuccessModal(false)}>
      <CModalHeader>
        <CModalTitle>Success</CModalTitle>
      </CModalHeader>
      <CModalBody>
        <p>{successMessage}</p>
      </CModalBody>
      <CModalFooter>
        <CButton color="primary" onClick={() => setShowSuccessModal(false)}>
          OK
        </CButton>
      </CModalFooter>
    </CModal>
  );

  return (
    <div style={{ height: "500px", overflowY: "auto" }}>
      <div className="d-flex justify-content-between align-items-center mb-3">
        <input
          type="text"
          placeholder="Search by name"
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="form-control"
          style={{ flex: "1" }}
        />
        <div className="d-flex">
          <CButton
            color="secondary"
            className="ms-2"
            onClick={refreshData}
            disabled={isLoading}
            title="Refresh data"
          >
            <CIcon icon={cilReload} className={isLoading ? "icon-spin" : ""} />
          </CButton>
          <CButton
            color="primary"
            className="ms-2"
            onClick={() => setShowCreateModal(true)}
          >
            <CIcon icon={cilPlus} className="me-1" /> Create
          </CButton>
        </div>
      </div>

      <CTable align="middle" className="mb-0 mt-1 border" hover responsive>
        <CTableHead className="text-nowrap">
          <CTableRow>
            <CTableHeaderCell className="bg-body-tertiary text-center">
              <CIcon icon={cilPeople} />
            </CTableHeaderCell>
            <CTableHeaderCell className="bg-body-tertiary">User</CTableHeaderCell>
            <CTableHeaderCell className="bg-body-tertiary text-center">Channel</CTableHeaderCell>
            <CTableHeaderCell className="bg-body-tertiary">Scheduled At</CTableHeaderCell>
            <CTableHeaderCell className="bg-body-tertiary">Type</CTableHeaderCell>
            <CTableHeaderCell className="bg-body-tertiary text-center">Actions</CTableHeaderCell> {/* Changed column name */}
          </CTableRow>
        </CTableHead>
        <CTableBody>
          {filteredData.map((item, index) => (
            <CTableRow key={index}>
              <CTableDataCell className="text-center">
                <CAvatar size="md" src={UserIcon} />
              </CTableDataCell>
              <CTableDataCell>
                <div>{item.name || 'Customer #' + item.defaulter_id}</div>
                <div className="small text-body-secondary text-nowrap">
                  {item.defaulter_id && `ID: ${item.defaulter_id}`}
                </div>
              </CTableDataCell>
              <CTableDataCell className="text-center">
                <div className="d-flex align-items-center justify-content-center">
                  <img src={communicationIcons[item.action_channel]} alt={item.action_channel} className="me-2" style={{ width: "24px", height: "24px" }} />
                </div>
              </CTableDataCell>
              <CTableDataCell>
                <div className="fw-semibold text-nowrap">
                  {item.action_date && item.action_time
                    ? formatTimestamp(`${item.action_date}T${item.action_time}`)
                    : item.action_date || 'Not scheduled'}
                </div>
              </CTableDataCell>
              <CTableDataCell>
                {item.category || item.type ? (
                  <div className={`badge rounded-pill bg-${getTypeColor(item.category || item.type)}`} style={{padding: '6px 10px', color: 'white', textTransform: 'capitalize', fontSize: '0.85rem', fontWeight: 'bold', display: 'inline-block'}}>
                    {(item.category || item.type || "").replace('-', ' ')}
                  </div>
                ) : (
                  <div className="badge rounded-pill bg-secondary" style={{padding: '6px 10px', color: 'white', fontSize: '0.85rem', fontWeight: 'bold'}}>
                    unknown
                  </div>
                )}
              </CTableDataCell>
              <CTableDataCell className="text-center">
                <CButton
                  color="primary"
                  variant="ghost"
                  onClick={() => viewMore(item)}
                >
                  <CIcon icon={cilOptions} />
                </CButton>
              </CTableDataCell>
            </CTableRow>
          ))}
        </CTableBody>
      </CTable>

      <div ref={observerRef} style={{ height: "50px", display: hasMore ? "block" : "none" }}>
        {isLoading && <p>Loading more data...</p>}
      </div>

      {/* Render modals */}
      {renderEditModal()}
      {renderCreateModal()}
      {renderViewModal()}
      {renderSuccessModal()}
    </div>
  );
};

export default ActionItemsTable
