// src/timezoneContext.js
import { createContext, useContext, useState, useEffect } from 'react';

// Create a context for timezone
const TimezoneContext = createContext();

// Default timezone is UTC
const DEFAULT_TIMEZONE = 'UTC';

// List of common timezones for the dropdown
export const COMMON_TIMEZONES = [
  { value: 'UTC', label: 'UTC (Coordinated Universal Time)' },
  { value: 'America/New_York', label: 'Eastern Time (ET)' },
  { value: 'America/Chicago', label: 'Central Time (CT)' },
  { value: 'America/Denver', label: 'Mountain Time (MT)' },
  { value: 'America/Los_Angeles', label: 'Pacific Time (PT)' },
  { value: 'America/Anchorage', label: 'Alaska Time' },
  { value: 'Pacific/Honolulu', label: 'Hawaii Time' },
  { value: 'Europe/London', label: 'London (GMT/BST)' },
  { value: 'Europe/Paris', label: 'Central European Time (CET)' },
  { value: 'Europe/Moscow', label: 'Moscow Time' },
  { value: 'Asia/Tokyo', label: 'Japan Time' },
  { value: 'Asia/Shanghai', label: 'China Time' },
  { value: 'Asia/Kolkata', label: 'India Time' },
  { value: 'Australia/Sydney', label: 'Sydney Time' },
];

export const TimezoneProvider = ({ children }) => {
  // Initialize timezone from localStorage or use default
  const [timezone, setTimezone] = useState(() => {
    const savedTimezone = localStorage.getItem('timezone');
    return savedTimezone || DEFAULT_TIMEZONE;
  });

  // Save timezone to localStorage whenever it changes
  useEffect(() => {
    localStorage.setItem('timezone', timezone);
  }, [timezone]);

  // Function to update the timezone
  const updateTimezone = (newTimezone) => {
    setTimezone(newTimezone);
  };

  return (
    <TimezoneContext.Provider value={{ timezone, updateTimezone }}>
      {children}
    </TimezoneContext.Provider>
  );
};

// Custom hook to use the timezone context
export const useTimezone = () => {
  const context = useContext(TimezoneContext);
  if (!context) {
    throw new Error('useTimezone must be used within a TimezoneProvider');
  }
  return context;
};
