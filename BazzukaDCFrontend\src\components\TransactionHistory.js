import React, { useState, useEffect } from 'react';
import {
  <PERSON>ard,
  <PERSON>ard<PERSON>ody,
  CCardHeader,
  CTable,
  CTableHead,
  CTableRow,
  CTableHeaderCell,
  CTableBody,
  CTableDataCell,
  CBadge,
  CButton,
  CInputGroup,
  CFormInput,
  CFormSelect,
  CRow,
  CCol,
  CPagination,
  CPaginationItem,
  CSpinner,
  CAlert,
  CModal,
  CModalHeader,
  CModalTitle,
  CModalBody,
  CModalFooter
} from '@coreui/react';
import { getApiUrl } from '../utils/apiConfig'
// Icons replaced with emojis to avoid import issues

const TransactionHistory = () => {
  const [transactions, setTransactions] = useState([]);
  const [summary, setSummary] = useState({
    total_collected: 0,
    total_transactions: 0,
    pending_amount: 0,
    pending_transactions: 0,
    average_transaction: 0
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [validationErrors, setValidationErrors] = useState({});
  const [filters, setFilters] = useState({
    search: '',
    start_date: '',
    end_date: ''
  });
  const [pagination, setPagination] = useState({
    offset: 0,
    limit: 25,
    total_count: 0,
    has_more: false
  });
  const [selectedTransaction, setSelectedTransaction] = useState(null);
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [showStatusModal, setShowStatusModal] = useState(false);
  const [statusUpdate, setStatusUpdate] = useState({ status: '' });

  // Fetch transactions
  const fetchTransactions = async (resetPagination = false) => {
    setLoading(true);
    setError(null);
    try {
      const offset = resetPagination ? 0 : pagination.offset;
      const params = new URLSearchParams({
        ...filters,
        limit: pagination.limit,
        offset: offset
        // Backend defaults to 'paid,approved' status automatically
      });

      // Remove empty parameters
      Object.keys(filters).forEach(key => {
        if (!filters[key]) params.delete(key);
      });

      console.log('Fetching transactions with URL:', getApiUrl(`/v0/transactions?${params}`));

      const response = await fetch(getApiUrl(`/v0/transactions?${params}`), {
        headers: {
          'ngrok-skip-browser-warning': 'true',
          'Content-Type': 'application/json'
        }
      });

      console.log('Response status:', response.status);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('API Error:', errorText);
        throw new Error(`Failed to fetch transactions: ${response.status} ${errorText}`);
      }

      const data = await response.json();
      console.log('Received data:', data);

      if (resetPagination) {
        setTransactions(data.transactions || []);
        setPagination({
          ...pagination,
          offset: 0,
          total_count: data.total_count || 0,
          has_more: data.has_more || false
        });
      } else {
        setTransactions(prev => [...prev, ...(data.transactions || [])]);
        setPagination({
          ...pagination,
          offset: offset + pagination.limit,
          total_count: data.total_count || 0,
          has_more: data.has_more || false
        });
      }
    } catch (error) {
      console.error('Error fetching transactions:', error);
      setError(`Failed to load transactions: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  // Fetch summary statistics
  const fetchSummary = async () => {
    try {
      const params = new URLSearchParams();
      if (filters.start_date) params.append('start_date', filters.start_date);
      if (filters.end_date) params.append('end_date', filters.end_date);

      console.log('Fetching summary with URL:', getApiUrl(`/v0/transactions/summary?${params}`));

      const response = await fetch(getApiUrl(`/v0/transactions/summary?${params}`), {
        headers: {
          'ngrok-skip-browser-warning': 'true',
          'Content-Type': 'application/json'
        }
      });

      console.log('Summary response status:', response.status);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Summary API Error:', errorText);
        throw new Error(`Failed to fetch summary: ${response.status} ${errorText}`);
      }

      const data = await response.json();
      console.log('Summary data:', data);
      setSummary(data);
    } catch (error) {
      console.error('Error fetching summary:', error);
      // Set default summary to prevent UI issues
      setSummary({
        total_collected: 0,
        total_transactions: 0,
        pending_amount: 0,
        pending_transactions: 0,
        average_transaction: 0
      });
    }
  };

  useEffect(() => {
    // Add a small delay to ensure component is mounted
    const timer = setTimeout(() => {
      fetchTransactions(true);
      fetchSummary();
    }, 100);

    return () => clearTimeout(timer);
  }, []); // Remove filters dependency to prevent infinite loop

  const validateFilters = () => {
    const errors = {};

    // Validate date range if both dates are provided
    if (filters.start_date && filters.end_date) {
      const startDate = new Date(filters.start_date);
      const endDate = new Date(filters.end_date);
      if (startDate > endDate) {
        errors.dateRange = 'Start date must be before or equal to end date';
      }
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleFilterChange = (key, value) => {
    const newFilters = { ...filters, [key]: value };
    setFilters(newFilters);
    // Clear validation errors when user makes changes
    setValidationErrors({});
    // Trigger search with new filters immediately (skip validation for auto-triggers)
    setTimeout(() => fetchTransactions(true), 50);
  };

  const handleSearch = () => {
    if (validateFilters()) {
      fetchTransactions(true);
    }
  };

  const clearFilters = () => {
    setFilters({
      search: '',
      start_date: '',
      end_date: ''
    });
    setValidationErrors({});
    setTimeout(() => fetchTransactions(true), 50);
  };

  const loadMore = () => {
    if (!loading && pagination.has_more) {
      fetchTransactions(false);
    }
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount / 100);
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStatusBadge = (status) => {
    const statusConfig = {
      paid: { color: 'success', text: 'Paid' },
      pending: { color: 'warning', text: 'Pending' },
      failed: { color: 'danger', text: 'Failed' },
      refunded: { color: 'info', text: 'Refunded' }
    };

    const config = statusConfig[status] || { color: 'secondary', text: status };
    return <CBadge color={config.color}>{config.text}</CBadge>;
  };

  const handleViewDetails = async (transaction) => {
    try {
      // Fetch detailed transaction information
      const response = await fetch(getApiUrl(`/v0/transactions/${transaction.id}/details`), {
        headers: {
          'ngrok-skip-browser-warning': 'true',
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        setSelectedTransaction(data.transaction);
      } else {
        setSelectedTransaction(transaction);
      }
    } catch (error) {
      console.error('Error fetching transaction details:', error);
      setSelectedTransaction(transaction);
    }
    setShowDetailModal(true);
  };

  const handleStatusUpdate = (transaction) => {
    setSelectedTransaction(transaction);
    setStatusUpdate({ status: transaction.status });
    setShowStatusModal(true);
  };

  const updateTransactionStatus = async () => {
    try {
      const response = await fetch(getApiUrl(`/v0/transactions/${selectedTransaction.id}/status`), {
        method: 'PUT',
        headers: {
          'ngrok-skip-browser-warning': 'true',
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ status: statusUpdate.status })
      });

      if (response.ok) {
        setShowStatusModal(false);
        fetchTransactions(true); // Refresh the list
        alert('Transaction status updated successfully');
      } else {
        const error = await response.json();
        alert(`Error: ${error.error || 'Failed to update status'}`);
      }
    } catch (error) {
      console.error('Error updating transaction status:', error);
      alert('Error updating transaction status');
    }
  };

  const exportTransactions = async () => {
    try {
      const params = new URLSearchParams({
        ...filters,
        limit: 1000, // Export more records
        offset: 0
      });

      const response = await fetch(getApiUrl(`/v0/transactions?${params}`), {
        headers: {
          'ngrok-skip-browser-warning': 'true',
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) throw new Error('Failed to export transactions');

      const data = await response.json();

      // Convert to CSV
      const csvContent = [
        ['Date', 'Transaction ID', 'Debtor Name', 'Amount', 'Status', 'Email', 'Phone'].join(','),
        ...data.transactions.map(t => [
          formatDate(t.created_at),
          t.id,
          t.issues?.defaulters?.name || 'N/A',
          formatCurrency(t.amount),
          t.status,
          t.issues?.defaulters?.email || 'N/A',
          t.issues?.defaulters?.phone || 'N/A'
        ].join(','))
      ].join('\n');

      // Download CSV
      const blob = new Blob([csvContent], { type: 'text/csv' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `transactions_${new Date().toISOString().split('T')[0]}.csv`;
      a.click();
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Error exporting transactions:', error);
    }
  };

  console.log('TransactionHistory component rendering...');
  console.log('Current state:', { transactions, summary, loading, filters, error });

  // Add error boundary fallback
  if (error && error.includes('Failed to load')) {
    return (
      <div>
        <CCard>
          <CCardHeader>
            <h4>Transaction History</h4>
          </CCardHeader>
          <CCardBody>
            <CAlert color="danger">
              <h6>Unable to load transaction data</h6>
              <p>{error}</p>
              <CButton color="primary" onClick={() => window.location.reload()}>
                Reload Page
              </CButton>
            </CAlert>
          </CCardBody>
        </CCard>
      </div>
    );
  }

  return (
    <div>
      {/* Summary Cards */}
      <CRow className="mb-4">
        <CCol sm={6} lg={3}>
          <CCard className="text-center">
            <CCardBody>
              <div style={{ fontSize: '3rem' }} className="text-success mb-2">💰</div>
              <h4 className="text-success">{formatCurrency(summary.total_collected || 0)}</h4>
              <p className="text-muted mb-0">Total Collected</p>
            </CCardBody>
          </CCard>
        </CCol>
        <CCol sm={6} lg={3}>
          <CCard className="text-center">
            <CCardBody>
              <div style={{ fontSize: '3rem' }} className="text-warning mb-2">⏳</div>
              <h4 className="text-warning">{formatCurrency(summary.pending_amount || 0)}</h4>
              <p className="text-muted mb-0">In-Progress Amount</p>
            </CCardBody>
          </CCard>
        </CCol>
        <CCol sm={6} lg={3}>
          <CCard className="text-center">
            <CCardBody>
              <div style={{ fontSize: '3rem' }} className="text-info mb-2">📊</div>
              <h4 className="text-info">{summary.total_transactions || 0}</h4>
              <p className="text-muted mb-0">Total Transactions</p>
            </CCardBody>
          </CCard>
        </CCol>
        <CCol sm={6} lg={3}>
          <CCard className="text-center">
            <CCardBody>
              <div style={{ fontSize: '3rem' }} className="text-primary mb-2">💵</div>
              <h4 className="text-primary">{formatCurrency(summary.average_transaction || 0)}</h4>
              <p className="text-muted mb-0">Average Payment</p>
            </CCardBody>
          </CCard>
        </CCol>
      </CRow>

      {/* Simple Filters Section */}
      <CCard className="mb-4">
        <CCardHeader className="d-flex justify-content-between align-items-center">
          <h5 className="mb-0">Transaction History - Paid & In-Progress Payments</h5>
          <CButton color="success" size="sm" onClick={exportTransactions}>
            📊 Export
          </CButton>
        </CCardHeader>
        <CCardBody>
          {/* Validation Errors */}
          {Object.keys(validationErrors).length > 0 && (
            <CAlert color="danger" className="mb-3">
              <strong>Please fix the following errors:</strong>
              <ul className="mb-0 mt-2">
                {Object.entries(validationErrors).map(([key, message]) => (
                  <li key={key}>{message}</li>
                ))}
              </ul>
            </CAlert>
          )}

          {/* Simple Search and Date Filters */}
          <CRow className="mb-3">
            <CCol md={4}>
              <label className="form-label">Search</label>
              <CInputGroup>
                <CFormInput
                  placeholder="Search by debtor name, email, phone, or transaction ID..."
                  value={filters.search}
                  onChange={(e) => handleFilterChange('search', e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                />
                <CButton color="primary" onClick={handleSearch}>
                  🔍
                </CButton>
              </CInputGroup>
            </CCol>
            <CCol md={3}>
              <label className="form-label">Start Date</label>
              <CFormInput
                type="date"
                value={filters.start_date}
                onChange={(e) => handleFilterChange('start_date', e.target.value)}
                className={validationErrors.dateRange ? 'is-invalid' : ''}
              />
            </CCol>
            <CCol md={3}>
              <label className="form-label">End Date</label>
              <CFormInput
                type="date"
                value={filters.end_date}
                onChange={(e) => handleFilterChange('end_date', e.target.value)}
                className={validationErrors.dateRange ? 'is-invalid' : ''}
              />
              {validationErrors.dateRange && (
                <div className="invalid-feedback">
                  {validationErrors.dateRange}
                </div>
              )}
            </CCol>
            <CCol md={2}>
              <label className="form-label">Actions</label>
              <div className="d-flex gap-2">
                <CButton
                  color="secondary"
                  size="sm"
                  variant="outline"
                  onClick={clearFilters}
                  className="w-100"
                >
                  🗑️ Clear
                </CButton>
              </div>
            </CCol>
          </CRow>
        </CCardBody>
      </CCard>

      {/* Error Display */}
      {error && (
        <CAlert color="danger" className="mb-4">
          {error}
        </CAlert>
      )}

      {/* Transactions Table */}
      <CCard>
        <CCardBody>
          {loading && transactions.length === 0 ? (
            <div className="text-center py-4">
              <CSpinner color="primary" />
              <p className="mt-2">Loading transactions...</p>
            </div>
          ) : error ? (
            <CAlert color="danger">
              {error}
            </CAlert>
          ) : transactions.length === 0 ? (
            <CAlert color="info">
              No transactions found matching your criteria.
            </CAlert>
          ) : (
            <>
              <CTable hover responsive>
                <CTableHead>
                  <CTableRow>
                    <CTableHeaderCell>Date</CTableHeaderCell>
                    <CTableHeaderCell>Payment ID</CTableHeaderCell>
                    <CTableHeaderCell>Transaction ID</CTableHeaderCell>
                    <CTableHeaderCell>Debtor</CTableHeaderCell>
                    <CTableHeaderCell>Amount</CTableHeaderCell>
                    <CTableHeaderCell>Status</CTableHeaderCell>
                    <CTableHeaderCell>Type</CTableHeaderCell>
                    <CTableHeaderCell>Approval</CTableHeaderCell>
                    <CTableHeaderCell>Actions</CTableHeaderCell>
                  </CTableRow>
                </CTableHead>
                <CTableBody>
                  {transactions.map((transaction) => (
                    <CTableRow key={transaction.id}>
                      <CTableDataCell>
                        {formatDate(transaction.created_at)}
                      </CTableDataCell>
                      <CTableDataCell>
                        <code>{transaction.id.slice(0, 8)}...</code>
                      </CTableDataCell>
                      <CTableDataCell>
                        <code>{transaction.transaction_id ? transaction.transaction_id.slice(0, 8) + '...' : 'N/A'}</code>
                      </CTableDataCell>
                      <CTableDataCell>
                        <div>
                          <strong>{transaction.issues?.defaulters?.name || 'N/A'}</strong>
                          <br />
                          <small className="text-muted">
                            {transaction.issues?.defaulters?.email || 'No email'}
                          </small>
                        </div>
                      </CTableDataCell>
                      <CTableDataCell>
                        <strong>{formatCurrency(transaction.amount)}</strong>
                      </CTableDataCell>
                      <CTableDataCell>
                        {getStatusBadge(transaction.status)}
                      </CTableDataCell>
                      <CTableDataCell>
                        <CBadge color={transaction.recurring ? 'info' : 'secondary'}>
                          {transaction.recurring ? 'Recurring' : 'One-time'}
                        </CBadge>
                        {transaction.settlement_discount && (
                          <CBadge color="success" className="ms-1">
                            Discounted
                          </CBadge>
                        )}
                      </CTableDataCell>
                      <CTableDataCell>
                        <CBadge color={
                          transaction._approved === 'approved' ? 'success' :
                          transaction._approved === 'pending' ? 'warning' :
                          transaction._approved === 'rejected' ? 'danger' : 'secondary'
                        }>
                          {transaction._approved || 'Unknown'}
                        </CBadge>
                      </CTableDataCell>
                      <CTableDataCell>
                        <div className="d-flex gap-1">
                          <CButton
                            color="info"
                            size="sm"
                            onClick={() => handleViewDetails(transaction)}
                            title="View Details"
                          >
                            👁️
                          </CButton>
                          <CButton
                            color="warning"
                            size="sm"
                            onClick={() => handleStatusUpdate(transaction)}
                            title="Update Status"
                          >
                            ✏️
                          </CButton>
                        </div>
                      </CTableDataCell>
                    </CTableRow>
                  ))}
                </CTableBody>
              </CTable>

              {/* Load More Button */}
              {pagination.has_more && (
                <div className="text-center mt-3">
                  <CButton
                    color="primary"
                    onClick={loadMore}
                    disabled={loading}
                  >
                    {loading ? (
                      <>
                        <CSpinner size="sm" className="me-2" />
                        Loading...
                      </>
                    ) : (
                      'Load More'
                    )}
                  </CButton>
                </div>
              )}

              {/* Pagination Info */}
              <div className="text-center mt-3 text-muted">
                Showing {transactions.length} of {pagination.total_count} transactions
              </div>
            </>
          )}
        </CCardBody>
      </CCard>

      {/* Transaction Detail Modal */}
      <CModal visible={showDetailModal} onClose={() => setShowDetailModal(false)} size="lg">
        <CModalHeader>
          <CModalTitle>Transaction Details</CModalTitle>
        </CModalHeader>
        <CModalBody>
          {selectedTransaction && (
            <div>
              <CRow className="mb-3">
                <CCol sm={6}>
                  <strong>Payment Link ID:</strong>
                  <br />
                  <code>{selectedTransaction.id}</code>
                </CCol>
                <CCol sm={6}>
                  <strong>External Transaction ID:</strong>
                  <br />
                  <code>{selectedTransaction.transaction_id || 'N/A'}</code>
                </CCol>
              </CRow>

              <CRow className="mb-3">
                <CCol sm={6}>
                  <strong>Created Date:</strong>
                  <br />
                  {formatDate(selectedTransaction.created_at)}
                </CCol>
                <CCol sm={6}>
                  <strong>Payment Link ID:</strong>
                  <br />
                  <code>{selectedTransaction.id}</code>
                </CCol>
              </CRow>

              <CRow className="mb-3">
                <CCol sm={6}>
                  <strong>Amount:</strong>
                  <br />
                  <span className="h5 text-success">
                    {formatCurrency(selectedTransaction.amount)}
                  </span>
                </CCol>
                <CCol sm={6}>
                  <strong>Status:</strong>
                  <br />
                  {getStatusBadge(selectedTransaction.status)}
                </CCol>
              </CRow>

              <CRow className="mb-3">
                <CCol sm={6}>
                  <strong>Payment Type:</strong>
                  <br />
                  <CBadge color={selectedTransaction.recurring ? 'info' : 'secondary'}>
                    {selectedTransaction.recurring ? 'Recurring' : 'One-time'}
                  </CBadge>
                  {selectedTransaction.settlement_discount && (
                    <CBadge color="success" className="ms-1">
                      Settlement Discount
                    </CBadge>
                  )}
                </CCol>
                <CCol sm={6}>
                  <strong>Approval Status:</strong>
                  <br />
                  <CBadge color={
                    selectedTransaction._approved === 'approved' ? 'success' :
                    selectedTransaction._approved === 'pending' ? 'warning' :
                    selectedTransaction._approved === 'rejected' ? 'danger' : 'secondary'
                  }>
                    {selectedTransaction._approved || 'Unknown'}
                  </CBadge>
                </CCol>
              </CRow>

              <CRow className="mb-3">
                <CCol sm={6}>
                  <strong>Issue ID:</strong>
                  <br />
                  {selectedTransaction.issue_id || selectedTransaction.issues?.issue_id || 'N/A'}
                </CCol>
                <CCol sm={6}>
                  <strong>Issue Status:</strong>
                  <br />
                  <CBadge color={
                    selectedTransaction.issues?.status === 'solved' ? 'success' :
                    selectedTransaction.issues?.status === 'in-progress' ? 'warning' :
                    'danger'
                  }>
                    {selectedTransaction.issues?.status || 'Unknown'}
                  </CBadge>
                </CCol>
              </CRow>

              <CRow className="mb-3">
                <CCol sm={6}>
                  <strong>Outstanding Amount:</strong>
                  <br />
                  {selectedTransaction.issues?.outstanding_amount ?
                    formatCurrency(selectedTransaction.issues.outstanding_amount) : 'N/A'}
                </CCol>
                <CCol sm={6}>
                  <strong>Defaulter ID:</strong>
                  <br />
                  {selectedTransaction.issues?.defaulter_id || 'N/A'}
                </CCol>
              </CRow>

              <hr />

              <h6>Debtor Information</h6>
              <CRow className="mb-3">
                <CCol sm={6}>
                  <strong>Name:</strong>
                  <br />
                  {selectedTransaction.issues?.defaulters?.name || 'N/A'}
                </CCol>
                <CCol sm={6}>
                  <strong>Email:</strong>
                  <br />
                  {selectedTransaction.issues?.defaulters?.email || 'N/A'}
                </CCol>
              </CRow>

              <CRow className="mb-3">
                <CCol sm={6}>
                  <strong>Phone:</strong>
                  <br />
                  {selectedTransaction.issues?.defaulters?.phone || 'N/A'}
                </CCol>
                <CCol sm={6}>
                  <strong>Debtor ID:</strong>
                  <br />
                  {selectedTransaction.issues?.defaulter_id || 'N/A'}
                </CCol>
              </CRow>

              {selectedTransaction.payment_method && (
                <>
                  <hr />
                  <h6>Payment Method</h6>
                  <p>{selectedTransaction.payment_method}</p>
                </>
              )}


            </div>
          )}
        </CModalBody>
        <CModalFooter>
          <CButton color="secondary" onClick={() => setShowDetailModal(false)}>
            Close
          </CButton>
        </CModalFooter>
      </CModal>

      {/* Status Update Modal */}
      <CModal visible={showStatusModal} onClose={() => setShowStatusModal(false)}>
        <CModalHeader>
          <CModalTitle>Update Transaction Status</CModalTitle>
        </CModalHeader>
        <CModalBody>
          {selectedTransaction && (
            <div>
              <CRow className="mb-3">
                <CCol>
                  <strong>Transaction:</strong> {selectedTransaction.id}
                  <br />
                  <strong>Current Status:</strong> {getStatusBadge(selectedTransaction.status)}
                </CCol>
              </CRow>

              <CRow className="mb-3">
                <CCol>
                  <label htmlFor="status-select" className="form-label">
                    <strong>New Status:</strong>
                  </label>
                  <CFormSelect
                    id="status-select"
                    value={statusUpdate.status}
                    onChange={(e) => setStatusUpdate({...statusUpdate, status: e.target.value})}
                  >
                    <option value="pending">Pending</option>
                    <option value="paid">Paid</option>
                    <option value="failed">Failed</option>
                    <option value="refunded">Refunded</option>
                    <option value="cancelled">Cancelled</option>
                  </CFormSelect>
                </CCol>
              </CRow>


            </div>
          )}
        </CModalBody>
        <CModalFooter>
          <CButton color="secondary" onClick={() => setShowStatusModal(false)}>
            Cancel
          </CButton>
          <CButton color="primary" onClick={updateTransactionStatus}>
            Update Status
          </CButton>
        </CModalFooter>
      </CModal>
    </div>
  );
};

export default TransactionHistory;
