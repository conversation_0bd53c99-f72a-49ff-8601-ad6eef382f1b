"""
Test the communication preferences prompt with multiple model configurations
to identify false positives across different settings.
"""

import os
import json
import openai
from typing import List, Dict, Any

# Set up OpenAI client with API key
client = openai.OpenAI(api_key="********************************************************")

# The current prompt from the agent
COMM_PREFS_PROMPT = """Analyze the conversation transcript between a debt collector and a debtor to identify any explicit requests related to communication preferences.

**CRITICAL: Only call tools for EXPLICIT, DIRECT requests. Do NOT infer, assume, or interpret implicit preferences. When in doubt, DO NOT call any tools.**

- If—and only if—the debtor **explicitly** asks to stop receiving messages entirely through a specific channel (e.g., call, email, text), call the `opt_out_of_communications` tool with the appropriate channel.
    - You must always respect and act on any clear opt-out request.  
    - Example phrases include: "Please stop calling me," "Don't email me anymore," or "I no longer want to receive texts."
- If the debtor **explicitly** specifies limits on when they can be contacted (e.g., only during certain hours, days of the week, or not on holidays), call the `restrict_communications` tool with the relevant restrictions.  
    - Example phrases include: "Only call me after 5pm," "Do not contact me on weekends," or "Never call me at work."
- If you are given existing restrictions, you should append new restrictions before passing them into the `restrict_communications` tool.
- If the debtor requests not to be contacted at work, assume working hours are Monday to Friday, 9 AM to 5 PM, by default unless otherwise specified.
- **Never infer or assume a preference if the debtor does not directly state it.** For example, simply reaching voicemail, not answering a call, or automated greetings **do NOT** constitute a request or preference.
- Only act if the debtor makes a clear and affirmative request related to communication channels or timing.

**Key principle: If the debtor does not use explicit opt-out language or specific restriction requests, DO NOT call any tools.**

## Output Format
There should be no output, only tool calls if necessary."""

# Tool definitions
TOOLS = [
    {
        "type": "function",
        "function": {
            "name": "opt_out_of_communications",
            "description": "Opt a defaulter out entirely of specific communication channels.",
            "parameters": {
                "type": "object",
                "properties": {
                    "defaulter_id": {"type": "string"},
                    "channels": {"type": "array", "items": {"type": "string"}}
                },
                "required": ["defaulter_id", "channels"]
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "restrict_communications",
            "description": "Restrict a defaulter's communication by day of the week, time of the day, and/or holidays.",
            "parameters": {
                "type": "object",
                "properties": {
                    "defaulter_id": {"type": "string"},
                    "description": {"type": "string"}
                },
                "required": ["defaulter_id", "description"]
            }
        }
    }
]

# Test cases that should NOT trigger tool calls
FALSE_POSITIVE_TEST_CASES = [
    {
        'name': 'Debby says she doesnt want to talk but continues',
        'conversation': "Collector: Hi Debby, this is about your account.\nDebby: I really don't want to talk about this right now.\nCollector: I understand, but we need to resolve this.\nDebby: Okay fine, what do I owe?"
    },
    {
        'name': 'Debby mentions work hours but no restriction request',
        'conversation': "Collector: Hi Debby, calling about your balance.\nDebby: I'm at work right now. Can we make this quick?\nCollector: Of course. Let's discuss payment options.\nDebby: What are my choices?"
    },
    {
        'name': 'Debby says stop but then continues talking',
        'conversation': "Collector: Hi Debby, about your account.\nDebby: Stop, stop. I'm stressed about this.\nCollector: I understand this is stressful.\nDebby: Yeah, but tell me what I need to do."
    },
    {
        'name': 'Debby mentions not wanting calls but in different context',
        'conversation': "Collector: Hi Debby, calling about your debt.\nDebby: I don't want these kinds of problems in my life.\nCollector: I understand. Let's work together to resolve this.\nDebby: Okay, what can we do?"
    },
    {
        'name': 'Debby asks to call back later but continues',
        'conversation': "Collector: Hi Debby, about your account.\nDebby: Can you call me back later? Actually, no wait, let's just get this over with now.\nCollector: Sure, let's discuss it.\nDebby: What do I owe?"
    },
    {
        'name': 'Debby mentions being at work multiple times',
        'conversation': "Collector: Hi Debby.\nDebby: I'm at work. This is my workplace. I'm working right now.\nCollector: I understand you're busy.\nDebby: Yeah, but let's talk quickly."
    },
    {
        'name': 'Debby being very rude and hostile',
        'conversation': "Collector: Hi Debby, about your account.\nDebby: I hate you people! You're ruining my life! I don't want to deal with this!\nCollector: I understand you're upset.\nDebby: Just tell me what I have to pay."
    },
    {
        'name': 'Debby mentions not wanting to be bothered',
        'conversation': "Collector: Hi Debby.\nDebby: I don't want to be bothered with this stuff right now.\nCollector: I understand, but we need to discuss this.\nDebby: Fine, what's the minimum I can pay?"
    }
]

# Test configurations to try
TEST_CONFIGS = [
    {"model": "gpt-4.1-mini", "temperature": 0.0},
    {"model": "gpt-4.1-mini", "temperature": 0.3},
    {"model": "gpt-4.1-mini", "temperature": 0.7},
    {"model": "gpt-4.1", "temperature": 0.0},
    {"model": "gpt-4.1", "temperature": 0.3},
    {"model": "gpt-4o-mini", "temperature": 0.0},
    {"model": "gpt-4o-mini", "temperature": 0.3},
]


def test_single_case(test_case: Dict[str, Any], model: str, temperature: float) -> Dict[str, Any]:
    """Test a single case with specific model configuration."""
    result = {
        'name': test_case['name'],
        'conversation': test_case['conversation'],
        'model': model,
        'temperature': temperature,
        'passed': False,
        'tools_called': [],
        'error': None,
        'response_content': None
    }
    
    try:
        # Create the user message
        user_message = f"""Here was the most recent communication transcript between the collector and the debtor: 

- **Defaulter ID**: test_123
- **Recent Conversation**: {test_case['conversation']}"""
        
        # Make the API call
        response = client.chat.completions.create(
            model=model,
            messages=[
                {"role": "system", "content": COMM_PREFS_PROMPT},
                {"role": "user", "content": user_message}
            ],
            tools=TOOLS,
            temperature=temperature
        )
        
        # Check the response
        message = response.choices[0].message
        result['response_content'] = message.content
        
        # Check if any tools were called
        if message.tool_calls:
            for tool_call in message.tool_calls:
                result['tools_called'].append({
                    'tool': tool_call.function.name,
                    'arguments': tool_call.function.arguments
                })
        
        # Test passes if NO tools were called
        result['passed'] = len(result['tools_called']) == 0
        
    except Exception as e:
        result['error'] = str(e)
        result['passed'] = False
        
    return result


def run_comprehensive_test():
    """Run tests across all configurations to find false positives."""
    all_results = []
    false_positives = []
    
    print("Running comprehensive false positive detection...")
    print(f"Testing {len(TEST_CONFIGS)} configurations with {len(FALSE_POSITIVE_TEST_CASES)} test cases each")
    print("="*80)
    
    for config in TEST_CONFIGS:
        model = config['model']
        temperature = config['temperature']
        
        print(f"\nTesting {model} (temp: {temperature})")
        print("-" * 50)
        
        config_results = []
        config_false_positives = 0
        
        for test_case in FALSE_POSITIVE_TEST_CASES:
            result = test_single_case(test_case, model, temperature)
            config_results.append(result)
            all_results.append(result)
            
            if not result['passed']:
                config_false_positives += 1
                false_positives.append(result)
                print(f"❌ {test_case['name']}: Tools called - {[t['tool'] for t in result['tools_called']]}")
            else:
                print(f"✅ {test_case['name']}: No tools called")
        
        success_rate = ((len(FALSE_POSITIVE_TEST_CASES) - config_false_positives) / len(FALSE_POSITIVE_TEST_CASES)) * 100
        print(f"Config success rate: {success_rate:.1f}% ({config_false_positives} false positives)")
    
    # Print overall summary
    print("\n" + "="*80)
    print("COMPREHENSIVE TEST SUMMARY")
    print("="*80)
    
    total_tests = len(all_results)
    total_passed = sum(1 for r in all_results if r['passed'])
    total_false_positives = total_tests - total_passed
    
    print(f"Total tests run: {total_tests}")
    print(f"Total passed: {total_passed}")
    print(f"Total false positives: {total_false_positives}")
    print(f"Overall success rate: {(total_passed/total_tests)*100:.1f}%")
    
    if false_positives:
        print(f"\nFALSE POSITIVE BREAKDOWN:")
        for fp in false_positives:
            print(f"- {fp['name']} ({fp['model']}, temp: {fp['temperature']})")
            for tool_call in fp['tools_called']:
                print(f"  Tool: {tool_call['tool']}")
    
    # Save detailed results
    with open('comprehensive_false_positive_results.json', 'w') as f:
        json.dump(all_results, f, indent=2)
    
    print(f"\nDetailed results saved to 'comprehensive_false_positive_results.json'")
    
    return false_positives


if __name__ == "__main__":
    false_positives = run_comprehensive_test()
    
    if false_positives:
        print(f"\n🚨 Found {len(false_positives)} false positive cases to address!")
    else:
        print(f"\n✅ No false positives found across all configurations!")
