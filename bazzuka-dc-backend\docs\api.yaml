openapi: 3.0.0
info:
  title: Bazzuka API 
  description: API for Bazzuka
  version: 1.0.0

servers:
  - url: https://api.bazzuka.tech
    description: Production server
  - url: http://localhost:5000
    description: Local development server

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  schemas:
    Message:
      type: object
      properties:
        id:
          type: integer
        content:
          type: string
        sender:
          type: string

security:
  - BearerAuth: []

paths:
  /messages:
    get:
      summary: Get all messages
      responses:
        '200':
          description: List of messages
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Message'
    post:
      summary: Post a new message
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Message'
      responses:
        '201':
          description: Message created
