from app.ai.client import AIClient, Action, PromptGenerator
from app.utils.openai.client import openai as openai_client
from datetime import datetime
from pydantic import BaseModel, Field
import json
from app.core.ai.payment_agent_tools import payment_agent_tool_engine
from app.utils.supabase.queries import get_issues_by_defaulter_id, get_scheduled_payment_plans_by_defaulter_id


class PaymentAnalysisPromptGenerator(PromptGenerator):
    def __init__(self):
        super().__init__()
        self.prompt = """Analyze the conversation transcript between a debt collector and a debtor to identify any explicit payment commitments, agreements, requests, or cancellations.

        # CONTEXT
        - You are provided with:
            - The most recent conversation transcript.
            - All available issues (debts) for the defaulter.
            - All scheduled and pending payment arrangements for the defaulter (including their status, amount, type, and dates).
            - The total remaining balance after all scheduled and pending payments are made.

        # SCHEDULING
        - If the debtor **explicitly** agrees to make a payment (one-time or recurring), call the `schedule_payment` tool with the appropriate parameters.
        - If the debtor **explicitly** requests to schedule a follow-up communication at a specific time, call the `schedule_one_off_communication` tool.
        - Only act on **clear and explicit** payment commitments or agreements, not vague statements like "I'll try to pay something" or "maybe next week."
        - For recurring payments, identify the frequency (weekly, biweekly, monthly) and schedule accordingly.
        - For one-time payments, ensure the amount is clearly stated and agreed upon.
        - **Never infer or assume a payment commitment if the debtor does not directly agree to specific terms.**
        - Only process payments when:
            1. The debtor explicitly agrees to pay a specific amount
            2. The timing is clear (for recurring payments, the frequency must be specified)
            3. The terms are confirmed by the debtor

        # CANCELLATION
        - If the debtor **explicitly requests to cancel** an existing payment arrangement, call the `request_cancel_payment_arrangement` tool with the correct payment arrangement ID and (optionally) a reason.
        - This will mark the arrangement as `pending_cancellation` and require human approval.
        - After human approval, the system will:
            - Cancel the corresponding Stripe subscription (if any)
            - Mark the arrangement as `cancelled`
        - **CANCELLATION TRIGGERS: Use the cancellation tool when the debtor says things like:**
            - "Cancel that payment plan"
            - "Cancel all that" (referring to existing arrangements)
            - "I don't want that plan anymore"
            - "Cancel the previous arrangement"
            - "Stop the payment plan"
            - "I want to cancel the monthly/weekly payments"
        - **SPECIFIC EXAMPLE: If the debtor says "So just cancel all that I'll just need to pay the whole amount tomorrow" - this is a CLEAR request to cancel existing arrangements and schedule a new payment. You MUST call `request_cancel_payment_arrangement` for each existing arrangement first, then call `schedule_payment` for the new payment.**
        - **IMPORTANT: If the debtor mentions canceling existing arrangements AND agreeing to a new payment, you MUST:**
            1. FIRST call `request_cancel_payment_arrangement` for each existing arrangement
            2. THEN call `schedule_payment` for the new arrangement
        - **CONTEXT AWARENESS: Always check the "Scheduled and Pending Payment Arrangements" section above. If the debtor requests to cancel arrangements and there are existing arrangements listed, you MUST cancel them before scheduling new ones.**
        - **Do NOT use the cancellation tool unless the debtor clearly requests to cancel a specific payment plan.**

        # CRITICAL DATE REQUIREMENTS
        **ALWAYS use current or future dates. NEVER use past dates.**
        - You will be provided with today's date - USE IT as reference for scheduling
        - When the debtor says "next week", calculate the actual date based on today's date
        - When they say "next month", use the appropriate month/year based on current date
        - All start_date and due_date values MUST be in YYYY-MM-DD format
        - All dates MUST be current or future dates (not past dates)

        # CRITICAL: Understanding Date Contexts
        **Distinguish between payment deadlines vs link delivery timing:**
        - **"I want to pay by [date]"** = due_date only, send link immediately
        - **"I can pay by [date]"** = due_date only, send link immediately  
        - **"Send me the link on [date]"** = start_date for delayed link delivery
        - **"Don't send the link until [date]"** = start_date for delayed link delivery
        
        **For ONE-TIME payments:**
        - start_date = optional, ONLY use when customer explicitly requests delayed link sending
        - due_date = required, when the payment must be completed
        - If customer says "pay by July 2", use due_date="2025-07-02" with NO start_date
        
        **For RECURRING payments:**
        - start_date = when billing cycle begins (this is the first due date)
        - Do NOT include due_date for recurring payments

        # CRITICAL SAFETY RULE - PAYMENT AMOUNT VALIDATION
        **BEFORE scheduling any payment, you MUST verify that the payment amount does not exceed the outstanding balance for the issue.**
        - Check the "Amount Owed" against the payment amount the debtor agreed to
        - If the payment amount exceeds the outstanding balance, DO NOT schedule the payment - it will be REJECTED
        - Only schedule payments for amounts equal to or less than the outstanding balance
        - The system will IMMEDIATELY REJECT any payment amount higher than the outstanding balance

        # Payment Types
        - **One-time payment**: Use `schedule_payment` with `recurring=False`
        - **Weekly payments**: Use `schedule_payment` with `recurring=True, interval="week", interval_count=1`
        - **Biweekly payments**: Use `schedule_payment` with `recurring=True, interval="week", interval_count=2`
        - **Monthly payments**: Use `schedule_payment` with `recurring=True, interval="month", interval_count=1`

        # Follow-up Communications
        - If the debtor requests to be contacted at a specific future date/time, use `schedule_one_off_communication`
        - Include the reason for the follow-up in the `reason` parameter

        # ORDER OF OPERATIONS
        - If the debtor requests to cancel existing arrangements AND schedule new payments:
            1. FIRST: Cancel all existing payment arrangements using `request_cancel_payment_arrangement`
            2. THEN: Schedule new payments using `schedule_payment`
        - This ensures the old arrangements are properly cancelled before new ones are created.

        # Output Format
        There should be no output, only tool calls if necessary.
        """
        self.inputs_prologue = "Here was the most recent communication transcript between the collector and the debtor: \n"

    def make_inputs(self, args):
        assert args.get("conversation"), f"conversation is missing or empty: {args.get('conversation')}"
        assert args.get("defaulter_id"), f"defaulter_id is missing or empty: {args.get('defaulter_id')}"
        assert args.get("issues"), f"issues is missing or empty: {args.get('issues')}"
        
        issues_info = "\n".join([f"  - Issue ID: {issue['issue_id']}, Amount Owed: {issue.get('outstanding_amount', 'Unknown')}" for issue in args["issues"]])

        # Add payment arrangements context
        arrangements = args.get("payment_arrangements", [])
        if arrangements:
            arrangements_info = "\n".join([
                f"  - Payment ID: {arr.get('id')}, Amount: ${arr.get('amount')}, Status: {arr.get('status')}, Approved: {arr.get('_approved')}, Type: {'Recurring' if arr.get('recurring') else 'One-time'}, Due: {arr.get('due_date') or arr.get('start_date', 'N/A')}" for arr in arrangements
            ])
        else:
            arrangements_info = "  (None)"

        # Add remaining balance context
        remaining_balance = args.get("remaining_balance_after_scheduled", "Unknown")

        # Add current date context
        today = datetime.now().strftime("%Y-%m-%d")
        
        inputs = f"""
        - **Today's Date**: {today}
        - **Defaulter ID**: {args["defaulter_id"]}
        - **Available Issues**:
{issues_info}
        - **Scheduled and Pending Payment Arrangements**:
{arrangements_info}
        - **Total Remaining Balance After All Scheduled/Pending Payments**: {remaining_balance}
        - **Recent Conversation**: {args["conversation"]}
        """
        return inputs

    def generate(self, args):
        messages = []
        messages.append(
            {
                "role": "system",
                "content": self.prompt,
            }
        )
        messages.append(
            {
                "role": "user",
                "content": self.inputs_prologue + self.make_inputs(args),
            }
        )
        return messages


model = "gpt-4.1-mini"


class PaymentAnalysisAction(Action):
    def __init__(self):
        super().__init__()
        self.prompt_generator = PaymentAnalysisPromptGenerator()
        self.tool_engine = payment_agent_tool_engine
        self.structured_output_type = None


def make_payment_analysis_client():
    client = AIClient(openai_client, model=model)
    payment_analysis_action = PaymentAnalysisAction()
    client.register("payment_analysis", payment_analysis_action)
    return client


payment_analysis_client = make_payment_analysis_client()


def analyze_payment_commitments(conversation: str, defaulter_id: str) -> dict:
    """
    Analyze the conversation for payment commitments and automatically schedule payments.

    Args:
        conversation: The conversation text to analyze
        defaulter_id: The ID of the defaulter

    Returns:
        dict: Analysis results containing any scheduled payments or communications
    """
    
    # Skip analysis if conversation is empty or None
    if not conversation or conversation.strip() == "":
        # print(f"[PaymentAgent] Skipping analysis for defaulter {defaulter_id} - empty conversation")
        return None
    
    # Ensure defaulter_id is a string
    defaulter_id = str(defaulter_id)
    
    # Get the issues associated with this defaulter
    issues_result = get_issues_by_defaulter_id(defaulter_id)
    issues = issues_result.data if issues_result.data else []
    
    if not issues:
        # print(f"No issues found for defaulter {defaulter_id}")
        return None

    # Get all scheduled and pending payment arrangements for the defaulter
    arrangements_result = get_scheduled_payment_plans_by_defaulter_id(defaulter_id)
    payment_arrangements = arrangements_result.data if arrangements_result and arrangements_result.data else []
    
    # Debug: Log what arrangements were found
    # print(f"[PaymentAgent] Found {len(payment_arrangements)} payment arrangements for defaulter {defaulter_id}")
    # for arr in payment_arrangements:
    #     print(f"[PaymentAgent] Arrangement: ID={arr.get('id')}, Amount=${arr.get('amount')}, Status={arr.get('status')}, Approved={arr.get('_approved')}")
    
    # Calculate total remaining balance after all scheduled/pending payments
    total_owed = 0.0
    for issue in issues:
        try:
            amt = float(str(issue.get('outstanding_amount', '0')).replace('$','').replace(',',''))
            total_owed += amt
        except Exception:
            pass
    total_scheduled = 0.0
    for arr in payment_arrangements:
        try:
            amt = float(str(arr.get('amount', '0')).replace('$','').replace(',',''))
            total_scheduled += amt
        except Exception:
            pass
    remaining_balance = max(total_owed - total_scheduled, 0.0)

    result = (
        payment_analysis_client.do("payment_analysis")
        .with_context(
            {
                "defaulter_id": defaulter_id,
                "issues": issues,
                "conversation": conversation,
                "payment_arrangements": payment_arrangements,
                "remaining_balance_after_scheduled": remaining_balance,
            }
        )
        .execute()
    )

    return result 
