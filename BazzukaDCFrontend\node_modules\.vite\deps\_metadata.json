{"hash": "671439d1", "configHash": "0d5a1687", "lockfileHash": "cadbed1b", "browserHash": "b4bcf578", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "ca50d6fd", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "61f904e8", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "aa928666", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "03eabb47", "needsInterop": true}, "@coreui/icons": {"src": "../../@coreui/icons/dist/esm/index.js", "file": "@coreui_icons.js", "fileHash": "ea90e863", "needsInterop": false}, "@coreui/icons-react": {"src": "../../@coreui/icons-react/dist/index.esm.js", "file": "@coreui_icons-react.js", "fileHash": "7cedfe84", "needsInterop": false}, "@coreui/react": {"src": "../../@coreui/react/dist/esm/index.js", "file": "@coreui_react.js", "fileHash": "1766f773", "needsInterop": false}, "@coreui/react-chartjs": {"src": "../../@coreui/react-chartjs/dist/index.esm.js", "file": "@coreui_react-chartjs.js", "fileHash": "173d291a", "needsInterop": false}, "@coreui/utils": {"src": "../../@coreui/utils/dist/esm/index.js", "file": "@coreui_utils.js", "fileHash": "82786dae", "needsInterop": false}, "@supabase/supabase-js": {"src": "../../@supabase/supabase-js/dist/module/index.js", "file": "@supabase_supabase-js.js", "fileHash": "5fb683e7", "needsInterop": false}, "classnames": {"src": "../../classnames/index.js", "file": "classnames.js", "fileHash": "cf741701", "needsInterop": true}, "core-js": {"src": "../../core-js/index.js", "file": "core-js.js", "fileHash": "93b12f48", "needsInterop": true}, "papaparse": {"src": "../../papaparse/papaparse.min.js", "file": "papaparse.js", "fileHash": "76481297", "needsInterop": true}, "prop-types": {"src": "../../prop-types/index.js", "file": "prop-types.js", "fileHash": "cd90a72b", "needsInterop": true}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "f8e9a7f3", "needsInterop": true}, "react-redux": {"src": "../../react-redux/dist/react-redux.mjs", "file": "react-redux.js", "fileHash": "aa222d95", "needsInterop": false}, "react-router-dom": {"src": "../../react-router-dom/dist/index.js", "file": "react-router-dom.js", "fileHash": "21faf278", "needsInterop": false}, "redux": {"src": "../../redux/dist/redux.mjs", "file": "redux.js", "fileHash": "7381f279", "needsInterop": false}, "simplebar-react": {"src": "../../simplebar-react/dist/index.mjs", "file": "simplebar-react.js", "fileHash": "ef01fb0d", "needsInterop": false}}, "chunks": {"browser-V2CCMWH4": {"file": "browser-V2CCMWH4.js"}, "browser-2R6GTW4O": {"file": "browser-2R6GTW4O.js"}, "chunk-FEWAEXOL": {"file": "chunk-FEWAEXOL.js"}, "chunk-WRD5HZVH": {"file": "chunk-WRD5HZVH.js"}, "chunk-OU5AQDZK": {"file": "chunk-OU5AQDZK.js"}, "chunk-NQPNIMY6": {"file": "chunk-NQPNIMY6.js"}, "chunk-EWTE5DHJ": {"file": "chunk-EWTE5DHJ.js"}}}