from . import api

from .webhook import webhook_blueprint
from .protected import protected_bp
from .communications import communications_bp
from .issues import issues_bp
from .stats import stats_bp
from .orgs import orgs_bp
from .contacts import contacts_bp
from .payments import payments_bp
from .outbound import outbound_bp
from .drafts import drafts_bp
from .status import status_bp
from .defaulters_routes import defaulters_bp
# from app.api.demo.simplechat import simplechat_bp
from .notifications import notifications_bp

api.register_blueprint(webhook_blueprint, url_prefix="/webhook")
api.register_blueprint(protected_bp)
api.register_blueprint(communications_bp, url_prefix="/communications")
api.register_blueprint(issues_bp, url_prefix="/issues")
api.register_blueprint(stats_bp, url_prefix="/stats")
api.register_blueprint(orgs_bp, url_prefix="/orgs")
api.register_blueprint(contacts_bp, url_prefix="/contacts")
api.register_blueprint(payments_bp)
api.register_blueprint(outbound_bp, url_prefix="/outbound")
api.register_blueprint(drafts_bp)
api.register_blueprint(status_bp, url_prefix="/status")
api.register_blueprint(defaulters_bp, url_prefix="/defaulters")
# api.register_blueprint(simplechat_bp, url_prefix="/simplechat")
api.register_blueprint(notifications_bp, url_prefix="/notifications")
