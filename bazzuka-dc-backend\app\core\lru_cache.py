from collections import OrderedDict


class LRUCache:
    def __init__(self, max_size):
        """Initialize the cache with a maximum size."""
        self.cache = OrderedDict()
        self.max_size = max_size

    def get(self, key):
        """Retrieve a value from the cache, if it exists."""
        if key in self.cache:
            # Move the key to the end to mark it as recently used
            self.cache.move_to_end(key)
            return self.cache[key]
        return None

    def set(self, key, value):
        """Add a key-value pair to the cache."""
        if key in self.cache:
            # Update the value and mark it as recently used
            self.cache.move_to_end(key)
        elif len(self.cache) >= self.max_size:
            # Evict the least recently used (first) item
            self.cache.popitem(last=False)
        self.cache[key] = value
