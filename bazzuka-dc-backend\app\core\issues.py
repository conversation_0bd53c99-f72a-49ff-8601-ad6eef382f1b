import json

from app.utils.supabase.queries import (
    get_comm_logs,
    get_emails_by_id,
    get_summaries_by_defaulter_id,
    get_issues_by_defaulter_id,
    get_org,
    get_comm_logs_by_defaulter_id,
)

from .defaulters import defaulters
from .vapi import get_call_transcripts


class IssueRepository:
    """
    This class is responsible for getting info from the database and processing the data to be returned to the client
    and also bridging together data sources.

    Note it might need to be renamed or reogranized.
    """

    def __init__(self):
        pass

    def get_debt_info(self, defaulter_id):
        result = get_issues_by_defaulter_id(defaulter_id).data
        return result

    def get_issue(self, email_address):
        result = defaulters.get_defaulter_by_email(email_address)
        return result

    def get_comm_history_mixed(
        self, defaulter_id, from_date=None, to_date=None, limit=10
    ):
        """
        Returns a mixed list of call summaries and email transcripts in a standardized format.
        Each item in the list has the following structure:
        {
            type: "call" | "email",
            summary: str,  # For calls, this is the summary from commlogs
            content: str,  # For emails, this is the full email body
            timestamp: str,
            direction: "inbound" | "outbound", limit=limit
        }
        """
        # Fetch the communication history for the issue
        comm_logs = get_comm_logs_by_defaulter_id(defaulter_id).data
        if not comm_logs:
            return []

        mixed_history = []
        email_ids = []

        # Separate email and call logs
        for comm in comm_logs:
            if comm["channel"] == "email":
                email_ids.append(comm["comm_id"])
            elif comm["channel"] == "call":
                # For calls, we use the summary field
                mixed_history.append(
                    {
                        "type": "call",
                        "summary": comm["summary"],
                        "content": None,  # Calls don't have content
                        "timestamp": comm["timestamp"],
                        "direction": comm["direction"],
                    }
                )

        # Get email transcripts
        if email_ids:
            emails = get_emails_by_id(email_ids).data
            for email in emails:
                mixed_history.append(
                    {
                        "type": "email",
                        "summary": None,  # Emails don't have summaries
                        "content": email["email_body"],
                        "timestamp": email["timestamp"],
                        "direction": email["direction"],
                    }
                )

        # Sort by timestamp
        mixed_history.sort(key=lambda x: x["timestamp"])
        return mixed_history

    def get_comm_history(self, defaulter_id, from_date=None, to_date=None, limit=5):
        """
        Note: tested and works
        TODO: tidy the data more before returning it (remove unnecessary fields, etc.)
        NOTE: We can lookup emails from emaillogs using defaulter_id, then filter commlogs by defaulter_id and channel=email
        """
        # Fetch the communication history for the issue.
        comm_logs = get_comm_logs_by_defaulter_id(defaulter_id).data

        # print(comm_logs)

        if not comm_logs:
            return None

        return comm_logs

        email_ids = []
        calls = []
        for comm in comm_logs:
            if comm["channel"] == "email":
                email_ids.append(comm["comm_id"])
            elif comm["channel"] == "call":
                calls.append(comm)

        # get emails by id from emaillogs
        emails = []
        if email_ids:
            emails = get_emails_by_id(email_ids).data

        # get calls by id from VAPI
        call_transcripts = []
        if calls:
            call_transcripts = get_call_transcripts(calls)

        # mix the two lists, sorted by timestamp
        comm_history = sorted(emails + call_transcripts, key=lambda x: x["timestamp"])
        return json.dumps(comm_history)

    def get_summaries(self, defaulter_id):
        """
        Note: tested and works
        """
        summaries = get_summaries_by_defaulter_id(defaulter_id)
        return summaries.data

    def get_strategy(self, defaulter_id):
        return defaulters.get_strategy(defaulter_id)
