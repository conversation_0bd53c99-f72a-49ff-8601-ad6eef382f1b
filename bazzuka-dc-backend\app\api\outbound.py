from flask import Blueprint, request, jsonify
from app.core.comm_manager import comm_manager as manager
import traceback

outbound_bp = Blueprint("outbound_api", __name__)


@outbound_bp.route("", methods=["POST", "OPTIONS", "GET"])
def outbound_route():
    if request.method == "OPTIONS":
        return jsonify({"methods": ["POST", "OPTIONS"]}), 200

    data = request.get_json()
    print("[DEBUG] Incoming /v0/outbound data:", data)

    # Check if this is a batch request
    if "items" in data:
        success_results = []
        failed_results = []
        for item in data["items"]:
            try:
                print("[DEBUG] Executing batch item:", item)
                manager.execute(
                    item["defaulter_id"],
                    item["channel"],
                    action_channel_reason=item["reason"],
                )
                success_results.append(
                    {
                        "actionitem_id": item.get("actionitem_id"),
                        "defaulter_id": item["defaulter_id"],
                        "status": "success",
                    }
                )
            except Exception as e:
                print("[ERROR] Exception in batch item:", item, e)
                failed_results.append(
                    {
                        "actionitem_id": item.get("actionitem_id"),
                        "defaulter_id": item["defaulter_id"],
                        "status": "failed",
                        "error": str(e),
                    }
                )
        return jsonify({"successful": success_results, "failed": failed_results}), 200

    # Handle single message (existing behavior)
    try:
        print("[DEBUG] Executing single outbound:", data)
        manager.execute(
            data["defaulter_id"],
            data["channel"],
            action_channel_reason=data["reason"],
        )
    except Exception as e:
        print("[ERROR] Exception in single outbound:", data, e)
        traceback.print_exc()
        return jsonify({"error": str(e)}), 400

    return jsonify({"message": "Outbounds successfully initiated."}), 200
