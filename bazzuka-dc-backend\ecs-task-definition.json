{"family": "bazzuka-api", "networkMode": "awsvpc", "requiresCompatibilities": ["FARGATE"], "cpu": "1024", "memory": "2048", "executionRoleArn": "arn:aws:iam::YOUR_ACCOUNT_ID:role/ecsTaskExecutionRole", "taskRoleArn": "arn:aws:iam::YOUR_ACCOUNT_ID:role/ecsTaskRole", "containerDefinitions": [{"name": "bazzuka-api", "image": "YOUR_ECR_REPOSITORY_URI:latest", "portMappings": [{"containerPort": 8000, "protocol": "tcp"}], "essential": true, "environment": [{"name": "FLASK_ENV", "value": "production"}, {"name": "PYTHONPATH", "value": "/app"}, {"name": "BACKGROUND_MAX_WORKERS", "value": "10"}, {"name": "ROOT_DOMAIN", "value": "https://your-domain.com"}], "secrets": [{"name": "OPENAI_API_KEY", "valueFrom": "arn:aws:secretsmanager:us-east-1:YOUR_ACCOUNT_ID:secret:bazzuka/openai-api-key"}, {"name": "SUPABASE_URL", "valueFrom": "arn:aws:secretsmanager:us-east-1:YOUR_ACCOUNT_ID:secret:bazzuka/supabase-url"}, {"name": "SUPABASE_SERVICE_ROLE_KEY", "valueFrom": "arn:aws:secretsmanager:us-east-1:YOUR_ACCOUNT_ID:secret:bazzuka/supabase-service-key"}, {"name": "VAPI_API_KEY", "valueFrom": "arn:aws:secretsmanager:us-east-1:YOUR_ACCOUNT_ID:secret:bazzuka/vapi-api-key"}, {"name": "VAPI_SECRET_KEY", "valueFrom": "arn:aws:secretsmanager:us-east-1:YOUR_ACCOUNT_ID:secret:bazzuka/vapi-secret-key"}, {"name": "SENDGRID_API_KEY", "valueFrom": "arn:aws:secretsmanager:us-east-1:YOUR_ACCOUNT_ID:secret:bazzuka/sendgrid-api-key"}, {"name": "NYLAS_API_KEY", "valueFrom": "arn:aws:secretsmanager:us-east-1:YOUR_ACCOUNT_ID:secret:bazzuka/nylas-api-key"}, {"name": "NYLAS_CLIENT_ID", "valueFrom": "arn:aws:secretsmanager:us-east-1:YOUR_ACCOUNT_ID:secret:bazzuka/nylas-client-id"}, {"name": "NYLAS_WEBHOOK_SECRET", "valueFrom": "arn:aws:secretsmanager:us-east-1:YOUR_ACCOUNT_ID:secret:bazzuka/nylas-webhook-secret"}], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/bazzuka-api", "awslogs-region": "us-east-1", "awslogs-stream-prefix": "ecs"}}, "healthCheck": {"command": ["CMD-SHELL", "curl -f http://localhost:8000/v0/status/health || curl -f http://localhost:8000/ || exit 1"], "interval": 30, "timeout": 10, "retries": 3, "startPeriod": 120}}]}