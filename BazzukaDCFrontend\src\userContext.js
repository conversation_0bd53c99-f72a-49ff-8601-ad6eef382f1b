// src/UserContext.js
import { createContext, useContext, useState, useEffect } from 'react'
import { supabase } from './supabaseClient'

const UserContext = createContext()

export const UserProvider = ({ children }) => {
  const [user, setUser] = useState(null)

  useEffect(() => {
    // Check for an authenticated user on initial load
    const getSession = async () => {
      const {
        data: { session },
      } = await supabase.auth.getSession()
      setUser(session?.user ?? null)
      console.log(session?.user.user_metadata ?? null);
      
      if (session?.user.id) {
        document.cookie = `sb-token=${session.access_token.toString() ?? null};domain=.bazzuka.ai;`
        sessionStorage.setItem('access_token', session?.access_token.toString() ?? null)
        sessionStorage.setItem('org_id', session?.user.user_metadata.org_id.toString() ?? null)
      }
    }

    getSession()

    // Listen for auth changes
    const { data: authListener } = supabase.auth.onAuthStateChange((_event, session) => {
      setUser(session?.user ?? null)
    })

    // Ensure unsubscribe if authListener is active
    return () => {
      if (authListener?.subscription) {
        authListener.subscription.unsubscribe()
      }
    }
  }, [])

  return <UserContext.Provider value={user}>{children}</UserContext.Provider>
}

export const useUser = () => useContext(UserContext)
