[tool.poetry]
name = "bazzuka-api"
version = "0.1.0"
description = ""
authors = ["mwomick <<EMAIL>>"]
readme = "README.md"
package-mode = false

[tool.poetry.dependencies]
python = "^3.12"
flask = {extras = ["async"], version = "^3.1.0"}
openai = "^1.59.8"
flask-cors = "^4.0.0"
python-dotenv = "^1.0.1"
requests = "^2.32.3"
llama-index = "^0.9.19"
supabase = "^2.11.0"
setuptools = "^75.3.0"
schedule = "^1.2.2"
sendgrid = "^6.11.0"
aiohttp = "^3.11.11"
nylas = "^6.5.0"
bs4 = "^0.0.2"
html2text = "^2024.2.26"
authorizenet = "^1.1.5"
gunicorn = "^21.2.0"
stripe = "^12.2.0"
holidays = "^0.74"

[tool.poetry.group.dev.dependencies]
pytest = "^8.3.3"
black = "^24.10.0"

[tool.poetry.scripts]
dev = "run_local:main"
test-api = "test_api_local:main"
check-setup = "check_setup:main"
test-scalability = "test_scalability:main"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"