import os
import json
import email
from datetime import datetime, timezone
import hmac  # TODO: move verification logic to utils.helpers
import hashlib  # TODO: move verification logic to utils.helpers
import re

from flask import Blueprint, request, jsonify

from app.core.comm_manager import comm_manager as manager

email_webhooks_bp = Blueprint("email_hooks", __name__)

from bs4 import BeautifulSoup
import html2text


# From Twilio SendGrid Inbound Parse Webhook
@email_webhooks_bp.route("/sendgrid", methods=["POST"])
def recv_email_sg():
    envelope = json.loads(request.form.get("envelope"))
    text = request.form.get("text")
    # html = request.form.get('html')
    subject = request.form.get("subject")
    headers = request.form.get("headers")
    date = None
    if headers:
        date = email.message_from_string(headers)["Date"]

    data = {
        "text": text,
        "subject": subject,
        "senderAddress": envelope["from"],
        "recipientAddress": envelope["to"],
        "timestamp": (
            datetime.strptime(date, "%a, %d %b %Y %H:%M:%S %z").strftime(
                "%Y-%m-%d %H:%M:%S%z"
            )
            if date
            else datetime.now().strftime("%Y-%m-%d %H:%M:%S%z")
        ),
    }

    try:
        manager.process("email", data)
    except Exception as e:
        print(e)
        return jsonify({"error": str(e)}), 500

    return jsonify({"message": "Email received"}), 200


@email_webhooks_bp.route("/nylas", methods=["GET", "POST"])
def recv_email_nylas():
    # TODO: move verification logic to utils.helpers
    # Signature verification
    def verify_signature(message, key, signature):
        digest = hmac.new(key, msg=message, digestmod=hashlib.sha256).hexdigest()
        return hmac.compare_digest(digest, signature)

    # We are connected to Nylas, let’s return the challenge parameter.
    if request.method == "GET" and "challenge" in request.args:
        print(" * Nylas connected to the webhook!")
        return request.args["challenge"], 200

    if request.method == "POST":
        print("rcvd")
        is_genuine = verify_signature(
            message=request.data,
            key=os.environ["NYLAS_WEBHOOK_SECRET"].encode("utf8"),
            signature=request.headers.get("x-nylas-signature"),
        )

        print("Signature:", request.headers.get("x-nylas-signature"))

        if not is_genuine:
            return "Signature verification failed!", 401
        data = request.get_json()
        print(data["data"]["object"])

        body = data["data"]["object"]["body"]

        # Parse and clean the HTML
        soup = BeautifulSoup(body, "html.parser")
        for blockquote in soup.find_all("blockquote"):
            blockquote.decompose()

        # Convert HTML to plain text
        text_maker = html2text.HTML2Text()
        text_maker.body_width = 0  # Prevent line wrapping
        text_maker.ignore_links = True  # Remove links
        plain_text = text_maker.handle(str(soup))

        # Extract user's response (text before reply markers)
        response = re.split(
            r"(?i)(\*{0,2}From:|\*\*Sent:|\*\*To:|\*\*Subject:)", plain_text, maxsplit=1
        )[0].strip()

        # Improved separator removal: Handles * * *, --- and similar dividers
        response = re.sub(r"(?m)^\s*([*\-_][\s*_\-]*){2,}\s*$", "", response).strip()

        # NOTE: plain_text is the raw email body, response is the specific snippet of the user's response to a thread
        today = datetime.now(timezone.utc)
        post_data = {
            "id": data["data"]["object"]["id"],
            "timestamp": datetime.fromtimestamp(
                data["data"]["object"]["date"], today.tzinfo
            ).strftime("%Y-%m-%d %H:%M:%S%z"),
            "subject": data["data"]["object"]["subject"],
            "text": response,
            "senderAddress": data["data"]["object"]["from"][0]["email"],
            "senderName": data["data"]["object"]["from"][0]["name"],
            "recipientAddress": data["data"]["object"]["to"][0]["email"],
        }

        try:
            manager.process("email", post_data)
        except Exception as e:
            print(e)
            return jsonify({"message": "email already exists"}), 200

        return jsonify({"message": "email received"}), 200
