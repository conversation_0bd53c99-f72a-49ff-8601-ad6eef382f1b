import threading
import time
import os
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from typing import List, Dict, Any
from datetime import datetime
import traceback

from app.core.ai import make_ai_client, make_negotiation_gen_client
from app.utils.supabase.queries import (
    get_negotiation_strategy,
    get_org,
    insert_negotiation_strategy,
    update_customizations,
)
from app.core.defaulters import defaulters
from app.core.comm_manager import comm_manager


class BackgroundTaskManager:
    def __init__(self, max_workers: int = None):
        # Configure max workers based on environment
        if max_workers is None:
            max_workers = int(os.environ.get("BACKGROUND_MAX_WORKERS", "10"))
        
        self.max_workers = max_workers
        
        print(f"BackgroundTaskManager initialized with {self.max_workers} max workers (thread-safe AI clients)")
    
    def process_strategy_generation_batch(self, defaulter_ids: List[str]) -> None:
        """Process negotiation strategy generation for multiple defaulters in parallel"""
        
        def generate_strategy_for_defaulter(defaulter_id: str) -> Dict[str, Any]:
            try:
                print(f"Generating strategy for defaulter: {defaulter_id} (Thread: {threading.current_thread().name})")
                
                # Create thread-local AI clients (now thread-safe by default)
                negotiation_gen_client = make_negotiation_gen_client()
                
                # Check if strategy already exists
                strategy = get_negotiation_strategy(defaulter_id)
                if strategy.data and strategy.data[0]["negotiation_strategy"] is not None:
                    print(f"Strategy already exists for {defaulter_id}")
                    return {"defaulter_id": defaulter_id, "status": "exists", "strategy": strategy.data[0]["negotiation_strategy"]}
                
                # Get organization policy
                org_response = get_org()
                policy = ""
                if org_response.data:
                    metadata = org_response.data[0]["metadata"]
                    if metadata.get("settlement_plans"):
                        policy += "\nThe settlement options are: " + metadata["settlement_plans"]
                
                # Import here to avoid circular imports
                defaulter = str(defaulters.get_defaulter_by_id(defaulter_id))
                
                # Generate strategy using AI (thread-safe)
                output = negotiation_gen_client.do("generate_negotiation_prompt").with_context({
                    "policy": policy,
                    "case_info": defaulter,
                }).execute()
                
                # Insert strategy
                insert_negotiation_strategy(defaulter_id, output)
                update_customizations(defaulter_id, {"negotiation_strategy": output})
                
                print(f"Strategy generated successfully for {defaulter_id}")
                return {"defaulter_id": defaulter_id, "status": "success", "strategy": output}
                
            except Exception as e:
                print(f"Error generating strategy for {defaulter_id}: {str(e)}")
                return {"defaulter_id": defaulter_id, "status": "error", "error": str(e)}
        
        # Process strategies in parallel batches
        batch_size = min(self.max_workers, len(defaulter_ids))
        
        with ThreadPoolExecutor(max_workers=batch_size) as executor:
            # Submit all tasks
            future_to_defaulter = {
                executor.submit(generate_strategy_for_defaulter, defaulter_id): defaulter_id
                for defaulter_id in defaulter_ids
            }
            
            # Process results as they complete
            for future in as_completed(future_to_defaulter):
                defaulter_id = future_to_defaulter[future]
                try:
                    result = future.result()
                    print(f"Completed strategy generation for {defaulter_id}: {result['status']}")
                except Exception as exc:
                    print(f"Strategy generation failed for {defaulter_id}: {exc}")
    
    def process_communication_batch(self, defaulter_ids: List[str], demo_mode: bool = True) -> None:
        """Process communication execution for multiple defaulters in parallel"""
        
        def execute_communication_for_defaulter(defaulter_id: str) -> Dict[str, Any]:
            try:
                print(f"Processing communication for defaulter: {defaulter_id} (Thread: {threading.current_thread().name})")
                
                # Create thread-local AI client (now thread-safe by default)
                ai_client = make_ai_client()
                
                defaulter = defaulters.get_defaulter_by_id(defaulter_id)
                if not defaulter:
                    print(f"Defaulter not found for ID: {defaulter_id}")
                    return {"defaulter_id": defaulter_id, "status": "error", "error": "Defaulter not found"}
                
                print(f"Defaulter found: {defaulter_id}")
                print(f"Defaulter data structure: {type(defaulter)}")
                print(f"Defaulter keys: {list(defaulter.keys()) if isinstance(defaulter, dict) else 'Not a dict'}")
                if isinstance(defaulter, dict):
                    print(f"Email field: {defaulter.get('email', 'NOT FOUND')}")
                    print(f"Name field: {defaulter.get('name', 'NOT FOUND')}")
                
                if demo_mode:
                    # Send email in demo mode
                    print(f"Executing demo mode communication for {defaulter_id}")
                    try:
                        # Validate defaulter has required fields before calling comm_manager
                        if not defaulter.get("email"):
                            raise Exception(f"Defaulter {defaulter_id} has no email address")
                        if not defaulter.get("name"):
                            raise Exception(f"Defaulter {defaulter_id} has no name")
                        
                        print(f"Defaulter validation passed for {defaulter_id}")
                        comm_manager.execute(defaulter_id, "email")
                        print(f"Demo communication executed successfully for {defaulter_id}")
                    except Exception as comm_error:
                        print(f"Error in comm_manager.execute for {defaulter_id}: {str(comm_error)}")
                        print(f"Defaulter data: {defaulter}")
                        raise comm_error
                else:
                    # Production mode - run analysis (thread-safe)
                    print(f"Executing production mode analysis for {defaulter_id}")
                    try:
                        org = get_org()
                        if not org.data:
                            raise Exception("No organization data found")
                        
                        print(f"Organization data retrieved for {defaulter_id}")
                        communication_channels = org.data[0]["metadata"].get("communication_channels", {})
                        
                        analyzer_args = {
                            "defaulter_id": defaulter_id,
                            "case_info": defaulter,
                            "current_conversation_summary": "New defaulter added to system",
                            "current_conversation_direction": "outbound",
                            "collections_strategy": org.data[0]["metadata"].get("strategy", ""),
                            "communication_channels": communication_channels,
                            "restrictions": "There are currently no time, holiday, or day of week restrictions for this user.",
                        }
                        
                        print(f"Running AI analysis for {defaulter_id}")
                        print(f"[BackgroundTasks] AI ARGS SENT TO ANALYZER: {analyzer_args}")
                        ai_client.do("analyze").with_context(analyzer_args).execute()
                        print(f"AI analysis completed successfully for {defaulter_id}")
                    except Exception as analysis_error:
                        print(f"Error in AI analysis for {defaulter_id}: {str(analysis_error)}")
                        print(f"Analysis args: {analyzer_args}")
                        raise analysis_error
                
                print(f"Communication processed successfully for {defaulter_id}")
                return {"defaulter_id": defaulter_id, "status": "success"}
                
            except Exception as e:
                error_msg = f"Error processing communication for {defaulter_id}: {str(e)}"
                print(error_msg)
                print(f"Full traceback: {traceback.format_exc()}")
                return {"defaulter_id": defaulter_id, "status": "error", "error": str(e)}
        
        # Process communications in parallel batches
        batch_size = min(self.max_workers, len(defaulter_ids))
        
        with ThreadPoolExecutor(max_workers=batch_size) as executor:
            # Submit all tasks
            future_to_defaulter = {
                executor.submit(execute_communication_for_defaulter, defaulter_id): defaulter_id
                for defaulter_id in defaulter_ids
            }
            
            # Process results as they complete
            for future in as_completed(future_to_defaulter):
                defaulter_id = future_to_defaulter[future]
                try:
                    result = future.result()
                    print(f"Completed communication processing for {defaulter_id}: {result['status']}")
                except Exception as exc:
                    print(f"Communication processing failed for {defaulter_id}: {exc}")
    
    def process_defaulters_async(self, defaulter_ids: List[str], demo_mode: bool = True) -> None:
        """
        Process strategy generation and communication for defaulters asynchronously
        This method returns immediately and processes in the background
        """
        def background_worker():
            try:
                print(f"Starting background processing for {len(defaulter_ids)} defaulters")
                
                # Process strategy generation first
                self.process_strategy_generation_batch(defaulter_ids)
                
                # Then process communications
                self.process_communication_batch(defaulter_ids, demo_mode)
                
                print(f"Completed background processing for {len(defaulter_ids)} defaulters")
                
            except Exception as e:
                print(f"Error in background processing: {str(e)}")
        
        # Start background thread
        background_thread = threading.Thread(target=background_worker)
        background_thread.daemon = True
        background_thread.start()
        
        print(f"Background processing started for {len(defaulter_ids)} defaulters")


# Global instance
background_task_manager = BackgroundTaskManager() 