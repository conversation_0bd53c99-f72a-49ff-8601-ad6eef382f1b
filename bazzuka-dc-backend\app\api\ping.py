from flask import Blueprint, jsonify, g

# from ..utils.supabase.client import supabase as s
from ..utils.supabase.admin import update_user_metadata

ping_bp = Blueprint("ping", __name__)


@ping_bp.route("/ping", methods=["GET", "POST", "OPTIONS"])
def ping():
    update_user_metadata(
        g.user,
        {
            "org_id": "8241d390-a8b5-4f59-b0a9-b95c074db3f5",
            "nylas_grant_id": "490ffcaa-0de8-4a0e-951b-c652b570e14d",
        },
    )
    return jsonify({"message": "pong"}), 200
