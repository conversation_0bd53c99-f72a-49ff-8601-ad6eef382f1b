import React, { useEffect, useState } from 'react';

const NotificationRow = ({ n, onDone }) => {
  return (
    <div style={{
      display: 'flex',
      alignItems: 'center',
      padding: '18px 0',
      borderBottom: '1px solid #e5e7eb',
      minHeight: 56,
    }}>
      {/* Always blue dot */}
      <div style={{ width: 32, display: 'flex', justifyContent: 'center' }}>
        <span style={{ color: '#2563eb', fontSize: 18, display: 'inline-block', marginTop: 2 }}>●</span>
      </div>
      {/* Main content */}
      <div style={{ flex: 1 }}>
        <div style={{ fontWeight: 600, color: '#222', fontSize: 15 }}>
          {n.defaulter_name ? n.defaulter_name : 'Unknown'}
          {n.contact && (
            <span style={{ fontWeight: 400, color: '#555', fontSize: 13, marginLeft: 8 }}>
              ({n.contact})
            </span>
          )}
        </div>
        <div style={{ color: '#222', fontSize: 14, marginTop: 2 }}>
          {n.message}
        </div>
        <div style={{ color: '#888', fontSize: 12, marginTop: 2 }}>
          {n.date ? new Date(n.date).toLocaleString() : ''}
        </div>
      </div>
      {/* Done button or checkmark */}
      <div style={{ minWidth: 56, textAlign: 'right' }}>
        {n.is_done ? (
          <span style={{ color: '#22c55e', fontSize: 22 }}>✔️</span>
        ) : (
          <button
            style={{
              background: '#e5e7eb',
              border: 'none',
              borderRadius: 6,
              padding: '4px 12px',
              color: '#222',
              fontWeight: 500,
              fontSize: 13,
              cursor: 'pointer',
              marginLeft: 8
            }}
            onClick={onDone}
            disabled={n.is_done}
          >Done</button>
        )}
      </div>
    </div>
  );
};

const NotificationsPage = () => {
  const [notifications, setNotifications] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [updating, setUpdating] = useState({});

  useEffect(() => {
    const fetchNotifications = async () => {
      setLoading(true);
      try {
        const response = await fetch('/v0/notifications');
        if (!response.ok) throw new Error('Failed to fetch notifications');
        const data = await response.json();
        setNotifications(data.notifications || []);
      } catch (err) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };
    fetchNotifications();
  }, []);

  const handleDone = async (id) => {
    setUpdating((u) => ({ ...u, [id]: true }));
    try {
      const res = await fetch(`/v0/notifications/${id}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ is_done: true }),
      });
      if (!res.ok) throw new Error('Failed to update notification');
      // Update notification in state
      setNotifications((prev) =>
        prev.map((n) => (n.id === id ? { ...n, is_done: true } : n))
      );
    } catch (e) {
      alert('Failed to mark as done.');
    } finally {
      setUpdating((u) => ({ ...u, [id]: false }));
    }
  };

  return (
    <div style={{
      minHeight: 'calc(100vh - 56px)',
      width: '100%',
      background: '#fff',
      padding: 0,
      margin: 0,
    }}>
      <div style={{
        maxWidth: 900,
        margin: '0 auto',
        padding: '0 24px',
      }}>
        <h2 style={{ fontWeight: 700, fontSize: 28, margin: '32px 0', color: '#111', textAlign: 'left' }}>Notifications</h2>
        {loading && <div style={{ textAlign: 'center', color: '#888', padding: 32 }}>Loading...</div>}
        {error && <div style={{ textAlign: 'center', color: 'red', padding: 32 }}>{error}</div>}
        {!loading && !error && notifications.length === 0 && (
          <div style={{ textAlign: 'center', color: '#888', padding: 32 }}>No notifications</div>
        )}
        <div>
          {notifications.map((n, idx) => (
            <NotificationRow
              n={n}
              key={n.id || idx}
              onDone={() => handleDone(n.id)}
              disabled={!!updating[n.id]}
            />
          ))}
        </div>
      </div>
    </div>
  );
};

export default NotificationsPage; 