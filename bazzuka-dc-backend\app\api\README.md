# app.api

## Overview

This directory should implement [Flask Blueprints](https://flask.palletsprojects.com/en/stable/blueprints/).
No (or very minimal!) application logic should be implemented in this directory at all.
For example, this means no database queries, no data processing, no third party libraries (OpenAI, Supabase, etc.).

What is allowed:

- Parsing or unwrapping a request so the proper arguments can be passed into a `core` object.
- Importing types if necessary to wrap arguments to be passed into a `core` object.

## Contents

### `routes.py`

This registers all the Blueprint children to the Blueprint parent. Any subdirectories should have a `route.py` and a `__init__.py` that follows the pattern in the root (`app/api`) folder.

### `__init__.py`

Exposes the routes for easy importing in the parent Blueprint/Flask app.
