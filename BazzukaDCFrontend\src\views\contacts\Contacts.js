import React, { useEffect, useState } from 'react'
import { useNavigate } from 'react-router-dom'
import {
  <PERSON>ard,
  CCardBody,
  CCardHeader,
  CCol,
  CRow,
  CTable,
  CTableBody,
  CTableDataCell,
  CTableHead,
  CTableHeaderCell,
  CTableRow,
  CButton,
  CButtonGroup,
} from '@coreui/react'
import CIcon from '@coreui/icons-react'
import { cilUser, cilCloudDownload, cilPhone, cilEnvelopeClosed } from '@coreui/icons'
import ImportModal from './ContactsUploadModal'
import { getApiUrl } from '../../utils/apiConfig'

const Contacts = () => {
  const navigate = useNavigate()
  const user = sessionStorage.getItem('access_token')

  useEffect(() => {
    if (!user) navigate('/login')
  }, [user, navigate])

  const [showImportModal, setShowImportModal] = useState(false)
  const [contacts, setContacts] = useState([])
  console.log(contacts)

  const company = 'IEC'

  const handleImportClick = () => {
    setShowImportModal(true)
  }

  const handleCloseModal = () => {
    setShowImportModal(false)
  }

  const handleUpload = (uploadedContacts) => {
    setContacts((prevContacts) => [...prevContacts, ...uploadedContacts])
    console.log('con',contacts);

  }


  const startAutopilot = async () => {
    try {
      const response = await fetch(getApiUrl('/v0/contacts'), {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
          // Authorization: `Bearer ${user}`,
        },
        body: JSON.stringify(contacts),
        redirect: 'follow',
      })
      if (response.ok) {
        console.log('Request successful')
        navigate('/dashboard')
      } else {
        console.error('Request failed')
      }
    } catch (error) {
      console.error('Error making call request:', error)
    }
  }

  return (
    <>
      <CRow>
        <CCol>
          <CCard>
            <CCardHeader>
              <CButtonGroup>
                {/* <CButton color="primary">
                  <CIcon icon={cilUser} /> Add Contact
                </CButton> */}
                <CButton color="secondary" onClick={handleImportClick}>
                  <CIcon icon={cilCloudDownload} /> Upload Contacts
                </CButton>
                <CButton color="info" onClick={() => {
                  alert('You will be redirected to a google spreadsheet. In that please fill your name, email and phone number, download it as CSV and come back here to upload it.');
                  window.open('https://docs.google.com/spreadsheets/d/1VipzRx3a9NYYASKl0x6QWiwsDMfpcVTvwCSIjtpoB18/edit?usp=sharing', '_blank');
                }}>
                  Sample CSV
                </CButton>
              </CButtonGroup>
            </CCardHeader>
            <CCardBody>
              {company === 'IEC' ? (
                <CTable className="mb-0 border" hover responsive>
                  <CTableHead className="text-nowrap">
                    <CTableRow>
                      <CTableHeaderCell className="bg-body-tertiary">ID</CTableHeaderCell>
                      <CTableHeaderCell className="bg-body-tertiary">Name</CTableHeaderCell>
                      <CTableHeaderCell className="bg-body-tertiary">Email</CTableHeaderCell>
                      <CTableHeaderCell className="bg-body-tertiary">Phone</CTableHeaderCell>
                      <CTableHeaderCell className="bg-body-tertiary">SSN</CTableHeaderCell>
                      <CTableHeaderCell className="bg-body-tertiary">DOB</CTableHeaderCell>
                      <CTableHeaderCell className="bg-body-tertiary">Delinquency</CTableHeaderCell>
                      <CTableHeaderCell className="bg-body-tertiary">Outstanding</CTableHeaderCell>
                      <CTableHeaderCell className="bg-body-tertiary">Last Paid on</CTableHeaderCell>
                      <CTableHeaderCell className="bg-body-tertiary">Last Paid Amt</CTableHeaderCell>
                    </CTableRow>
                  </CTableHead>
                  <CTableBody>
                    {contacts.map((contact, index) => (
                      <CTableRow key={index}>
                        <CTableDataCell className="text-nowrap">{contact.studnum}</CTableDataCell>
                        <CTableDataCell className="text-nowrap">{contact.name}</CTableDataCell>
                        <CTableDataCell className="text-nowrap">{contact.email}</CTableDataCell>
                        <CTableDataCell className="text-nowrap">{contact.phone}</CTableDataCell>
                        <CTableDataCell className="text-nowrap">
                          {'*'.repeat(contact.ssn.length)}
                        </CTableDataCell>
                        <CTableDataCell className="text-nowrap">{contact.dob}</CTableDataCell>
                        <CTableDataCell className="text-nowrap">{contact.delinquency}</CTableDataCell>
                        <CTableDataCell className="text-nowrap">{contact.outstanding_amount}</CTableDataCell>
                        <CTableDataCell className="text-nowrap">{contact.last_paid_date}</CTableDataCell>
                        <CTableDataCell className="text-nowrap">{contact.last_paid_amt}</CTableDataCell>
                      </CTableRow>
                    ))}
                  </CTableBody>
                </CTable>
              ) : (
                <CTable className="mb-0 border" hover responsive>
                  <CTableHead className="text-nowrap">
                    <CTableRow>
                      <CTableHeaderCell className="bg-body-tertiary">Account ID</CTableHeaderCell>
                      <CTableHeaderCell className="bg-body-tertiary">Name</CTableHeaderCell>
                      <CTableHeaderCell className="bg-body-tertiary">Email</CTableHeaderCell>
                      <CTableHeaderCell className="bg-body-tertiary">Phone</CTableHeaderCell>
                      <CTableHeaderCell className="bg-body-tertiary">SSN</CTableHeaderCell>
                      <CTableHeaderCell className="bg-body-tertiary">Location</CTableHeaderCell>
                      <CTableHeaderCell className="bg-body-tertiary">Outstanding</CTableHeaderCell>
                      <CTableHeaderCell className="bg-body-tertiary">Due</CTableHeaderCell>
                      <CTableHeaderCell className="bg-body-tertiary">Principal</CTableHeaderCell>
                      <CTableHeaderCell className="bg-body-tertiary">Type</CTableHeaderCell>
                    </CTableRow>
                  </CTableHead>
                  <CTableBody>
                    {contacts.map((contact, index) => (
                      <CTableRow key={index}>
                        <CTableDataCell className="text-nowrap">{contact.name}</CTableDataCell>
                        <CTableDataCell className="text-nowrap">{contact.email}</CTableDataCell>
                        <CTableDataCell className="text-nowrap">{contact.phone}</CTableDataCell>
                        <CTableDataCell className="text-nowrap">{contact.age}</CTableDataCell>
                        <CTableDataCell className="text-nowrap">{contact.location}</CTableDataCell>
                        <CTableDataCell className="text-nowrap">{contact.outstanding_amount}</CTableDataCell>
                        <CTableDataCell className="text-nowrap">{contact.due_date}</CTableDataCell>
                        <CTableDataCell className="text-nowrap">{contact.principal_amount}</CTableDataCell>
                        <CTableDataCell className="text-nowrap">{contact.type}</CTableDataCell>
                        <CTableDataCell className="flex gap-2">
                          <CButton color="primary" onClick={() => handleCallRequest(contact)}>
                            <CIcon icon={cilPhone} />
                          </CButton>
                          <CButton color="primary" onClick={() => handleEmailRequest(contact)}>
                            <CIcon icon={cilEnvelopeClosed} />
                          </CButton>
                        </CTableDataCell>
                      </CTableRow>
                    ))}
                  </CTableBody>
                </CTable>
              )}
            </CCardBody>
          </CCard>
        </CCol>
      </CRow>
      <CRow className="justify-content-center mt-3">
        <CCol xs="auto">
          <CButton color="primary" onClick={startAutopilot}>
            Start Autopilot
          </CButton>
        </CCol>
      </CRow>
      <ImportModal show={showImportModal} onClose={handleCloseModal} onUpload={handleUpload} />
    </>
  )
}

export default Contacts
