import os

from flask import Blueprint, jsonify, g, request

from ..utils.supabase.queries import get_org_by_id, upsert_org_metadata
from ..core.localstorage import inputs_storage, email_inputs_storage

orgs_bp = Blueprint("orgs", __name__)


@orgs_bp.route("/<org_id>", methods=["GET", "POST", "OPTIONS"])
def orgs(org_id=None):
    if request.method == "OPTIONS":
        return jsonify({}), 204

    org_id == "8241d390-a8b5-4f59-b0a9-b95c074db3f5"

    if request.method == "GET":
        org = get_org_by_id(org_id).data[0]
        return jsonify({"data": org}), 200

    if request.method == "POST":
        org_metadata = get_org_by_id(org_id).data[0].get("metadata")
        if not org_metadata:
            org_metadata = {}

        data = request.get_json()
        new_metadata = data.get("metadata")

        # Store old verification types for comparison
        old_verification_types = org_metadata.get("verification_types", [])
        old_fields = {vt["field"] for vt in old_verification_types}

        # Update metadata
        if new_metadata.get("type"):
            org_metadata["type"] = new_metadata["type"]
        if new_metadata.get("strategy"):
            org_metadata["strategy"] = new_metadata["strategy"]
        if new_metadata.get("settlement_plans"):
            org_metadata["settlement_plans"] = new_metadata["settlement_plans"]
        if new_metadata.get("promise_failure_policy"):
            org_metadata["promise_failure_policy"] = new_metadata[
                "promise_failure_policy"
            ]
        if new_metadata.get("call_forwarding"):
            org_metadata["call_forwarding"] = new_metadata["call_forwarding"]
        if new_metadata.get("communication_channels"):
            org_metadata["communication_channels"] = new_metadata[
                "communication_channels"
            ]

        if new_metadata.get("verification_types"):
            print(new_metadata["verification_types"])
            org_metadata["verification_types"] = new_metadata["verification_types"]
        else:
            org_metadata["verification_types"] = []

        try:
            upsert_org_metadata(org_id, org_metadata)
            return jsonify({"data": {"metadata": org_metadata}}), 200
        except Exception as e:
            return jsonify({"error": "Could not update this org"}), 500
