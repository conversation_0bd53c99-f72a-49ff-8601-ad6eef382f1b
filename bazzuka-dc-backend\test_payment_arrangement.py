#!/usr/bin/env python3
"""
Test script for the payment arrangement creation API endpoint
"""

import requests
import json
from datetime import datetime, timedelta

# Configuration
BASE_URL = "http://localhost:5000"  # Adjust this to your backend URL
API_ENDPOINT = f"{BASE_URL}/v0/payments/create-arrangement"

def test_payment_arrangement_creation():
    """Test the payment arrangement creation endpoint"""
    
    # Test data
    tomorrow = (datetime.now() + timedelta(days=1)).strftime("%Y-%m-%d")
    
    test_cases = [
        {
            "name": "One-time payment",
            "data": {
                "defaulter_id": "test-defaulter-1",
                "amount": 100.00,
                "recurrence": "one-time",
                "discount": 0,
                "due_date": tomorrow
            }
        },
        {
            "name": "Monthly recurring payment with discount",
            "data": {
                "defaulter_id": "test-defaulter-2",
                "amount": 500.00,
                "recurrence": "monthly",
                "discount": 10.0,
                "due_date": tomorrow
            }
        },
        {
            "name": "Weekly payment",
            "data": {
                "defaulter_id": "test-defaulter-3",
                "amount": 50.00,
                "recurrence": "weekly",
                "discount": 5.0,
                "due_date": tomorrow
            }
        }
    ]
    
    print("Testing Payment Arrangement Creation API")
    print("=" * 50)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\nTest {i}: {test_case['name']}")
        print(f"Request data: {json.dumps(test_case['data'], indent=2)}")
        
        try:
            response = requests.post(
                API_ENDPOINT,
                json=test_case['data'],
                headers={
                    'Content-Type': 'application/json',
                    'ngrok-skip-browser-warning': 'true'
                },
                timeout=30
            )
            
            print(f"Status Code: {response.status_code}")
            
            if response.status_code == 200 or response.status_code == 201:
                result = response.json()
                print(f"Success: {result.get('message', 'No message')}")
                print(f"Details: {result.get('details', 'No details')}")
            else:
                print(f"Error: {response.text}")
                
        except requests.exceptions.RequestException as e:
            print(f"Request failed: {e}")
        except json.JSONDecodeError as e:
            print(f"JSON decode error: {e}")
            print(f"Response text: {response.text}")
        
        print("-" * 30)

def test_validation_errors():
    """Test validation error cases"""
    
    print("\n\nTesting Validation Errors")
    print("=" * 50)
    
    error_cases = [
        {
            "name": "Missing required field",
            "data": {
                "defaulter_id": "test-defaulter",
                "amount": 100.00,
                "recurrence": "one-time"
                # Missing due_date
            }
        },
        {
            "name": "Invalid amount",
            "data": {
                "defaulter_id": "test-defaulter",
                "amount": -50.00,
                "recurrence": "one-time",
                "discount": 0,
                "due_date": "2024-01-15"
            }
        },
        {
            "name": "Invalid discount",
            "data": {
                "defaulter_id": "test-defaulter",
                "amount": 100.00,
                "recurrence": "one-time",
                "discount": 150.0,  # > 100%
                "due_date": "2024-01-15"
            }
        },
        {
            "name": "Past due date",
            "data": {
                "defaulter_id": "test-defaulter",
                "amount": 100.00,
                "recurrence": "one-time",
                "discount": 0,
                "due_date": "2020-01-01"  # Past date
            }
        }
    ]
    
    for i, test_case in enumerate(error_cases, 1):
        print(f"\nError Test {i}: {test_case['name']}")
        print(f"Request data: {json.dumps(test_case['data'], indent=2)}")
        
        try:
            response = requests.post(
                API_ENDPOINT,
                json=test_case['data'],
                headers={
                    'Content-Type': 'application/json',
                    'ngrok-skip-browser-warning': 'true'
                },
                timeout=30
            )
            
            print(f"Status Code: {response.status_code}")
            
            if response.status_code == 400:
                result = response.json()
                print(f"Expected error: {result.get('error', 'No error message')}")
            else:
                print(f"Unexpected response: {response.text}")
                
        except requests.exceptions.RequestException as e:
            print(f"Request failed: {e}")
        
        print("-" * 30)

if __name__ == "__main__":
    print("Payment Arrangement API Test Suite")
    print("Make sure your backend server is running before executing this test.")
    print()
    
    # Test successful cases
    test_payment_arrangement_creation()
    
    # Test error cases
    test_validation_errors()
    
    print("\nTest suite completed!") 