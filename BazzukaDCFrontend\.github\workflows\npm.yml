name: NPM Installation

on:
  push:
    branches-ignore:
      - "dependabot/**"
  schedule:
  - cron: '0 0 * * *'

env:
  FORCE_COLOR: 2
  NODE: 16

jobs:
  build:
    strategy:
      matrix:
        platform: [ubuntu-latest, windows-latest]
        node-version: [16.x, 17.x, 18.x]
    runs-on: ${{ matrix.platform }}
    steps:
      - name: Clone repository
        uses: actions/checkout@v3

      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: ${{ env.node-version }}

      - name: Install npm dependencies
        run: npm install

      - name: Run build
        run: npm run build
