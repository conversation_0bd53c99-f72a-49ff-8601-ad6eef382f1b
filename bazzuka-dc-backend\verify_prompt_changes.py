#!/usr/bin/env python3
"""
Simple verification script to check if prompt improvements were applied correctly.
This script only checks the prompt content without running the full agent.
"""

import sys
from pathlib import Path

def verify_prompt_improvements():
    """Verify that the prompt improvements are in place."""
    
    print("🔍 Verifying Communication Preferences Prompt Improvements")
    print("=" * 60)
    
    try:
        # Read the comm_prefs_agent.py file directly
        agent_file = Path("app/core/ai/comm_prefs_agent.py")
        
        if not agent_file.exists():
            print("❌ Could not find app/core/ai/comm_prefs_agent.py")
            return False
        
        with open(agent_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for specific improvements
        improvements = [
            {
                "name": "CRITICAL warning at the top",
                "check": "CRITICAL: Only call tools for EXPLICIT, DIRECT requests",
                "found": False
            },
            {
                "name": "Negative examples section header",
                "check": "Common False Positive Scenarios - DO NOT Call Tools For:",
                "found": False
            },
            {
                "name": "Specific busy example",
                "check": "I'm busy right now",
                "found": False
            },
            {
                "name": "Specific annoying example", 
                "check": "This is annoying",
                "found": False
            },
            {
                "name": "Follow-up scheduling example",
                "check": "Call me tomorrow at 7 PM",
                "found": False
            },
            {
                "name": "Key principle statement",
                "check": "Key principle: If the debtor does not use explicit opt-out language",
                "found": False
            },
            {
                "name": "When in doubt guidance",
                "check": "When in doubt, DO NOT call any tools",
                "found": False
            }
        ]
        
        # Check each improvement
        for improvement in improvements:
            if improvement["check"] in content:
                improvement["found"] = True
        
        # Report results
        found_count = sum(1 for imp in improvements if imp["found"])
        total_count = len(improvements)
        
        print(f"📊 Prompt Improvements Found: {found_count}/{total_count}")
        print()
        
        for improvement in improvements:
            status = "✅" if improvement["found"] else "❌"
            print(f"{status} {improvement['name']}")
        
        if found_count == total_count:
            print(f"\n🎉 All prompt improvements successfully applied!")
            success = True
        else:
            print(f"\n⚠️ Only {found_count}/{total_count} improvements found")
            success = False
        
        # Show a snippet of the improved prompt
        print(f"\n📄 PROMPT PREVIEW:")
        print("-" * 40)
        
        # Find the prompt section
        prompt_start = content.find('self.prompt = """')
        if prompt_start != -1:
            prompt_start += len('self.prompt = """')
            prompt_end = content.find('"""', prompt_start)
            if prompt_end != -1:
                prompt_content = content[prompt_start:prompt_end]
                # Show first 600 characters
                preview = prompt_content[:600] + "..." if len(prompt_content) > 600 else prompt_content
                print(preview)
            else:
                print("Could not find end of prompt")
        else:
            print("Could not find prompt in file")
        
        print("-" * 40)
        
        return success
        
    except Exception as e:
        print(f"❌ Error reading prompt file: {e}")
        return False


def verify_temperature_changes():
    """Verify that temperature=0.0 was added to AI client."""
    
    print(f"\n🌡️ Verifying Temperature Changes")
    print("-" * 30)
    
    try:
        client_file = Path("app/ai/client.py")
        
        if not client_file.exists():
            print("❌ Could not find app/ai/client.py")
            return False
        
        with open(client_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Count temperature=0.0 occurrences
        temp_count = content.count("temperature=0.0")
        
        if temp_count >= 3:  # Should be at least 3 OpenAI calls with temperature
            print(f"✅ Temperature=0.0 found in {temp_count} locations")
            return True
        else:
            print(f"⚠️ Temperature=0.0 found in only {temp_count} locations (expected at least 3)")
            return False
            
    except Exception as e:
        print(f"❌ Error reading client file: {e}")
        return False


def show_testing_instructions():
    """Show instructions for testing the improvements."""
    
    print(f"\n🧪 TESTING INSTRUCTIONS")
    print("=" * 30)
    print("Now that the prompt improvements are in place, test them by:")
    print()
    print("1. 🗣️ Have conversations with Debby where you:")
    print("   - Say 'I'm busy right now'")
    print("   - Say 'This is so annoying'") 
    print("   - Say 'Can you call me back tomorrow at 7 PM?'")
    print("   - Say 'I can't talk right now, I'm at work'")
    print("   - Ask 'How much do I owe?'")
    print()
    print("2. ✅ Verify that NO restriction tools are called for the above")
    print()
    print("3. 🧪 Test that explicit opt-outs still work:")
    print("   - Say 'Please stop calling me'")
    print("   - Say 'Only call me after 6 PM'")
    print("   - Verify that tools ARE called for these")
    print()
    print("4. 📊 Compare before/after false positive rates")


def main():
    print("🚀 Prompt Improvements Verification")
    print("=" * 40)
    
    # Check if we're in the right directory
    if not Path("app").exists():
        print("❌ Please run this script from the bazzuka-dc-backend directory")
        print(f"   Current directory: {Path.cwd()}")
        return
    
    # Verify prompt improvements
    prompt_success = verify_prompt_improvements()
    
    # Verify temperature changes
    temp_success = verify_temperature_changes()
    
    # Overall result
    if prompt_success and temp_success:
        print(f"\n🎉 ALL IMPROVEMENTS SUCCESSFULLY APPLIED!")
        print("✅ Enhanced prompt with negative examples")
        print("✅ Temperature set to 0.0 for reproducibility")
        
        show_testing_instructions()
        
    else:
        print(f"\n⚠️ Some improvements may not have been applied correctly")
        if not prompt_success:
            print("❌ Prompt improvements incomplete")
        if not temp_success:
            print("❌ Temperature changes incomplete")


if __name__ == "__main__":
    main()
