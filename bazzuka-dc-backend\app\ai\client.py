import json

from pydantic import BaseModel

from .tools import ToolEngine
from .utils import (
    get_content,
    wrap_message,
    get_tool_calls,
    tool_call_msg,
    tool_output_msg,
)

from typing import List


class PromptGenerator:
    prompt: str

    inputs_prologue: str
    inputs: str

    def make_inputs(self, args):
        raise NotImplementedError

    def generate(self, args) -> List[str]:
        messages = []
        messages.insert(0, wrap_message("system", self.prompt))
        messages.append(
            wrap_message(
                "user",
                self.inputs_prologue
                + self.make_inputs(args if isinstance(args, dict) else {}),
            )
        )
        # print(messages)
        return messages


class Action:
    tool_engine: ToolEngine
    prompt_generator: PromptGenerator
    structured_output_type: BaseModel = None


class ThreadSafeActionBuilder:
    """
    Builder pattern to maintain backwards compatibility with the fluent API
    while being thread-safe by avoiding shared state.
    """

    def __init__(self, client, action_name):
        self.client = client
        self.action_name = action_name
        self.prompt_args = {}

    def with_context(self, prompt_args: dict):
        self.prompt_args = prompt_args
        return self

    def execute(self):
        return self.client.execute_action(self.action_name, self.prompt_args)


"""
Thread-safe AI client - all features using AI should interface with this class.
Eliminates race conditions by removing shared mutable state and passing context
through the call chain instead.
"""


class AIClient:
    def __init__(self, openai_client, model="gpt-4.1"):
        self.model = openai_client
        self.actions = {}  # Read-only after initialization, so thread-safe
        self.model_name = model
        # Removed _curr_action and _curr_args - these caused the race conditions

    def _handle_multistep_response(self, messages, action, args):
        """
        Handle the response from the completions model.
        Now takes action and args as parameters instead of using shared state.
        This eliminates race conditions in multi-threaded environments.
        """
        # print(messages)
        if action.structured_output_type is None:
            if (
                self.model_name == "gpt-4.1"
                or self.model_name == "gpt-4.1-mini"
                or self.model_name == "gpt-4o-mini"
            ):
                response = self.model.chat.completions.create(
                    model=self.model_name,
                    messages=messages,
                    tools=self._get_tools(action) if self.actions else None,
                )
            else:
                response = self.model.chat.completions.create(
                    model=self.model_name,
                    messages=messages,
                )
        else:
            response = self.model.beta.chat.completions.parse(
                model=self.model_name,
                messages=messages,
                response_format=action.structured_output_type,
            )

        tool_calls = get_tool_calls(response)
        if tool_calls:
            print("Tool calls!!!!!!!")
            print(tool_calls)
            # Append tool call response to conversation
            messages.append(tool_call_msg(tool_calls))

            # Append all the data to the conversation
            for tool in tool_calls:
                func_name = tool.function.name
                func_args = json.loads(tool.function.arguments)
                print(f"Executing tool: {func_name} with args: {func_args}")
                result = action.tool_engine.execute(func_name, func_args)
                tool_output = tool_output_msg(tool.id, result)
                print(f"Tool result: {tool_output}")
                messages.append(tool_output)

            # Send another call to the model (recursive with same action/args)
            return self._handle_multistep_response(messages, action, args)
        else:
            try:
                if action.structured_output_type:
                    return response.choices[0].message.parsed.to_dict()
                else:
                    return get_content(response)
            except Exception:
                return get_content(response)

    def complete(self, messages=[], prompt=None, next_msg=None, action=None, args=None):
        """
        Complete a conversation with optional prompt and next message.
        For tool-based completions, action and args must be provided.
        """
        if prompt:
            messages.insert(0, wrap_message("system", prompt))

        if next_msg:
            messages.append(wrap_message("user", next_msg))

        if action and args:
            return self._handle_multistep_response(messages, action, args)
        else:
            # Simple completion without tools - for backwards compatibility
            # This path doesn't support tools since we don't have action context
            response = self.model.chat.completions.create(
                model=self.model_name,
                messages=messages,
            )
            return get_content(response)

    def register(self, name, action):
        self.actions[name] = action

    def _get_prompt(self, action, args):
        return action.prompt_generator.generate(args)

    def _get_tools(self, action):
        if action is None or action.tool_engine is None:
            return None
        return action.tool_engine.get_tools()

    def list_actions(self):
        return list(self.actions.keys())

    # New thread-safe API - single method call with all context
    def execute_action(self, action_name: str, prompt_args: dict):
        """
        Thread-safe execution method that takes all context as parameters.
        No shared mutable state - everything is passed through the call stack.

        Recommended for new code as it's more explicit and thread-safe.
        """
        action = self.actions[action_name]
        prompt = self._get_prompt(action, prompt_args)
        result = self._handle_multistep_response(prompt, action, prompt_args)
        return result

    # Legacy API compatibility (now thread-safe via builder pattern)
    def do(self, action):
        """
        Backwards-compatible fluent API that's now thread-safe.
        Returns a builder that captures the action and args without shared state.
        """
        return ThreadSafeActionBuilder(self, action)

    # Legacy method for backwards compatibility (but not recommended)
    def with_context(self, prompt_args: dict):
        """
        Deprecated: Use execute_action() instead for better thread safety.
        This method is kept for backwards compatibility but creates a temporary builder.
        """
        # This is tricky - we don't know which action was called before this
        # So we'll need to track it somehow or deprecate this usage pattern
        raise NotImplementedError(
            "with_context() without do() is deprecated. Use execute_action() or do().with_context() pattern."
        )

    def execute(self):
        """
        Deprecated: Use execute_action() instead for better thread safety.
        This method is kept for backwards compatibility but has no context.
        """
        raise NotImplementedError(
            "execute() without context is deprecated. Use execute_action() or do().with_context().execute() pattern."
        )
