from flask import Blueprint, jsonify, request
from app.core.background_tasks import background_task_manager
import threading

status_bp = Blueprint("status", __name__)


@status_bp.route("/background-tasks", methods=["GET"])
def get_background_task_status():
    """Get the status of background task processing"""

    # Get active thread count
    active_threads = threading.active_count()

    # Get background task manager info
    status_info = {
        "active_threads": active_threads,
        "max_workers": background_task_manager.max_workers,
        "task_manager_status": "operational",
    }

    return jsonify(status_info), 200


@status_bp.route("/health", methods=["GET"])
def health_check():
    """Simple health check endpoint"""
    return jsonify({"status": "healthy", "service": "BazzukaDC Backend"}), 200
