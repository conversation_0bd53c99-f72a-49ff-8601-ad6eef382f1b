from datetime import datetime
import math
import time
import stripe

from flask import Blueprint, jsonify, request, render_template

from authorizenet import apicontractsv1
from authorizenet.apicontrollers import (
    createTransactionController,
    ARBCreateSubscriptionController,
)
from app.utils.supabase.queries import (
    mark_defaulter_as_active,
    update_payment_status,
    approve_payment,
    delete_action_items,
    update_payment,
    update_action_item,
    reject_payment,
    get_pending_payments,
    get_action_items_by_defaulter_id,
    get_approved_payments,
    get_payments_by_issue_id,
    get_payments,
    get_payment,
    set_payment_status,
    delete_action_item_for_payment,
    update_issue,
    get_issue_by_id,
    get_org,
    update_customizations,
    get_defaulter_by_id,
    get_issues_by_defaulter_id,
    update_issue_status,
    mark_action_items_approved,
    delete_payment,
    get_transaction_history_with_details,
    get_transaction_count,
    get_payment_summary_stats,
    mark_payment_as_paid,
    get_payment_link_details,
    update_payment_link_status,
    get_transactions_by_status,
    get_payment_analytics,
)
from nylas import Client as NylasClient
from app.core.stripe import create_stripe_subscription, create_invoice
from app.core.payments import send_payment_link

import os
from decimal import Decimal

from app.core.ai import make_negotiation_gen_client

negotiation_gen_client = make_negotiation_gen_client()

payments_bp = Blueprint("payments", __name__)

# TODO: remove this
transactionKey = "287nCgxD763j7rL6"
apiLoginId = "9Lu8WK4nq"

import re


def parse_money_to_cents(text: str) -> int:
    """
    Converts a string like "$1,234.56" to an integer value in cents: 123456
    """
    # Remove anything that's not a digit or dot
    clean = re.sub(r"[^\d.]", "", text)

    # Handle decimal point if it exists
    if "." in clean:
        dollars, cents = clean.split(".")
        cents = (cents + "00")[:2]  # pad/truncate to 2 digits
    else:
        dollars, cents = clean, "00"

    return int(dollars) * 100 + int(cents)


def format_cents_to_money(cents: int) -> str:
    """
    Converts an integer number of cents (e.g. 123456) into a money string (e.g. "$1,234.56")
    """
    # Extract dollar and cent parts as integers
    dollars = cents // 100
    cents_part = cents % 100

    # Format dollars with comma separators, then append cents
    dollars_str = f"{dollars:,}"
    cents_str = f"{cents_part:02d}"

    return f"${dollars_str}.{cents_str}"


def create_merchant_auth():
    """Create merchant authentication object"""
    merchantAuth = apicontractsv1.merchantAuthenticationType()
    merchantAuth.name = apiLoginId
    merchantAuth.transactionKey = transactionKey
    return merchantAuth


def create_credit_card(form):
    """Create credit card object from form data"""
    creditCard = apicontractsv1.creditCardType()
    creditCard.cardNumber = form.get("card_number")
    month = form.get("expiry_month")
    year = form.get("expiry_year")
    creditCard.expirationDate = f"{year}-{month}"
    creditCard.cardCode = form.get("card_code")
    return creditCard


def create_customer_address(form):
    """Create customer address object from form data"""
    customerAddress = apicontractsv1.customerAddressType()
    customerAddress.firstName = form.get("first_name")
    customerAddress.lastName = form.get("last_name")
    customerAddress.address = form.get("address")
    customerAddress.city = form.get("city")
    customerAddress.state = form.get("state")
    customerAddress.zip = form.get("zip")
    customerAddress.country = form.get("country")
    return customerAddress


def handle_subscription_payment(
    payment_info, merchantAuth, payment, customerAddress, outstanding_amount
):
    """Handle recurring payment using subscription API"""
    paymentschedule = apicontractsv1.paymentScheduleType()
    paymentschedule.interval = apicontractsv1.paymentScheduleTypeInterval()
    paymentschedule.interval.length = 30  # Monthly subscription
    paymentschedule.interval.unit = apicontractsv1.ARBSubscriptionUnitEnum.days
    paymentschedule.startDate = datetime.now().date()
    paymentschedule.totalOccurrences = math.ceil(
        outstanding_amount / payment_info.get("amount")
    )
    paymentschedule.trialOccurrences = 0

    # Create billing info from customer address
    billto = apicontractsv1.nameAndAddressType()
    billto.firstName = customerAddress.firstName
    billto.lastName = customerAddress.lastName
    billto.address = customerAddress.address
    billto.city = customerAddress.city
    billto.state = customerAddress.state
    billto.zip = customerAddress.zip
    billto.country = customerAddress.country

    subscription = apicontractsv1.ARBSubscriptionType()
    subscription.name = f"Payment Plan for Issue {payment_info.get('issue_id')}"
    subscription.paymentSchedule = paymentschedule
    subscription.amount = Decimal(str(payment_info.get("amount") / 100))
    subscription.trialAmount = Decimal("0.00")
    subscription.billTo = billto
    subscription.payment = payment

    request = apicontractsv1.ARBCreateSubscriptionRequest()
    request.merchantAuthentication = merchantAuth
    request.subscription = subscription

    controller = ARBCreateSubscriptionController(request)
    controller.execute()
    return controller.getresponse()


def handle_one_time_payment(payment_info, merchantAuth, payment, customerAddress):
    """Handle one-time payment"""
    transactionrequest = apicontractsv1.transactionRequestType()
    transactionrequest.transactionType = "authCaptureTransaction"
    transactionrequest.amount = payment_info.get("amount") / 100
    transactionrequest.payment = payment
    transactionrequest.billTo = customerAddress

    createtransactionrequest = apicontractsv1.createTransactionRequest()
    createtransactionrequest.merchantAuthentication = merchantAuth
    createtransactionrequest.refId = "MerchantID-0001"
    createtransactionrequest.transactionRequest = transactionrequest

    createtransactioncontroller = createTransactionController(createtransactionrequest)
    createtransactioncontroller.execute()
    return createtransactioncontroller.getresponse()


def update_issue_and_payment_status(
    payment_id, payment_info, transaction_id, issue=None
):
    """Update issue and payment status after successful payment"""
    if payment_info.get("recurring"):
        update_payment_status(payment_id, "in-progress", transaction_id=transaction_id)
    else:
        set_payment_status(payment_id, "paid", transaction_id=transaction_id)

    if issue is None:
        issue = get_issue_by_id(payment_info.get("issue_id")).data[0]
    defaulter_id = issue.get("defaulter_id")
    issue_outstanding_balance = parse_money_to_cents(issue.get("outstanding_amount"))
    issue_outstanding_balance -= payment_info.get("amount")

    if issue_outstanding_balance == 0 or (
        payment_info.get("settlement_discount") and not payment_info.get("recurring")
    ):
        update_issue(
            issue.get("issue_id"), {"status": "solved", "outstanding_amount": "$0.00"}
        )
    else:
        update_issue(
            issue.get("issue_id"),
            {"outstanding_amount": format_cents_to_money(issue_outstanding_balance)},
        )

    issues = get_issues_by_defaulter_id(defaulter_id).data
    all_issues_solved = all(issue.get("status") == "solved" for issue in issues)
    if all_issues_solved:
        delete_action_items(defaulter_id)
        return True

    org = get_org().data[0].get("metadata")
    case_info = get_defaulter_by_id(defaulter_id).data[0]
    cases = get_issues_by_defaulter_id(defaulter_id).data
    case_info["cases"] = cases
    new_strategy = (
        negotiation_gen_client.do("generate_negotiation_prompt")
        .with_context(
            {
                "case_info": case_info,
                "policy": org.get("strategy"),
            }
        )
        .execute()
    )
    update_customizations(defaulter_id, {"negotiation_strategy": new_strategy})
    return False


@payments_bp.route("/payments", methods=["POST", "GET", "OPTIONS"])
def payments():
    if request.method == "OPTIONS":
        return jsonify({"message": "ok"}), 200

    if request.method == "GET":
        try:
            if request.args.get("approved") == "false":
                print("[DEBUG] Fetching pending payments for approvals...")
                payments_result = get_pending_payments()
                payments = payments_result.data if payments_result.data else []
                print(f"[DEBUG] Found {len(payments)} pending payments")

                # Process each payment with error handling
                valid_payments = []
                for payment in payments:
                    try:
                        print(f"[DEBUG] Processing payment {payment['id']}")
                        
                        # Get issue data
                        issue_result = get_issue_by_id(payment["issue_id"])
                        if not issue_result.data:
                            print(f"[ERROR] No issue found for payment {payment['id']}")
                            continue
                        issue = issue_result.data[0]
                        payment["issue"] = issue
                        
                        # Get defaulter data
                        defaulter_result = get_defaulter_by_id(issue["defaulter_id"])
                        if not defaulter_result.data:
                            print(f"[ERROR] No defaulter found for issue {issue['issue_id']}")
                            continue
                        payment["defaulter"] = defaulter_result.data[0]
                        
                        # Get action items and related payments
                        payment["actionitems"] = get_action_items_by_defaulter_id(issue["defaulter_id"]).data
                        payment["payments"] = get_payments_by_issue_id(issue["issue_id"]).data

                        # --- Add interval, interval_count, and number_of_payments ---
                        # Try to extract from payment['type'] if present (e.g., 'recurring_week_1')
                        payment_type = payment.get('type', '')
                        interval = None
                        interval_count = None
                        if payment.get('recurring') and payment_type.startswith('recurring_'):
                            parts = payment_type.split('_')
                            if len(parts) >= 3:
                                interval = parts[1]
                                try:
                                    interval_count = int(parts[2])
                                except Exception:
                                    interval_count = 1
                        # Fallbacks
                        if not interval:
                            interval = payment.get('interval', None)
                        if not interval_count:
                            interval_count = payment.get('interval_count', 1)
                        payment['interval'] = interval
                        payment['interval_count'] = interval_count
                        # Number of payments: count related payments for this issue that are recurring and pending/active
                        related_payments = payment["payments"]
                        if payment.get('recurring'):
                            # Only count recurring payments for this plan/issue
                            num_payments = len([p for p in related_payments if p.get('recurring')])
                        else:
                            num_payments = 1
                        payment['number_of_payments'] = num_payments
                        # --- End add ---

                        valid_payments.append(payment)
                        print(f"[DEBUG] Successfully processed payment {payment['id']}")
                        
                    except Exception as e:
                        print(f"[ERROR] Failed to process payment {payment['id']}: {e}")
                        continue
                
                payments = valid_payments
                print(f"[DEBUG] Returning {len(payments)} valid payments")

            elif request.args.get("approved") == "true":
                payments = get_approved_payments().data
            else:
                payments = get_payments().data

            return jsonify({"payments": payments}), 200
            
        except Exception as e:
            print(f"[ERROR] Failed to fetch payments: {e}")
            return jsonify({"error": "Failed to fetch payments", "details": str(e)}), 500

    form = request.form
    payment_id = form.get("payment_id")
    payment_info = get_payment(payment_id).data[0]

    if payment_info.get("status") == "paid":
        return jsonify({"message": "payment already paid"}), 400

    # Setup payment objects
    merchantAuth = create_merchant_auth()
    creditCard = create_credit_card(form)
    payment = apicontractsv1.paymentType()
    payment.creditCard = creditCard
    customerAddress = create_customer_address(form)

    # Get issue data once and reuse it
    issue = get_issue_by_id(payment_info.get("issue_id")).data[0]

    # Process payment based on type
    if payment_info.get("recurring"):
        outstanding_amount = parse_money_to_cents(issue.get("outstanding_amount"))
        response = handle_subscription_payment(
            payment_info, merchantAuth, payment, customerAddress, outstanding_amount
        )
        if response is None:
            return jsonify({"message": "null response"}), 400

        if response.messages.resultCode != "Ok":
            print("Failed to create subscription.")
            print("Error Code: %s" % response.messages.message[0]["code"].text)
            print("Error message: %s" % response.messages.message[0]["text"].text)
            return jsonify({"message": "Failed to create subscription"}), 400

        print(
            "Successfully created subscription with Subscription ID: %s"
            % response.subscriptionId
        )
        print("Message Code: %s" % response.messages.message[0]["code"].text)
        print("Message text: %s" % response.messages.message[0]["text"].text)

        transaction_id = str(response.subscriptionId)
    else:
        response = handle_one_time_payment(
            payment_info, merchantAuth, payment, customerAddress
        )
        if response is None:
            return jsonify({"message": "null response"}), 400

        if response.messages.resultCode != "Ok":
            print("Failed Transaction.")
            if hasattr(response, "transactionResponse") and hasattr(
                response.transactionResponse, "errors"
            ):
                print(
                    "Error Code: %s"
                    % str(response.transactionResponse.errors.error[0].errorCode)
                )
                print(
                    "Error message: %s"
                    % response.transactionResponse.errors.error[0].errorText
                )
            else:
                print("Error Code: %s" % response.messages.message[0]["code"].text)
                print("Error message: %s" % response.messages.message[0]["text"].text)
            return jsonify({"message": "transaction failed"}), 400

        if hasattr(response.transactionResponse, "messages"):
            print(
                "Successfully created transaction with Transaction ID: %s"
                % response.transactionResponse.transId
            )
            print(
                "Transaction Response Code: %s"
                % response.transactionResponse.responseCode
            )
            print(
                "Message Code: %s"
                % response.transactionResponse.messages.message[0].code
            )
            print(
                "Description: %s"
                % response.transactionResponse.messages.message[0].description
            )
        else:
            print("Failed Transaction.")
            if hasattr(response.transactionResponse, "errors"):
                print(
                    "Error Code: %s"
                    % str(response.transactionResponse.errors.error[0].errorCode)
                )
                print(
                    "Error message: %s"
                    % response.transactionResponse.errors.error[0].errorText
                )
            return jsonify({"message": "transaction failed"}), 400

        transaction_id = str(response.transactionResponse.transId)

    # Update status and return success
    all_issues_solved = update_issue_and_payment_status(
        payment_id, payment_info, transaction_id, issue
    )
    if all_issues_solved:
        return jsonify({"message": "payment successful"}), 200

    return jsonify({"message": "payment successful"}), 200


@payments_bp.route("/pay/<payment_id>", methods=["GET", "OPTIONS"])
def pay(payment_id):
    """
    Legacy payment route - now redirects to Stripe hosted invoice URL.
    This maintains backward compatibility for any existing payment links.
    """
    from flask import redirect

    if request.method == "OPTIONS":
        return jsonify({"message": "ok"}), 200

    payment = get_payment(payment_id)

    if not payment.data:
        return jsonify({"message": "payment not found"}), 404

    payment_data = payment.data[0]

    if payment_data["_approved"] == "pending":
        return jsonify({
            "message": "This payment link is pending approval. Please check back later."
        }), 200

    if payment_data["status"] == "paid":
        return jsonify({"message": "payment already paid"}), 400

    # Get the Stripe invoice URL
    stripe_url = payment_data.get("url")

    if stripe_url:
        # Redirect to Stripe hosted invoice page
        return redirect(stripe_url)
    else:
        # If no Stripe URL exists, this is an error condition
        return jsonify({
            "message": "Payment link not available. Please contact support.",
            "error": "No Stripe invoice URL found for this payment."
        }), 400


NYLAS_API_KEY = os.environ.get("NYLAS_API_KEY", "")
NYLAS_CLIENT_ID = os.environ.get("NYLAS_CLIENT_ID", "")
NYLAS_API_URI = os.environ.get("NYLAS_API_URI", "https://api.us.nylas.com")

nylas = NylasClient(api_key=NYLAS_API_KEY, api_uri=NYLAS_API_URI)


def send(data):
    subject = data["subject"]
    text = data["text"]
    recipient_name = data.get("recipientName", "New Customer")
    recipient_address = data["recipientAddress"]

    # Temporarily hardcoded grant ID
    grant_id = "490ffcaa-0de8-4a0e-951b-c652b570e14d"

    # Construct a cleaner and more structured HTML email body
    body = {
        "subject": subject,
        "body": f"""
        <html>
            <head>
                <style>
                    body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
                    .container {{ padding: 20px; }}
                    a {{ color: #1a73e8; text-decoration: none; }}
                </style>
            </head>
            <body>
                <div class="container">
                    {text}
                </div>
            </body>
        </html>
        """,
        "to": [{"name": recipient_name, "email": recipient_address}],
    }

    if "reply_to_message_id" in data:
        body["reply_to_message_id"] = data["reply_to_message_id"]

    message = nylas.messages.send(grant_id, request_body=body).data

    return message.id


@payments_bp.route("/payments/<payment_id>/approve", methods=["OPTIONS", "POST"])
def approve_scheduled_payment(payment_id):
    if request.method == "OPTIONS":
        return jsonify({"message": "ok"}), 200

    try:
        # This is where a human will approve the AI-scheduled payment
        payment = get_payment(payment_id)
        if not payment.data:
            return jsonify({"message": "payment not found"}), 404

        # Check current status before approval
        current_status = payment.data[0].get('_approved')
        
        # Approve the payment (this handles both normal approval and cancellation)
        approval_result = approve_payment(payment_id)
        
        # If this was a cancellation approval, don't send payment links
        if current_status == 'pending_cancellation':
            return jsonify({"message": "payment cancellation approved"}), 200
        
        # For normal approvals, proceed with sending payment links
        issue = get_issue_by_id(payment.data[0]["issue_id"]).data[0]
        mark_defaulter_as_active(issue["defaulter_id"])

        # For recurring payments, do not pass due_date
        if payment.data[0].get("recurring", False):
            result = send_payment_link(
                payment_id, payment.data[0]["issue_id"], payment.data[0]["amount"]
            )
        else:
            # Extract due_date from payment record to pass to send_payment_link
            payment_due_date = payment.data[0].get("due_date")
            result = send_payment_link(
                payment_id, payment.data[0]["issue_id"], payment.data[0]["amount"], payment_due_date
            )

        # Check if send_payment_link returned an error
        if isinstance(result, str) and result.startswith("Error:"):
            return (
                jsonify(
                    {"message": "payment approved but email failed", "error": result}
                ),
                200,
            )

        return jsonify({"message": "payment approved"}), 200

    except Exception as e:
        print(f"Error in approve_scheduled_payment: {e}")
        return jsonify({"message": "error approving payment", "error": str(e)}), 500


@payments_bp.route("/payments/<payment_id>/edit", methods=["OPTIONS", "POST"])
def edit_scheduled_payment(payment_id):
    if request.method == "OPTIONS":
        return jsonify({"message": "ok"}), 200

    for actionitem in request.json.get("actionitems", []):
        # can edit date, time, channel or reason.
        update_action_item(
            actionitem["id"],
            action_date=actionitem.get("action_date"),
            action_time=actionitem.get("action_time"),
            action_channel=actionitem.get("action_channel"),
            action_reason=actionitem.get("action_reason"),
        )

    payment = request.json.get("payment")

    update_payment(payment["id"], amount=payment["amount"])

    return jsonify({"message": "payment edited"}), 200


@payments_bp.route("/payments/<payment_id>/reject", methods=["OPTIONS", "POST"])
def reject_scheduled_payment(payment_id):
    if request.method == "OPTIONS":
        return jsonify({"message": "ok"}), 200

    # This is where a human will reject the AI-scheduled payment
    # if not payment.data:
    #     return jsonify({"message": "payment not found"}), 404
    reject_payment(payment_id)
    return jsonify({"message": "payment rejected"}), 200


@payments_bp.route("/payments/<payment_id>", methods=["OPTIONS", "PUT"])
def update_payment_details(payment_id):
    if request.method == "OPTIONS":
        return jsonify({"message": "ok"}), 200

    try:
        data = request.json

        # Update payment with new details
        update_data = {}
        if "amount" in data:
            update_data["amount"] = data["amount"]
        if "recurring" in data:
            update_data["recurring"] = data["recurring"]
        if "settlement_discount" in data:
            update_data["settlement_discount"] = data["settlement_discount"]

        update_payment(payment_id, **update_data)

        return jsonify({"message": "payment updated successfully"}), 200
    except Exception as e:
        return jsonify({"error": str(e)}), 500


@payments_bp.route("/payments/<payment_id>", methods=["OPTIONS", "DELETE"])
def delete_payment_link(payment_id):
    if request.method == "OPTIONS":
        return jsonify({"message": "ok"}), 200

    try:
        # Check if payment exists
        payment = get_payment(payment_id)
        if not payment.data:
            return jsonify({"message": "payment not found"}), 404

        # Delete the payment
        delete_payment(payment_id)

        return jsonify({"message": "payment deleted successfully"}), 200
    except Exception as e:
        return jsonify({"error": str(e)}), 500


@payments_bp.route("/transactions", methods=["OPTIONS", "GET"])
def get_transaction_history():
    if request.method == "OPTIONS":
        return jsonify({"message": "ok"}), 200

    try:
        # Get query parameters for filtering
        start_date = request.args.get("start_date")
        end_date = request.args.get("end_date")
        debtor_id = request.args.get("debtor_id")
        status = request.args.get(
            "status"
        )  # Payment status (paid/unpaid/failed) - can be comma-separated
        if not status or status == "all":
            status = None
        limit = int(request.args.get("limit", 50))
        offset = int(request.args.get("offset", 0))
        search = request.args.get("search", "").strip()

        # Only show approved transactions
        approved_only = True
        transactions = get_transaction_history_with_details(
            start_date=start_date,
            end_date=end_date,
            debtor_id=debtor_id,
            status=status,
            limit=limit,
            offset=offset,
            search=search,
            approved_only=approved_only,
        )

        # Get total count for pagination
        total_count = get_transaction_count(
            start_date=start_date,
            end_date=end_date,
            debtor_id=debtor_id,
            status=status,
            search=search,
            approved_only=approved_only,
        )

        return (
            jsonify(
                {
                    "transactions": transactions.data if transactions.data else [],
                    "total_count": total_count,
                    "limit": limit,
                    "offset": offset,
                    "has_more": (offset + limit) < total_count,
                }
            ),
            200,
        )

    except Exception as e:
        print(f"Error fetching transaction history: {e}")
        return jsonify({"error": str(e)}), 500


@payments_bp.route("/transactions/summary", methods=["OPTIONS", "GET"])
def get_transaction_summary():
    if request.method == "OPTIONS":
        return jsonify({"message": "ok"}), 200

    try:
        # Get date range from query parameters
        start_date = request.args.get("start_date")
        end_date = request.args.get("end_date")

        # Get summary statistics
        summary = get_payment_summary_stats(start_date=start_date, end_date=end_date)

        return jsonify(summary), 200

    except Exception as e:
        print(f"Error fetching transaction summary: {e}")
        return jsonify({"error": str(e)}), 500


@payments_bp.route("/transactions/<transaction_id>/details", methods=["OPTIONS", "GET"])
def get_transaction_details(transaction_id):
    """Get detailed information about a specific transaction"""
    if request.method == "OPTIONS":
        return jsonify({"message": "ok"}), 200

    try:
        # Get detailed payment link information
        payment_details = get_payment_link_details(transaction_id)

        if not payment_details.data:
            return jsonify({"message": "transaction not found"}), 404

        return jsonify({"transaction": payment_details.data[0]}), 200

    except Exception as e:
        print(f"Error fetching transaction details: {e}")
        return jsonify({"error": str(e)}), 500


@payments_bp.route("/transactions/<transaction_id>/status", methods=["OPTIONS", "PUT"])
def update_transaction_status(transaction_id):
    """Update transaction status with optional notes"""
    if request.method == "OPTIONS":
        return jsonify({"message": "ok"}), 200

    try:
        data = request.json
        new_status = data.get("status")
        notes = data.get("notes")
        external_transaction_id = data.get("transaction_id")

        if not new_status:
            return jsonify({"error": "Status is required"}), 400

        # Valid statuses
        valid_statuses = ["pending", "paid", "failed", "refunded", "cancelled"]
        if new_status not in valid_statuses:
            return (
                jsonify(
                    {
                        "error": f"Invalid status. Must be one of: {', '.join(valid_statuses)}"
                    }
                ),
                400,
            )

        # Update the payment link status
        update_payment_link_status(
            transaction_id, new_status, external_transaction_id, notes
        )

        return jsonify({"message": "transaction status updated successfully"}), 200

    except Exception as e:
        print(f"Error updating transaction status: {e}")
        return jsonify({"error": str(e)}), 500


@payments_bp.route("/transactions/analytics", methods=["OPTIONS", "GET"])
def get_payment_analytics_endpoint():
    """Get comprehensive payment analytics"""
    if request.method == "OPTIONS":
        return jsonify({"message": "ok"}), 200

    try:
        # Get date range from query parameters
        start_date = request.args.get("start_date")
        end_date = request.args.get("end_date")

        # Get analytics data
        analytics = get_payment_analytics(start_date=start_date, end_date=end_date)

        return jsonify(analytics), 200

    except Exception as e:
        print(f"Error fetching payment analytics: {e}")
        return jsonify({"error": str(e)}), 500


# @payments_bp.route("/transactions/by-status/<status>", methods=["OPTIONS", "GET"])
# def get_transactions_by_status_endpoint(status):
#     """Get transactions filtered by status"""
#     if request.method == "OPTIONS":
#         return jsonify({"message": "ok"}), 200

#     try:
#         limit = int(request.args.get('limit', 50))

#         # Get transactions by status
#         transactions = get_transactions_by_status(status, limit)

#         return jsonify({
#             "transactions": transactions.data if transactions.data else [],
#             "status": status,
#             "count": len(transactions.data) if transactions.data else 0
#         }), 200

#     except Exception as e:
#         print(f"Error fetching transactions by status: {e}")
#         return jsonify({"error": str(e)}), 500


@payments_bp.route("/payments/<payment_id>/resend", methods=["POST", "OPTIONS"])
def resend_payment_link(payment_id):
    if request.method == "OPTIONS":
        return jsonify({"message": "ok"}), 200

    try:
        data = request.json
        issue_id = data.get("issue_id")
        amount = data.get("amount")
        if not issue_id or not amount:
            return jsonify({"error": "Missing issue_id or amount"}), 400

        result = send_payment_link(payment_id, issue_id, amount)
        if isinstance(result, str) and result.startswith("Error:"):
            return jsonify({"message": "email failed", "error": result}), 500

        return jsonify({"message": "payment link resent"}), 200
    except Exception as e:
        print(f"Error in resend_payment_link: {e}")
        return (
            jsonify({"message": "error resending payment link", "error": str(e)}),
            500,
        )


@payments_bp.route("/payments/create-arrangement", methods=["POST", "OPTIONS"])
def create_payment_arrangement():
    if request.method == "OPTIONS":
        return jsonify({"message": "ok"}), 200

    try:
        data = request.get_json()
        
        # Validate required fields
        required_fields = ["defaulter_id", "amount", "recurrence", "discount", "due_date"]
        for field in required_fields:
            if field not in data:
                return jsonify({"error": f"Missing required field: {field}"}), 400
        
        defaulter_id = data["defaulter_id"]
        amount = data["amount"]
        recurrence = data["recurrence"]  # "one-time", "weekly", "biweekly", "monthly"
        discount = data["discount"]
        due_date = data["due_date"]
        
        # Validate amount
        if not isinstance(amount, (int, float)) or amount <= 0:
            return jsonify({"error": "Amount must be a positive number"}), 400
        
        # Validate discount
        if not isinstance(discount, (int, float)) or discount < 0 or discount > 100:
            return jsonify({"error": "Discount must be between 0 and 100"}), 400
        
        # Validate due_date format
        try:
            datetime.strptime(due_date, "%Y-%m-%d")
        except ValueError:
            return jsonify({"error": "Due date must be in YYYY-MM-DD format"}), 400
        
        # Get issues for this defaulter
        issues_result = get_issues_by_defaulter_id(defaulter_id)
        if not issues_result.data:
            return jsonify({"error": "No issues found for this defaulter"}), 404
        
        # For now, use the first issue (you might want to add issue_id to the request)
        issue = issues_result.data[0]
        issue_id = issue["issue_id"]
        
        # Calculate amount with discount
        discounted_amount = amount * (1 - discount / 100)
        
        # Determine payment type based on recurrence
        recurring = recurrence != "one-time"
        interval = None
        interval_count = None
        
        if recurring:
            if recurrence == "weekly":
                interval = "week"
                interval_count = 1
            elif recurrence == "biweekly":
                interval = "week"
                interval_count = 2
            elif recurrence == "monthly":
                interval = "month"
                interval_count = 1
            else:
                return jsonify({"error": "Invalid recurrence type"}), 400
        
        # Create payment arrangement directly
        from app.core.payments import PaymentsTool
        
        payments_tool = PaymentsTool()
        
        result = payments_tool.schedule_payment(
            issue_id=issue_id,
            amount=discounted_amount,
            recurring=recurring,
            settlement_discount=(discount > 0),
            interval=interval,
            interval_count=interval_count,
            due_date=due_date
        )
        
        if result.startswith("Error:"):
            return jsonify({"error": result}), 400
        
        return jsonify({
            "message": "Payment arrangement created successfully",
            "details": result
        }), 201
        
    except Exception as e:
        print(f"Error creating payment arrangement: {e}")
        return jsonify({"error": "Failed to create payment arrangement"}), 500
