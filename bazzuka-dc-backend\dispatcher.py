from datetime import datetime, timedelta
import schedule
import time
import requests
import os

from dotenv import load_dotenv

load_dotenv()

os.environ["TZ"] = "America/Los_Angeles"
time.tzset()

from app.utils.supabase.queries import (
    get_action_items,
    get_action_items_by_date_and_time,
    get_yesterday_overdue_payments,
    mark_payments_as_overdue,
    insert_action_item,
    delete_action_item,
    insert_payment_comms,
)


# This function will be called by the send_messages function and send the drafted emails at every hour.
def send_messages():
    # Get all the scheduled messages from the database and use the core.Manager class to send them.
    date = datetime.now().strftime("%Y-%m-%d")
    time = datetime.now().strftime("%H:%M")
    fifteen_minutes_from_now = (datetime.now() + timedelta(minutes=15)).strftime(
        "%H:%M"
    )

    # print("Sending messages scheduled for: ", date, time)
    action_items = get_action_items(date, time, fifteen_minutes_from_now)
    # print("Action items: ", action_items)

    for action_item in action_items.data:
        # Make HTTP POST request to /outbound endpoint
        payload = {
            "defaulter_id": action_item["defaulter_id"],
            "channel": action_item["action_channel"],
            "reason": action_item["action_reason"],
        }

        try:
            response = requests.post("http://localhost:5000/v0/outbound", json=payload)
            response.raise_for_status()
            # print(
            #     f"Successfully sent outbound request for defaulter {action_item['defaulter_id']}"
            # )
            # FUTURE: Update the action item to mark it as sent
            # NOW: Delete the action item

        except requests.exceptions.RequestException as e:
            # TODO: Handle the error!!!!!!
            pass
            # print(
            #     f"Failed to send outbound request for defaulter {action_item['defaulter_id']}: {e}"
            # )


def get_overdue_payments():
    payments = get_yesterday_overdue_payments()
    if payments.data:
        payment_ids = [payment["id"] for payment in payments.data]
        mark_payments_as_overdue(payment_ids)

    for payment in payments.data:
        # Create an action item to follow up on the overdue payment
        action_item = insert_action_item(
            action_date=datetime.now().strftime("%Y-%m-%d"),
            action_time="09:00",
            action_channel="email",
            action_channel_content=payment["email"],
            defaulter_id=payment["defaulter_id"],
            action_reason=f"The user missed a promise to pay that was due on {payment['due_date']}. The link to the payment is: https://sscd05m3-5000.devtunnels.ms/v0/payments/{payment['id']}. It is urgent to make contact with the user to urge prompt payment and determine the next steps.",
            payment_likelihood=0,
            category="overdue-follow-up",
        )
        action_item_id = action_item["id"]
        insert_payment_comms(
            [
                {
                    "payment": payment["id"],
                    "action_item": action_item_id,
                    "payment_plan": payment["payment_plan"],
                }
            ]
        )


# Schedule the job to run every hour
schedule.every().hour.at(":00").do(send_messages)
schedule.every().hour.at(":15").do(send_messages)
schedule.every().hour.at(":30").do(send_messages)
schedule.every().hour.at(":45").do(send_messages)

# schedule.every().day.at("00:00").do(get_overdue_payments)

while True:
    schedule.run_pending()
    time.sleep(1)
