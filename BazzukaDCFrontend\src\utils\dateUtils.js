import { useTimezone } from '../timezoneContext';

/**
 * Formats a timestamp into a combined date and time string
 * - Time is in 12-hour format with AM/PM
 * - Time does not show seconds
 * - Date does not show year if it's the current year
 * - Date shows year if it's not the current year
 *
 * @param {string|Date} timestamp - The timestamp to format
 * @param {Object} options - Optional formatting options
 * @param {string} options.timeZone - The time zone to use (default: 'UTC')
 * @returns {string} The formatted timestamp
 */
export const formatTimestamp = (timestamp, options = {}) => {
  const date = timestamp instanceof Date ? timestamp : new Date(timestamp);
  const timeZone = options.timeZone || 'UTC';

  // Get current year
  const currentYear = new Date().getFullYear();
  const timestampYear = date.getFullYear();

  // Format time in 12-hour format without seconds
  const time = date.toLocaleTimeString('en-US', {
    hour: 'numeric',
    minute: '2-digit',
    hour12: true,
    timeZone
  });

  // Format date without year if it's the current year
  const dateOptions = {
    month: 'short',
    day: 'numeric',
    timeZone
  };

  // Add year to date options if it's not the current year
  if (timestampYear !== currentYear) {
    dateOptions.year = 'numeric';
  }

  const formattedDate = date.toLocaleDateString('en-US', dateOptions);

  // Combine date and time
  return `${formattedDate}, ${time}`;
};

/**
 * React hook that returns a function to format timestamps using the current timezone from context
 * @returns {Function} A function that formats timestamps using the current timezone
 */
export const useFormattedTimestamp = () => {
  const { timezone } = useTimezone();

  return (timestamp) => {
    return formatTimestamp(timestamp, { timeZone: timezone });
  };
};
