import os
from datetime import datetime, timedelta

from flask import Blueprint, jsonify, g, request, current_app
from app.core.comm_manager import comm_manager as manager, validate_action_items
from app.core.defaulters import defaulters
from app.core.ai import make_ai_client
from app.utils.supabase.queries import get_org

contacts_bp = Blueprint("contacts", __name__)


@contacts_bp.route("", methods=["POST", "OPTIONS"])
def contacts():
    if request.method == "OPTIONS":
        return jsonify({"methods": ["POST", "OPTIONS"]}), 200

    if request.method == "POST":
        org_id = None
        # if g.user:
        #    org_id = g.user.user_metadata.get("org_id")

        if not org_id and os.environ.get("FLASK_DEBUG"):
            org_id = "8241d390-a8b5-4f59-b0a9-b95c074db3f5"

        # Load defaulters into the system
        result = defaulters.load_defaulters(request.json, org_id=org_id)

        response_data = {
            "status": "success",
            "message": f"Processed {result['total_defaulters']} unique defaulters with {result['total_issues']} total issues",
            "defaulter_ids": result["defaulter_ids"],
            "total_defaulters": result["total_defaulters"],
            "total_issues": result["total_issues"],
            "background_processing": "Strategy generation and communication processing started in background",
            "note": "Heavy operations are being processed asynchronously with built-in thread-safe AI clients. Check /api/status/background-tasks for monitoring.",
        }

        # Include conflicts in response if any
        if result.get("conflicts"):
            response_data["data_conflicts"] = result["conflicts"]
            response_data["warning"] = (
                f"Data conflicts detected for {len(result['conflicts'])} defaulters. Check data_conflicts field for details."
            )

        return jsonify(response_data), 200
