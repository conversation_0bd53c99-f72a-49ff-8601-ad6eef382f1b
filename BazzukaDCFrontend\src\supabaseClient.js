// src/supabaseClient.js
import { createClient } from '@supabase/supabase-js'

const SUPABASE_URL = import.meta.env.VITE_SUPABASE_URL
const SUPABASE_ANON_KEY = import.meta.env.VITE_SUPABASE_ANON_KEY

// Create Supabase client with dev schema
export const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY, {
  db: {
    schema: 'dev',
  }
})

// Log that Supabase client is initialized (without exposing the key)
console.log(`Supabase client initialized with URL: ${SUPABASE_URL} and schema: dev`)
