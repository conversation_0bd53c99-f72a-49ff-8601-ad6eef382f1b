#!/usr/bin/env python3
"""
Test script to verify that the schedule_one_off_communication tool has been successfully moved
from voice agent and email composer to the analyzer.
"""

import sys
import os

# Add the project root to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'bazzuka-dc-backend'))

def test_analyzer_has_schedule_tool():
    """Test that the analyzer tool engine includes the schedule_one_off_communication tool."""
    try:
        from app.core.ai.tools import analyzer_tool_engine
        
        available_tools = list(analyzer_tool_engine.tools.keys())
        print("✓ Analyzer tool engine loaded successfully")
        print(f"Available tools: {available_tools}")
        
        if 'schedule_one_off_communication' in available_tools:
            print("✓ schedule_one_off_communication tool is available in analyzer")
            return True
        else:
            print("✗ schedule_one_off_communication tool is NOT available in analyzer")
            return False
            
    except Exception as e:
        print(f"✗ Error loading analyzer tool engine: {e}")
        return False

def test_email_composer_missing_schedule_tool():
    """Test that the email composer tool engine no longer includes the schedule_one_off_communication tool."""
    try:
        from app.core.ai.payments_tools import email_payment_tool_engine
        
        available_tools = list(email_payment_tool_engine.tools.keys())
        print("✓ Email composer tool engine loaded successfully")
        print(f"Available tools: {available_tools}")
        
        if 'schedule_one_off_communication' not in available_tools:
            print("✓ schedule_one_off_communication tool is NOT in email composer (as expected)")
            return True
        else:
            print("✗ schedule_one_off_communication tool is still in email composer")
            return False
            
    except Exception as e:
        print(f"✗ Error loading email composer tool engine: {e}")
        return False

def test_analyzer_prompt_includes_scheduling():
    """Test that the analyzer prompt includes instructions for scheduling customer-requested follow-ups."""
    try:
        with open('bazzuka-dc-backend/data/prompts/analyzer.md', 'r', encoding='utf-8') as f:
            prompt_content = f.read()
        
        print("✓ Analyzer prompt loaded successfully")
        
        if 'schedule_one_off_communication' in prompt_content:
            print("✓ Analyzer prompt includes schedule_one_off_communication instructions")
            return True
        else:
            print("✗ Analyzer prompt does NOT include schedule_one_off_communication instructions")
            return False
            
    except Exception as e:
        print(f"✗ Error loading analyzer prompt: {e}")
        return False

def test_email_composer_prompt_updated():
    """Test that the email composer prompt no longer includes scheduling instructions."""
    try:
        with open('bazzuka-dc-backend/data/prompts/email_composer.md', 'r', encoding='utf-8') as f:
            prompt_content = f.read()
        
        print("✓ Email composer prompt loaded successfully")
        
        # Check that it mentions analyzer will handle scheduling
        if 'analyzer will handle scheduling' in prompt_content:
            print("✓ Email composer prompt correctly mentions analyzer will handle scheduling")
            return True
        else:
            print("✗ Email composer prompt does NOT mention analyzer handling scheduling")
            return False
            
    except Exception as e:
        print(f"✗ Error loading email composer prompt: {e}")
        return False

def main():
    """Run all tests and report results."""
    print("Testing schedule_one_off_communication tool migration...")
    print("=" * 60)
    
    tests = [
        ("Analyzer has schedule tool", test_analyzer_has_schedule_tool),
        ("Email composer missing schedule tool", test_email_composer_missing_schedule_tool),
        ("Analyzer prompt includes scheduling", test_analyzer_prompt_includes_scheduling),
        ("Email composer prompt updated", test_email_composer_prompt_updated),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        print("-" * 40)
        result = test_func()
        results.append((test_name, result))
    
    print("\n" + "=" * 60)
    print("SUMMARY:")
    print("=" * 60)
    
    passed = 0
    for test_name, result in results:
        status = "PASS" if result else "FAIL"
        print(f"{status}: {test_name}")
        if result:
            passed += 1
    
    print(f"\nPassed: {passed}/{len(tests)} tests")
    
    if passed == len(tests):
        print("✓ All tests passed! Migration completed successfully.")
        return 0
    else:
        print("✗ Some tests failed. Please review the migration.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
