{"version": 3, "sources": ["../../@coreui/tslib/tslib.es6.js", "../../@coreui/prop-types/node_modules/react-is/cjs/react-is.production.min.js", "../../@coreui/prop-types/node_modules/react-is/cjs/react-is.development.js", "../../@coreui/prop-types/node_modules/react-is/index.js", "../../@coreui/object-assign/index.js", "../../@coreui/prop-types/lib/ReactPropTypesSecret.js", "../../@coreui/prop-types/lib/has.js", "../../@coreui/prop-types/checkPropTypes.js", "../../@coreui/prop-types/factoryWithTypeCheckers.js", "../../@coreui/prop-types/factoryWithThrowingShims.js", "../../@coreui/prop-types/index.js", "../../@coreui/classnames/index.js", "../../@coreui/icons-react/src/CIcon.tsx", "../../@coreui/icons-react/src/CIconSvg.tsx"], "sourcesContent": ["/******************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise, SuppressedError, Symbol */\r\n\r\nvar extendStatics = function(d, b) {\r\n    extendStatics = Object.setPrototypeOf ||\r\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n        function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\r\n    return extendStatics(d, b);\r\n};\r\n\r\nexport function __extends(d, b) {\r\n    if (typeof b !== \"function\" && b !== null)\r\n        throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nexport var __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    }\r\n    return __assign.apply(this, arguments);\r\n}\r\n\r\nexport function __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\r\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\r\n                t[p[i]] = s[p[i]];\r\n        }\r\n    return t;\r\n}\r\n\r\nexport function __decorate(decorators, target, key, desc) {\r\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\r\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\r\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\r\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\r\n}\r\n\r\nexport function __param(paramIndex, decorator) {\r\n    return function (target, key) { decorator(target, key, paramIndex); }\r\n}\r\n\r\nexport function __esDecorate(ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {\r\n    function accept(f) { if (f !== void 0 && typeof f !== \"function\") throw new TypeError(\"Function expected\"); return f; }\r\n    var kind = contextIn.kind, key = kind === \"getter\" ? \"get\" : kind === \"setter\" ? \"set\" : \"value\";\r\n    var target = !descriptorIn && ctor ? contextIn[\"static\"] ? ctor : ctor.prototype : null;\r\n    var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});\r\n    var _, done = false;\r\n    for (var i = decorators.length - 1; i >= 0; i--) {\r\n        var context = {};\r\n        for (var p in contextIn) context[p] = p === \"access\" ? {} : contextIn[p];\r\n        for (var p in contextIn.access) context.access[p] = contextIn.access[p];\r\n        context.addInitializer = function (f) { if (done) throw new TypeError(\"Cannot add initializers after decoration has completed\"); extraInitializers.push(accept(f || null)); };\r\n        var result = (0, decorators[i])(kind === \"accessor\" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);\r\n        if (kind === \"accessor\") {\r\n            if (result === void 0) continue;\r\n            if (result === null || typeof result !== \"object\") throw new TypeError(\"Object expected\");\r\n            if (_ = accept(result.get)) descriptor.get = _;\r\n            if (_ = accept(result.set)) descriptor.set = _;\r\n            if (_ = accept(result.init)) initializers.unshift(_);\r\n        }\r\n        else if (_ = accept(result)) {\r\n            if (kind === \"field\") initializers.unshift(_);\r\n            else descriptor[key] = _;\r\n        }\r\n    }\r\n    if (target) Object.defineProperty(target, contextIn.name, descriptor);\r\n    done = true;\r\n};\r\n\r\nexport function __runInitializers(thisArg, initializers, value) {\r\n    var useValue = arguments.length > 2;\r\n    for (var i = 0; i < initializers.length; i++) {\r\n        value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);\r\n    }\r\n    return useValue ? value : void 0;\r\n};\r\n\r\nexport function __propKey(x) {\r\n    return typeof x === \"symbol\" ? x : \"\".concat(x);\r\n};\r\n\r\nexport function __setFunctionName(f, name, prefix) {\r\n    if (typeof name === \"symbol\") name = name.description ? \"[\".concat(name.description, \"]\") : \"\";\r\n    return Object.defineProperty(f, \"name\", { configurable: true, value: prefix ? \"\".concat(prefix, \" \", name) : name });\r\n};\r\n\r\nexport function __metadata(metadataKey, metadataValue) {\r\n    if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\r\n}\r\n\r\nexport function __awaiter(thisArg, _arguments, P, generator) {\r\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nexport function __generator(thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (g && (g = 0, op[0] && (_ = 0)), _) try {\r\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [op[0] & 2, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n}\r\n\r\nexport var __createBinding = Object.create ? (function(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    var desc = Object.getOwnPropertyDescriptor(m, k);\r\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\r\n        desc = { enumerable: true, get: function() { return m[k]; } };\r\n    }\r\n    Object.defineProperty(o, k2, desc);\r\n}) : (function(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    o[k2] = m[k];\r\n});\r\n\r\nexport function __exportStar(m, o) {\r\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(o, p)) __createBinding(o, m, p);\r\n}\r\n\r\nexport function __values(o) {\r\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\r\n    if (m) return m.call(o);\r\n    if (o && typeof o.length === \"number\") return {\r\n        next: function () {\r\n            if (o && i >= o.length) o = void 0;\r\n            return { value: o && o[i++], done: !o };\r\n        }\r\n    };\r\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\r\n}\r\n\r\nexport function __read(o, n) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\r\n    if (!m) return o;\r\n    var i = m.call(o), r, ar = [], e;\r\n    try {\r\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\r\n    }\r\n    catch (error) { e = { error: error }; }\r\n    finally {\r\n        try {\r\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\r\n        }\r\n        finally { if (e) throw e.error; }\r\n    }\r\n    return ar;\r\n}\r\n\r\n/** @deprecated */\r\nexport function __spread() {\r\n    for (var ar = [], i = 0; i < arguments.length; i++)\r\n        ar = ar.concat(__read(arguments[i]));\r\n    return ar;\r\n}\r\n\r\n/** @deprecated */\r\nexport function __spreadArrays() {\r\n    for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\r\n    for (var r = Array(s), k = 0, i = 0; i < il; i++)\r\n        for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\r\n            r[k] = a[j];\r\n    return r;\r\n}\r\n\r\nexport function __spreadArray(to, from, pack) {\r\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\r\n        if (ar || !(i in from)) {\r\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\r\n            ar[i] = from[i];\r\n        }\r\n    }\r\n    return to.concat(ar || Array.prototype.slice.call(from));\r\n}\r\n\r\nexport function __await(v) {\r\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n}\r\n\r\nexport function __asyncGenerator(thisArg, _arguments, generator) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n    function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\r\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\r\n    function fulfill(value) { resume(\"next\", value); }\r\n    function reject(value) { resume(\"throw\", value); }\r\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n}\r\n\r\nexport function __asyncDelegator(o) {\r\n    var i, p;\r\n    return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\r\n    function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: false } : f ? f(v) : v; } : f; }\r\n}\r\n\r\nexport function __asyncValues(o) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var m = o[Symbol.asyncIterator], i;\r\n    return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\r\n    function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\r\n    function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\r\n}\r\n\r\nexport function __makeTemplateObject(cooked, raw) {\r\n    if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\r\n    return cooked;\r\n};\r\n\r\nvar __setModuleDefault = Object.create ? (function(o, v) {\r\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\r\n}) : function(o, v) {\r\n    o[\"default\"] = v;\r\n};\r\n\r\nexport function __importStar(mod) {\r\n    if (mod && mod.__esModule) return mod;\r\n    var result = {};\r\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\r\n    __setModuleDefault(result, mod);\r\n    return result;\r\n}\r\n\r\nexport function __importDefault(mod) {\r\n    return (mod && mod.__esModule) ? mod : { default: mod };\r\n}\r\n\r\nexport function __classPrivateFieldGet(receiver, state, kind, f) {\r\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\r\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\r\n    return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\r\n}\r\n\r\nexport function __classPrivateFieldSet(receiver, state, value, kind, f) {\r\n    if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\r\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\r\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\r\n    return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\r\n}\r\n\r\nexport function __classPrivateFieldIn(state, receiver) {\r\n    if (receiver === null || (typeof receiver !== \"object\" && typeof receiver !== \"function\")) throw new TypeError(\"Cannot use 'in' operator on non-object\");\r\n    return typeof state === \"function\" ? receiver === state : state.has(receiver);\r\n}\r\n\r\nexport function __addDisposableResource(env, value, async) {\r\n    if (value !== null && value !== void 0) {\r\n        if (typeof value !== \"object\" && typeof value !== \"function\") throw new TypeError(\"Object expected.\");\r\n        var dispose;\r\n        if (async) {\r\n            if (!Symbol.asyncDispose) throw new TypeError(\"Symbol.asyncDispose is not defined.\");\r\n            dispose = value[Symbol.asyncDispose];\r\n        }\r\n        if (dispose === void 0) {\r\n            if (!Symbol.dispose) throw new TypeError(\"Symbol.dispose is not defined.\");\r\n            dispose = value[Symbol.dispose];\r\n        }\r\n        if (typeof dispose !== \"function\") throw new TypeError(\"Object not disposable.\");\r\n        env.stack.push({ value: value, dispose: dispose, async: async });\r\n    }\r\n    else if (async) {\r\n        env.stack.push({ async: true });\r\n    }\r\n    return value;\r\n}\r\n\r\nvar _SuppressedError = typeof SuppressedError === \"function\" ? SuppressedError : function (error, suppressed, message) {\r\n    var e = new Error(message);\r\n    return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\r\n};\r\n\r\nexport function __disposeResources(env) {\r\n    function fail(e) {\r\n        env.error = env.hasError ? new _SuppressedError(e, env.error, \"An error was suppressed during disposal.\") : e;\r\n        env.hasError = true;\r\n    }\r\n    function next() {\r\n        while (env.stack.length) {\r\n            var rec = env.stack.pop();\r\n            try {\r\n                var result = rec.dispose && rec.dispose.call(rec.value);\r\n                if (rec.async) return Promise.resolve(result).then(next, function(e) { fail(e); return next(); });\r\n            }\r\n            catch (e) {\r\n                fail(e);\r\n            }\r\n        }\r\n        if (env.hasError) throw env.error;\r\n    }\r\n    return next();\r\n}\r\n\r\nexport default {\r\n    __extends: __extends,\r\n    __assign: __assign,\r\n    __rest: __rest,\r\n    __decorate: __decorate,\r\n    __param: __param,\r\n    __metadata: __metadata,\r\n    __awaiter: __awaiter,\r\n    __generator: __generator,\r\n    __createBinding: __createBinding,\r\n    __exportStar: __exportStar,\r\n    __values: __values,\r\n    __read: __read,\r\n    __spread: __spread,\r\n    __spreadArrays: __spreadArrays,\r\n    __spreadArray: __spreadArray,\r\n    __await: __await,\r\n    __asyncGenerator: __asyncGenerator,\r\n    __asyncDelegator: __asyncDelegator,\r\n    __asyncValues: __asyncValues,\r\n    __makeTemplateObject: __makeTemplateObject,\r\n    __importStar: __importStar,\r\n    __importDefault: __importDefault,\r\n    __classPrivateFieldGet: __classPrivateFieldGet,\r\n    __classPrivateFieldSet: __classPrivateFieldSet,\r\n    __classPrivateFieldIn: __classPrivateFieldIn,\r\n    __addDisposableResource: __addDisposableResource,\r\n    __disposeResources: __disposeResources,\r\n};\r\n", "/** @license React v16.13.1\n * react-is.production.min.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';var b=\"function\"===typeof Symbol&&Symbol.for,c=b?Symbol.for(\"react.element\"):60103,d=b?Symbol.for(\"react.portal\"):60106,e=b?Symbol.for(\"react.fragment\"):60107,f=b?Symbol.for(\"react.strict_mode\"):60108,g=b?Symbol.for(\"react.profiler\"):60114,h=b?Symbol.for(\"react.provider\"):60109,k=b?Symbol.for(\"react.context\"):60110,l=b?Symbol.for(\"react.async_mode\"):60111,m=b?Symbol.for(\"react.concurrent_mode\"):60111,n=b?Symbol.for(\"react.forward_ref\"):60112,p=b?Symbol.for(\"react.suspense\"):60113,q=b?\nSymbol.for(\"react.suspense_list\"):60120,r=b?Symbol.for(\"react.memo\"):60115,t=b?Symbol.for(\"react.lazy\"):60116,v=b?Symbol.for(\"react.block\"):60121,w=b?Symbol.for(\"react.fundamental\"):60117,x=b?Symbol.for(\"react.responder\"):60118,y=b?Symbol.for(\"react.scope\"):60119;\nfunction z(a){if(\"object\"===typeof a&&null!==a){var u=a.$$typeof;switch(u){case c:switch(a=a.type,a){case l:case m:case e:case g:case f:case p:return a;default:switch(a=a&&a.$$typeof,a){case k:case n:case t:case r:case h:return a;default:return u}}case d:return u}}}function A(a){return z(a)===m}exports.AsyncMode=l;exports.ConcurrentMode=m;exports.ContextConsumer=k;exports.ContextProvider=h;exports.Element=c;exports.ForwardRef=n;exports.Fragment=e;exports.Lazy=t;exports.Memo=r;exports.Portal=d;\nexports.Profiler=g;exports.StrictMode=f;exports.Suspense=p;exports.isAsyncMode=function(a){return A(a)||z(a)===l};exports.isConcurrentMode=A;exports.isContextConsumer=function(a){return z(a)===k};exports.isContextProvider=function(a){return z(a)===h};exports.isElement=function(a){return\"object\"===typeof a&&null!==a&&a.$$typeof===c};exports.isForwardRef=function(a){return z(a)===n};exports.isFragment=function(a){return z(a)===e};exports.isLazy=function(a){return z(a)===t};\nexports.isMemo=function(a){return z(a)===r};exports.isPortal=function(a){return z(a)===d};exports.isProfiler=function(a){return z(a)===g};exports.isStrictMode=function(a){return z(a)===f};exports.isSuspense=function(a){return z(a)===p};\nexports.isValidElementType=function(a){return\"string\"===typeof a||\"function\"===typeof a||a===e||a===m||a===g||a===f||a===p||a===q||\"object\"===typeof a&&null!==a&&(a.$$typeof===t||a.$$typeof===r||a.$$typeof===h||a.$$typeof===k||a.$$typeof===n||a.$$typeof===w||a.$$typeof===x||a.$$typeof===y||a.$$typeof===v)};exports.typeOf=z;\n", "/** @license React v16.13.1\n * react-is.development.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\n\n\nif (process.env.NODE_ENV !== \"production\") {\n  (function() {\n'use strict';\n\n// The Symbol used to tag the ReactElement-like types. If there is no native Symbol\n// nor polyfill, then a plain number is used for performance.\nvar hasSymbol = typeof Symbol === 'function' && Symbol.for;\nvar REACT_ELEMENT_TYPE = hasSymbol ? Symbol.for('react.element') : 0xeac7;\nvar REACT_PORTAL_TYPE = hasSymbol ? Symbol.for('react.portal') : 0xeaca;\nvar REACT_FRAGMENT_TYPE = hasSymbol ? Symbol.for('react.fragment') : 0xeacb;\nvar REACT_STRICT_MODE_TYPE = hasSymbol ? Symbol.for('react.strict_mode') : 0xeacc;\nvar REACT_PROFILER_TYPE = hasSymbol ? Symbol.for('react.profiler') : 0xead2;\nvar REACT_PROVIDER_TYPE = hasSymbol ? Symbol.for('react.provider') : 0xeacd;\nvar REACT_CONTEXT_TYPE = hasSymbol ? Symbol.for('react.context') : 0xeace; // TODO: We don't use AsyncMode or ConcurrentMode anymore. They were temporary\n// (unstable) APIs that have been removed. Can we remove the symbols?\n\nvar REACT_ASYNC_MODE_TYPE = hasSymbol ? Symbol.for('react.async_mode') : 0xeacf;\nvar REACT_CONCURRENT_MODE_TYPE = hasSymbol ? Symbol.for('react.concurrent_mode') : 0xeacf;\nvar REACT_FORWARD_REF_TYPE = hasSymbol ? Symbol.for('react.forward_ref') : 0xead0;\nvar REACT_SUSPENSE_TYPE = hasSymbol ? Symbol.for('react.suspense') : 0xead1;\nvar REACT_SUSPENSE_LIST_TYPE = hasSymbol ? Symbol.for('react.suspense_list') : 0xead8;\nvar REACT_MEMO_TYPE = hasSymbol ? Symbol.for('react.memo') : 0xead3;\nvar REACT_LAZY_TYPE = hasSymbol ? Symbol.for('react.lazy') : 0xead4;\nvar REACT_BLOCK_TYPE = hasSymbol ? Symbol.for('react.block') : 0xead9;\nvar REACT_FUNDAMENTAL_TYPE = hasSymbol ? Symbol.for('react.fundamental') : 0xead5;\nvar REACT_RESPONDER_TYPE = hasSymbol ? Symbol.for('react.responder') : 0xead6;\nvar REACT_SCOPE_TYPE = hasSymbol ? Symbol.for('react.scope') : 0xead7;\n\nfunction isValidElementType(type) {\n  return typeof type === 'string' || typeof type === 'function' || // Note: its typeof might be other than 'symbol' or 'number' if it's a polyfill.\n  type === REACT_FRAGMENT_TYPE || type === REACT_CONCURRENT_MODE_TYPE || type === REACT_PROFILER_TYPE || type === REACT_STRICT_MODE_TYPE || type === REACT_SUSPENSE_TYPE || type === REACT_SUSPENSE_LIST_TYPE || typeof type === 'object' && type !== null && (type.$$typeof === REACT_LAZY_TYPE || type.$$typeof === REACT_MEMO_TYPE || type.$$typeof === REACT_PROVIDER_TYPE || type.$$typeof === REACT_CONTEXT_TYPE || type.$$typeof === REACT_FORWARD_REF_TYPE || type.$$typeof === REACT_FUNDAMENTAL_TYPE || type.$$typeof === REACT_RESPONDER_TYPE || type.$$typeof === REACT_SCOPE_TYPE || type.$$typeof === REACT_BLOCK_TYPE);\n}\n\nfunction typeOf(object) {\n  if (typeof object === 'object' && object !== null) {\n    var $$typeof = object.$$typeof;\n\n    switch ($$typeof) {\n      case REACT_ELEMENT_TYPE:\n        var type = object.type;\n\n        switch (type) {\n          case REACT_ASYNC_MODE_TYPE:\n          case REACT_CONCURRENT_MODE_TYPE:\n          case REACT_FRAGMENT_TYPE:\n          case REACT_PROFILER_TYPE:\n          case REACT_STRICT_MODE_TYPE:\n          case REACT_SUSPENSE_TYPE:\n            return type;\n\n          default:\n            var $$typeofType = type && type.$$typeof;\n\n            switch ($$typeofType) {\n              case REACT_CONTEXT_TYPE:\n              case REACT_FORWARD_REF_TYPE:\n              case REACT_LAZY_TYPE:\n              case REACT_MEMO_TYPE:\n              case REACT_PROVIDER_TYPE:\n                return $$typeofType;\n\n              default:\n                return $$typeof;\n            }\n\n        }\n\n      case REACT_PORTAL_TYPE:\n        return $$typeof;\n    }\n  }\n\n  return undefined;\n} // AsyncMode is deprecated along with isAsyncMode\n\nvar AsyncMode = REACT_ASYNC_MODE_TYPE;\nvar ConcurrentMode = REACT_CONCURRENT_MODE_TYPE;\nvar ContextConsumer = REACT_CONTEXT_TYPE;\nvar ContextProvider = REACT_PROVIDER_TYPE;\nvar Element = REACT_ELEMENT_TYPE;\nvar ForwardRef = REACT_FORWARD_REF_TYPE;\nvar Fragment = REACT_FRAGMENT_TYPE;\nvar Lazy = REACT_LAZY_TYPE;\nvar Memo = REACT_MEMO_TYPE;\nvar Portal = REACT_PORTAL_TYPE;\nvar Profiler = REACT_PROFILER_TYPE;\nvar StrictMode = REACT_STRICT_MODE_TYPE;\nvar Suspense = REACT_SUSPENSE_TYPE;\nvar hasWarnedAboutDeprecatedIsAsyncMode = false; // AsyncMode should be deprecated\n\nfunction isAsyncMode(object) {\n  {\n    if (!hasWarnedAboutDeprecatedIsAsyncMode) {\n      hasWarnedAboutDeprecatedIsAsyncMode = true; // Using console['warn'] to evade Babel and ESLint\n\n      console['warn']('The ReactIs.isAsyncMode() alias has been deprecated, ' + 'and will be removed in React 17+. Update your code to use ' + 'ReactIs.isConcurrentMode() instead. It has the exact same API.');\n    }\n  }\n\n  return isConcurrentMode(object) || typeOf(object) === REACT_ASYNC_MODE_TYPE;\n}\nfunction isConcurrentMode(object) {\n  return typeOf(object) === REACT_CONCURRENT_MODE_TYPE;\n}\nfunction isContextConsumer(object) {\n  return typeOf(object) === REACT_CONTEXT_TYPE;\n}\nfunction isContextProvider(object) {\n  return typeOf(object) === REACT_PROVIDER_TYPE;\n}\nfunction isElement(object) {\n  return typeof object === 'object' && object !== null && object.$$typeof === REACT_ELEMENT_TYPE;\n}\nfunction isForwardRef(object) {\n  return typeOf(object) === REACT_FORWARD_REF_TYPE;\n}\nfunction isFragment(object) {\n  return typeOf(object) === REACT_FRAGMENT_TYPE;\n}\nfunction isLazy(object) {\n  return typeOf(object) === REACT_LAZY_TYPE;\n}\nfunction isMemo(object) {\n  return typeOf(object) === REACT_MEMO_TYPE;\n}\nfunction isPortal(object) {\n  return typeOf(object) === REACT_PORTAL_TYPE;\n}\nfunction isProfiler(object) {\n  return typeOf(object) === REACT_PROFILER_TYPE;\n}\nfunction isStrictMode(object) {\n  return typeOf(object) === REACT_STRICT_MODE_TYPE;\n}\nfunction isSuspense(object) {\n  return typeOf(object) === REACT_SUSPENSE_TYPE;\n}\n\nexports.AsyncMode = AsyncMode;\nexports.ConcurrentMode = ConcurrentMode;\nexports.ContextConsumer = ContextConsumer;\nexports.ContextProvider = ContextProvider;\nexports.Element = Element;\nexports.ForwardRef = ForwardRef;\nexports.Fragment = Fragment;\nexports.Lazy = Lazy;\nexports.Memo = Memo;\nexports.Portal = Portal;\nexports.Profiler = Profiler;\nexports.StrictMode = StrictMode;\nexports.Suspense = Suspense;\nexports.isAsyncMode = isAsyncMode;\nexports.isConcurrentMode = isConcurrentMode;\nexports.isContextConsumer = isContextConsumer;\nexports.isContextProvider = isContextProvider;\nexports.isElement = isElement;\nexports.isForwardRef = isForwardRef;\nexports.isFragment = isFragment;\nexports.isLazy = isLazy;\nexports.isMemo = isMemo;\nexports.isPortal = isPortal;\nexports.isProfiler = isProfiler;\nexports.isStrictMode = isStrictMode;\nexports.isSuspense = isSuspense;\nexports.isValidElementType = isValidElementType;\nexports.typeOf = typeOf;\n  })();\n}\n", "'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/react-is.production.min.js');\n} else {\n  module.exports = require('./cjs/react-is.development.js');\n}\n", "/*\nobject-assign\n(c) <PERSON><PERSON> Sorhus\n@license MIT\n*/\n\n'use strict';\n/* eslint-disable no-unused-vars */\nvar getOwnPropertySymbols = Object.getOwnPropertySymbols;\nvar hasOwnProperty = Object.prototype.hasOwnProperty;\nvar propIsEnumerable = Object.prototype.propertyIsEnumerable;\n\nfunction toObject(val) {\n\tif (val === null || val === undefined) {\n\t\tthrow new TypeError('Object.assign cannot be called with null or undefined');\n\t}\n\n\treturn Object(val);\n}\n\nfunction shouldUseNative() {\n\ttry {\n\t\tif (!Object.assign) {\n\t\t\treturn false;\n\t\t}\n\n\t\t// Detect buggy property enumeration order in older V8 versions.\n\n\t\t// https://bugs.chromium.org/p/v8/issues/detail?id=4118\n\t\tvar test1 = new String('abc');  // eslint-disable-line no-new-wrappers\n\t\ttest1[5] = 'de';\n\t\tif (Object.getOwnPropertyNames(test1)[0] === '5') {\n\t\t\treturn false;\n\t\t}\n\n\t\t// https://bugs.chromium.org/p/v8/issues/detail?id=3056\n\t\tvar test2 = {};\n\t\tfor (var i = 0; i < 10; i++) {\n\t\t\ttest2['_' + String.fromCharCode(i)] = i;\n\t\t}\n\t\tvar order2 = Object.getOwnPropertyNames(test2).map(function (n) {\n\t\t\treturn test2[n];\n\t\t});\n\t\tif (order2.join('') !== '0123456789') {\n\t\t\treturn false;\n\t\t}\n\n\t\t// https://bugs.chromium.org/p/v8/issues/detail?id=3056\n\t\tvar test3 = {};\n\t\t'abcdefghijklmnopqrst'.split('').forEach(function (letter) {\n\t\t\ttest3[letter] = letter;\n\t\t});\n\t\tif (Object.keys(Object.assign({}, test3)).join('') !==\n\t\t\t\t'abcdefghijklmnopqrst') {\n\t\t\treturn false;\n\t\t}\n\n\t\treturn true;\n\t} catch (err) {\n\t\t// We don't expect any of the above to throw, but better to be safe.\n\t\treturn false;\n\t}\n}\n\nmodule.exports = shouldUseNative() ? Object.assign : function (target, source) {\n\tvar from;\n\tvar to = toObject(target);\n\tvar symbols;\n\n\tfor (var s = 1; s < arguments.length; s++) {\n\t\tfrom = Object(arguments[s]);\n\n\t\tfor (var key in from) {\n\t\t\tif (hasOwnProperty.call(from, key)) {\n\t\t\t\tto[key] = from[key];\n\t\t\t}\n\t\t}\n\n\t\tif (getOwnPropertySymbols) {\n\t\t\tsymbols = getOwnPropertySymbols(from);\n\t\t\tfor (var i = 0; i < symbols.length; i++) {\n\t\t\t\tif (propIsEnumerable.call(from, symbols[i])) {\n\t\t\t\t\tto[symbols[i]] = from[symbols[i]];\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\treturn to;\n};\n", "/**\n * Copyright (c) 2013-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\nvar ReactPropTypesSecret = 'SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED';\n\nmodule.exports = ReactPropTypesSecret;\n", "module.exports = Function.call.bind(Object.prototype.hasOwnProperty);\n", "/**\n * Copyright (c) 2013-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\nvar printWarning = function() {};\n\nif (process.env.NODE_ENV !== 'production') {\n  var ReactPropTypesSecret = require('./lib/ReactPropTypesSecret');\n  var loggedTypeFailures = {};\n  var has = require('./lib/has');\n\n  printWarning = function(text) {\n    var message = 'Warning: ' + text;\n    if (typeof console !== 'undefined') {\n      console.error(message);\n    }\n    try {\n      // --- Welcome to debugging React ---\n      // This error was thrown as a convenience so that you can use this stack\n      // to find the callsite that caused this warning to fire.\n      throw new Error(message);\n    } catch (x) { /**/ }\n  };\n}\n\n/**\n * Assert that the values match with the type specs.\n * Error messages are memorized and will only be shown once.\n *\n * @param {object} typeSpecs Map of name to a ReactPropType\n * @param {object} values Runtime values that need to be type-checked\n * @param {string} location e.g. \"prop\", \"context\", \"child context\"\n * @param {string} componentName Name of the component for error messages.\n * @param {?Function} getStack Returns the component stack.\n * @private\n */\nfunction checkPropTypes(typeSpecs, values, location, componentName, getStack) {\n  if (process.env.NODE_ENV !== 'production') {\n    for (var typeSpecName in typeSpecs) {\n      if (has(typeSpecs, typeSpecName)) {\n        var error;\n        // Prop type validation may throw. In case they do, we don't want to\n        // fail the render phase where it didn't fail before. So we log it.\n        // After these have been cleaned up, we'll let them throw.\n        try {\n          // This is intentionally an invariant that gets caught. It's the same\n          // behavior as without this statement except with a better message.\n          if (typeof typeSpecs[typeSpecName] !== 'function') {\n            var err = Error(\n              (componentName || 'React class') + ': ' + location + ' type `' + typeSpecName + '` is invalid; ' +\n              'it must be a function, usually from the `prop-types` package, but received `' + typeof typeSpecs[typeSpecName] + '`.' +\n              'This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.'\n            );\n            err.name = 'Invariant Violation';\n            throw err;\n          }\n          error = typeSpecs[typeSpecName](values, typeSpecName, componentName, location, null, ReactPropTypesSecret);\n        } catch (ex) {\n          error = ex;\n        }\n        if (error && !(error instanceof Error)) {\n          printWarning(\n            (componentName || 'React class') + ': type specification of ' +\n            location + ' `' + typeSpecName + '` is invalid; the type checker ' +\n            'function must return `null` or an `Error` but returned a ' + typeof error + '. ' +\n            'You may have forgotten to pass an argument to the type checker ' +\n            'creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and ' +\n            'shape all require an argument).'\n          );\n        }\n        if (error instanceof Error && !(error.message in loggedTypeFailures)) {\n          // Only monitor this failure once because there tends to be a lot of the\n          // same error.\n          loggedTypeFailures[error.message] = true;\n\n          var stack = getStack ? getStack() : '';\n\n          printWarning(\n            'Failed ' + location + ' type: ' + error.message + (stack != null ? stack : '')\n          );\n        }\n      }\n    }\n  }\n}\n\n/**\n * Resets warning cache when testing.\n *\n * @private\n */\ncheckPropTypes.resetWarningCache = function() {\n  if (process.env.NODE_ENV !== 'production') {\n    loggedTypeFailures = {};\n  }\n}\n\nmodule.exports = checkPropTypes;\n", "/**\n * Copyright (c) 2013-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\nvar ReactIs = require('react-is');\nvar assign = require('object-assign');\n\nvar ReactPropTypesSecret = require('./lib/ReactPropTypesSecret');\nvar has = require('./lib/has');\nvar checkPropTypes = require('./checkPropTypes');\n\nvar printWarning = function() {};\n\nif (process.env.NODE_ENV !== 'production') {\n  printWarning = function(text) {\n    var message = 'Warning: ' + text;\n    if (typeof console !== 'undefined') {\n      console.error(message);\n    }\n    try {\n      // --- Welcome to debugging React ---\n      // This error was thrown as a convenience so that you can use this stack\n      // to find the callsite that caused this warning to fire.\n      throw new Error(message);\n    } catch (x) {}\n  };\n}\n\nfunction emptyFunctionThatReturnsNull() {\n  return null;\n}\n\nmodule.exports = function(isValidElement, throwOnDirectAccess) {\n  /* global Symbol */\n  var ITERATOR_SYMBOL = typeof Symbol === 'function' && Symbol.iterator;\n  var FAUX_ITERATOR_SYMBOL = '@@iterator'; // Before Symbol spec.\n\n  /**\n   * Returns the iterator method function contained on the iterable object.\n   *\n   * Be sure to invoke the function with the iterable as context:\n   *\n   *     var iteratorFn = getIteratorFn(myIterable);\n   *     if (iteratorFn) {\n   *       var iterator = iteratorFn.call(myIterable);\n   *       ...\n   *     }\n   *\n   * @param {?object} maybeIterable\n   * @return {?function}\n   */\n  function getIteratorFn(maybeIterable) {\n    var iteratorFn = maybeIterable && (ITERATOR_SYMBOL && maybeIterable[ITERATOR_SYMBOL] || maybeIterable[FAUX_ITERATOR_SYMBOL]);\n    if (typeof iteratorFn === 'function') {\n      return iteratorFn;\n    }\n  }\n\n  /**\n   * Collection of methods that allow declaration and validation of props that are\n   * supplied to React components. Example usage:\n   *\n   *   var Props = require('ReactPropTypes');\n   *   var MyArticle = React.createClass({\n   *     propTypes: {\n   *       // An optional string prop named \"description\".\n   *       description: Props.string,\n   *\n   *       // A required enum prop named \"category\".\n   *       category: Props.oneOf(['News','Photos']).isRequired,\n   *\n   *       // A prop named \"dialog\" that requires an instance of Dialog.\n   *       dialog: Props.instanceOf(Dialog).isRequired\n   *     },\n   *     render: function() { ... }\n   *   });\n   *\n   * A more formal specification of how these methods are used:\n   *\n   *   type := array|bool|func|object|number|string|oneOf([...])|instanceOf(...)\n   *   decl := ReactPropTypes.{type}(.isRequired)?\n   *\n   * Each and every declaration produces a function with the same signature. This\n   * allows the creation of custom validation functions. For example:\n   *\n   *  var MyLink = React.createClass({\n   *    propTypes: {\n   *      // An optional string or URI prop named \"href\".\n   *      href: function(props, propName, componentName) {\n   *        var propValue = props[propName];\n   *        if (propValue != null && typeof propValue !== 'string' &&\n   *            !(propValue instanceof URI)) {\n   *          return new Error(\n   *            'Expected a string or an URI for ' + propName + ' in ' +\n   *            componentName\n   *          );\n   *        }\n   *      }\n   *    },\n   *    render: function() {...}\n   *  });\n   *\n   * @internal\n   */\n\n  var ANONYMOUS = '<<anonymous>>';\n\n  // Important!\n  // Keep this list in sync with production version in `./factoryWithThrowingShims.js`.\n  var ReactPropTypes = {\n    array: createPrimitiveTypeChecker('array'),\n    bigint: createPrimitiveTypeChecker('bigint'),\n    bool: createPrimitiveTypeChecker('boolean'),\n    func: createPrimitiveTypeChecker('function'),\n    number: createPrimitiveTypeChecker('number'),\n    object: createPrimitiveTypeChecker('object'),\n    string: createPrimitiveTypeChecker('string'),\n    symbol: createPrimitiveTypeChecker('symbol'),\n\n    any: createAnyTypeChecker(),\n    arrayOf: createArrayOfTypeChecker,\n    element: createElementTypeChecker(),\n    elementType: createElementTypeTypeChecker(),\n    instanceOf: createInstanceTypeChecker,\n    node: createNodeChecker(),\n    objectOf: createObjectOfTypeChecker,\n    oneOf: createEnumTypeChecker,\n    oneOfType: createUnionTypeChecker,\n    shape: createShapeTypeChecker,\n    exact: createStrictShapeTypeChecker,\n  };\n\n  /**\n   * inlined Object.is polyfill to avoid requiring consumers ship their own\n   * https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/is\n   */\n  /*eslint-disable no-self-compare*/\n  function is(x, y) {\n    // SameValue algorithm\n    if (x === y) {\n      // Steps 1-5, 7-10\n      // Steps 6.b-6.e: +0 != -0\n      return x !== 0 || 1 / x === 1 / y;\n    } else {\n      // Step 6.a: NaN == NaN\n      return x !== x && y !== y;\n    }\n  }\n  /*eslint-enable no-self-compare*/\n\n  /**\n   * We use an Error-like object for backward compatibility as people may call\n   * PropTypes directly and inspect their output. However, we don't use real\n   * Errors anymore. We don't inspect their stack anyway, and creating them\n   * is prohibitively expensive if they are created too often, such as what\n   * happens in oneOfType() for any type before the one that matched.\n   */\n  function PropTypeError(message, data) {\n    this.message = message;\n    this.data = data && typeof data === 'object' ? data: {};\n    this.stack = '';\n  }\n  // Make `instanceof Error` still work for returned errors.\n  PropTypeError.prototype = Error.prototype;\n\n  function createChainableTypeChecker(validate) {\n    if (process.env.NODE_ENV !== 'production') {\n      var manualPropTypeCallCache = {};\n      var manualPropTypeWarningCount = 0;\n    }\n    function checkType(isRequired, props, propName, componentName, location, propFullName, secret) {\n      componentName = componentName || ANONYMOUS;\n      propFullName = propFullName || propName;\n\n      if (secret !== ReactPropTypesSecret) {\n        if (throwOnDirectAccess) {\n          // New behavior only for users of `prop-types` package\n          var err = new Error(\n            'Calling PropTypes validators directly is not supported by the `prop-types` package. ' +\n            'Use `PropTypes.checkPropTypes()` to call them. ' +\n            'Read more at http://fb.me/use-check-prop-types'\n          );\n          err.name = 'Invariant Violation';\n          throw err;\n        } else if (process.env.NODE_ENV !== 'production' && typeof console !== 'undefined') {\n          // Old behavior for people using React.PropTypes\n          var cacheKey = componentName + ':' + propName;\n          if (\n            !manualPropTypeCallCache[cacheKey] &&\n            // Avoid spamming the console because they are often not actionable except for lib authors\n            manualPropTypeWarningCount < 3\n          ) {\n            printWarning(\n              'You are manually calling a React.PropTypes validation ' +\n              'function for the `' + propFullName + '` prop on `' + componentName + '`. This is deprecated ' +\n              'and will throw in the standalone `prop-types` package. ' +\n              'You may be seeing this warning due to a third-party PropTypes ' +\n              'library. See https://fb.me/react-warning-dont-call-proptypes ' + 'for details.'\n            );\n            manualPropTypeCallCache[cacheKey] = true;\n            manualPropTypeWarningCount++;\n          }\n        }\n      }\n      if (props[propName] == null) {\n        if (isRequired) {\n          if (props[propName] === null) {\n            return new PropTypeError('The ' + location + ' `' + propFullName + '` is marked as required ' + ('in `' + componentName + '`, but its value is `null`.'));\n          }\n          return new PropTypeError('The ' + location + ' `' + propFullName + '` is marked as required in ' + ('`' + componentName + '`, but its value is `undefined`.'));\n        }\n        return null;\n      } else {\n        return validate(props, propName, componentName, location, propFullName);\n      }\n    }\n\n    var chainedCheckType = checkType.bind(null, false);\n    chainedCheckType.isRequired = checkType.bind(null, true);\n\n    return chainedCheckType;\n  }\n\n  function createPrimitiveTypeChecker(expectedType) {\n    function validate(props, propName, componentName, location, propFullName, secret) {\n      var propValue = props[propName];\n      var propType = getPropType(propValue);\n      if (propType !== expectedType) {\n        // `propValue` being instance of, say, date/regexp, pass the 'object'\n        // check, but we can offer a more precise error message here rather than\n        // 'of type `object`'.\n        var preciseType = getPreciseType(propValue);\n\n        return new PropTypeError(\n          'Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + preciseType + '` supplied to `' + componentName + '`, expected ') + ('`' + expectedType + '`.'),\n          {expectedType: expectedType}\n        );\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createAnyTypeChecker() {\n    return createChainableTypeChecker(emptyFunctionThatReturnsNull);\n  }\n\n  function createArrayOfTypeChecker(typeChecker) {\n    function validate(props, propName, componentName, location, propFullName) {\n      if (typeof typeChecker !== 'function') {\n        return new PropTypeError('Property `' + propFullName + '` of component `' + componentName + '` has invalid PropType notation inside arrayOf.');\n      }\n      var propValue = props[propName];\n      if (!Array.isArray(propValue)) {\n        var propType = getPropType(propValue);\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + propType + '` supplied to `' + componentName + '`, expected an array.'));\n      }\n      for (var i = 0; i < propValue.length; i++) {\n        var error = typeChecker(propValue, i, componentName, location, propFullName + '[' + i + ']', ReactPropTypesSecret);\n        if (error instanceof Error) {\n          return error;\n        }\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createElementTypeChecker() {\n    function validate(props, propName, componentName, location, propFullName) {\n      var propValue = props[propName];\n      if (!isValidElement(propValue)) {\n        var propType = getPropType(propValue);\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + propType + '` supplied to `' + componentName + '`, expected a single ReactElement.'));\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createElementTypeTypeChecker() {\n    function validate(props, propName, componentName, location, propFullName) {\n      var propValue = props[propName];\n      if (!ReactIs.isValidElementType(propValue)) {\n        var propType = getPropType(propValue);\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + propType + '` supplied to `' + componentName + '`, expected a single ReactElement type.'));\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createInstanceTypeChecker(expectedClass) {\n    function validate(props, propName, componentName, location, propFullName) {\n      if (!(props[propName] instanceof expectedClass)) {\n        var expectedClassName = expectedClass.name || ANONYMOUS;\n        var actualClassName = getClassName(props[propName]);\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + actualClassName + '` supplied to `' + componentName + '`, expected ') + ('instance of `' + expectedClassName + '`.'));\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createEnumTypeChecker(expectedValues) {\n    if (!Array.isArray(expectedValues)) {\n      if (process.env.NODE_ENV !== 'production') {\n        if (arguments.length > 1) {\n          printWarning(\n            'Invalid arguments supplied to oneOf, expected an array, got ' + arguments.length + ' arguments. ' +\n            'A common mistake is to write oneOf(x, y, z) instead of oneOf([x, y, z]).'\n          );\n        } else {\n          printWarning('Invalid argument supplied to oneOf, expected an array.');\n        }\n      }\n      return emptyFunctionThatReturnsNull;\n    }\n\n    function validate(props, propName, componentName, location, propFullName) {\n      var propValue = props[propName];\n      for (var i = 0; i < expectedValues.length; i++) {\n        if (is(propValue, expectedValues[i])) {\n          return null;\n        }\n      }\n\n      var valuesString = JSON.stringify(expectedValues, function replacer(key, value) {\n        var type = getPreciseType(value);\n        if (type === 'symbol') {\n          return String(value);\n        }\n        return value;\n      });\n      return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of value `' + String(propValue) + '` ' + ('supplied to `' + componentName + '`, expected one of ' + valuesString + '.'));\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createObjectOfTypeChecker(typeChecker) {\n    function validate(props, propName, componentName, location, propFullName) {\n      if (typeof typeChecker !== 'function') {\n        return new PropTypeError('Property `' + propFullName + '` of component `' + componentName + '` has invalid PropType notation inside objectOf.');\n      }\n      var propValue = props[propName];\n      var propType = getPropType(propValue);\n      if (propType !== 'object') {\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + propType + '` supplied to `' + componentName + '`, expected an object.'));\n      }\n      for (var key in propValue) {\n        if (has(propValue, key)) {\n          var error = typeChecker(propValue, key, componentName, location, propFullName + '.' + key, ReactPropTypesSecret);\n          if (error instanceof Error) {\n            return error;\n          }\n        }\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createUnionTypeChecker(arrayOfTypeCheckers) {\n    if (!Array.isArray(arrayOfTypeCheckers)) {\n      process.env.NODE_ENV !== 'production' ? printWarning('Invalid argument supplied to oneOfType, expected an instance of array.') : void 0;\n      return emptyFunctionThatReturnsNull;\n    }\n\n    for (var i = 0; i < arrayOfTypeCheckers.length; i++) {\n      var checker = arrayOfTypeCheckers[i];\n      if (typeof checker !== 'function') {\n        printWarning(\n          'Invalid argument supplied to oneOfType. Expected an array of check functions, but ' +\n          'received ' + getPostfixForTypeWarning(checker) + ' at index ' + i + '.'\n        );\n        return emptyFunctionThatReturnsNull;\n      }\n    }\n\n    function validate(props, propName, componentName, location, propFullName) {\n      var expectedTypes = [];\n      for (var i = 0; i < arrayOfTypeCheckers.length; i++) {\n        var checker = arrayOfTypeCheckers[i];\n        var checkerResult = checker(props, propName, componentName, location, propFullName, ReactPropTypesSecret);\n        if (checkerResult == null) {\n          return null;\n        }\n        if (checkerResult.data && has(checkerResult.data, 'expectedType')) {\n          expectedTypes.push(checkerResult.data.expectedType);\n        }\n      }\n      var expectedTypesMessage = (expectedTypes.length > 0) ? ', expected one of type [' + expectedTypes.join(', ') + ']': '';\n      return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` supplied to ' + ('`' + componentName + '`' + expectedTypesMessage + '.'));\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createNodeChecker() {\n    function validate(props, propName, componentName, location, propFullName) {\n      if (!isNode(props[propName])) {\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` supplied to ' + ('`' + componentName + '`, expected a ReactNode.'));\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function invalidValidatorError(componentName, location, propFullName, key, type) {\n    return new PropTypeError(\n      (componentName || 'React class') + ': ' + location + ' type `' + propFullName + '.' + key + '` is invalid; ' +\n      'it must be a function, usually from the `prop-types` package, but received `' + type + '`.'\n    );\n  }\n\n  function createShapeTypeChecker(shapeTypes) {\n    function validate(props, propName, componentName, location, propFullName) {\n      var propValue = props[propName];\n      var propType = getPropType(propValue);\n      if (propType !== 'object') {\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type `' + propType + '` ' + ('supplied to `' + componentName + '`, expected `object`.'));\n      }\n      for (var key in shapeTypes) {\n        var checker = shapeTypes[key];\n        if (typeof checker !== 'function') {\n          return invalidValidatorError(componentName, location, propFullName, key, getPreciseType(checker));\n        }\n        var error = checker(propValue, key, componentName, location, propFullName + '.' + key, ReactPropTypesSecret);\n        if (error) {\n          return error;\n        }\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createStrictShapeTypeChecker(shapeTypes) {\n    function validate(props, propName, componentName, location, propFullName) {\n      var propValue = props[propName];\n      var propType = getPropType(propValue);\n      if (propType !== 'object') {\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type `' + propType + '` ' + ('supplied to `' + componentName + '`, expected `object`.'));\n      }\n      // We need to check all keys in case some are required but missing from props.\n      var allKeys = assign({}, props[propName], shapeTypes);\n      for (var key in allKeys) {\n        var checker = shapeTypes[key];\n        if (has(shapeTypes, key) && typeof checker !== 'function') {\n          return invalidValidatorError(componentName, location, propFullName, key, getPreciseType(checker));\n        }\n        if (!checker) {\n          return new PropTypeError(\n            'Invalid ' + location + ' `' + propFullName + '` key `' + key + '` supplied to `' + componentName + '`.' +\n            '\\nBad object: ' + JSON.stringify(props[propName], null, '  ') +\n            '\\nValid keys: ' + JSON.stringify(Object.keys(shapeTypes), null, '  ')\n          );\n        }\n        var error = checker(propValue, key, componentName, location, propFullName + '.' + key, ReactPropTypesSecret);\n        if (error) {\n          return error;\n        }\n      }\n      return null;\n    }\n\n    return createChainableTypeChecker(validate);\n  }\n\n  function isNode(propValue) {\n    switch (typeof propValue) {\n      case 'number':\n      case 'string':\n      case 'undefined':\n        return true;\n      case 'boolean':\n        return !propValue;\n      case 'object':\n        if (Array.isArray(propValue)) {\n          return propValue.every(isNode);\n        }\n        if (propValue === null || isValidElement(propValue)) {\n          return true;\n        }\n\n        var iteratorFn = getIteratorFn(propValue);\n        if (iteratorFn) {\n          var iterator = iteratorFn.call(propValue);\n          var step;\n          if (iteratorFn !== propValue.entries) {\n            while (!(step = iterator.next()).done) {\n              if (!isNode(step.value)) {\n                return false;\n              }\n            }\n          } else {\n            // Iterator will provide entry [k,v] tuples rather than values.\n            while (!(step = iterator.next()).done) {\n              var entry = step.value;\n              if (entry) {\n                if (!isNode(entry[1])) {\n                  return false;\n                }\n              }\n            }\n          }\n        } else {\n          return false;\n        }\n\n        return true;\n      default:\n        return false;\n    }\n  }\n\n  function isSymbol(propType, propValue) {\n    // Native Symbol.\n    if (propType === 'symbol') {\n      return true;\n    }\n\n    // falsy value can't be a Symbol\n    if (!propValue) {\n      return false;\n    }\n\n    // 19.4.3.5 Symbol.prototype[@@toStringTag] === 'Symbol'\n    if (propValue['@@toStringTag'] === 'Symbol') {\n      return true;\n    }\n\n    // Fallback for non-spec compliant Symbols which are polyfilled.\n    if (typeof Symbol === 'function' && propValue instanceof Symbol) {\n      return true;\n    }\n\n    return false;\n  }\n\n  // Equivalent of `typeof` but with special handling for array and regexp.\n  function getPropType(propValue) {\n    var propType = typeof propValue;\n    if (Array.isArray(propValue)) {\n      return 'array';\n    }\n    if (propValue instanceof RegExp) {\n      // Old webkits (at least until Android 4.0) return 'function' rather than\n      // 'object' for typeof a RegExp. We'll normalize this here so that /bla/\n      // passes PropTypes.object.\n      return 'object';\n    }\n    if (isSymbol(propType, propValue)) {\n      return 'symbol';\n    }\n    return propType;\n  }\n\n  // This handles more types than `getPropType`. Only used for error messages.\n  // See `createPrimitiveTypeChecker`.\n  function getPreciseType(propValue) {\n    if (typeof propValue === 'undefined' || propValue === null) {\n      return '' + propValue;\n    }\n    var propType = getPropType(propValue);\n    if (propType === 'object') {\n      if (propValue instanceof Date) {\n        return 'date';\n      } else if (propValue instanceof RegExp) {\n        return 'regexp';\n      }\n    }\n    return propType;\n  }\n\n  // Returns a string that is postfixed to a warning about an invalid type.\n  // For example, \"undefined\" or \"of type array\"\n  function getPostfixForTypeWarning(value) {\n    var type = getPreciseType(value);\n    switch (type) {\n      case 'array':\n      case 'object':\n        return 'an ' + type;\n      case 'boolean':\n      case 'date':\n      case 'regexp':\n        return 'a ' + type;\n      default:\n        return type;\n    }\n  }\n\n  // Returns class name of the object, if any.\n  function getClassName(propValue) {\n    if (!propValue.constructor || !propValue.constructor.name) {\n      return ANONYMOUS;\n    }\n    return propValue.constructor.name;\n  }\n\n  ReactPropTypes.checkPropTypes = checkPropTypes;\n  ReactPropTypes.resetWarningCache = checkPropTypes.resetWarningCache;\n  ReactPropTypes.PropTypes = ReactPropTypes;\n\n  return ReactPropTypes;\n};\n", "/**\n * Copyright (c) 2013-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\nvar ReactPropTypesSecret = require('./lib/ReactPropTypesSecret');\n\nfunction emptyFunction() {}\nfunction emptyFunctionWithReset() {}\nemptyFunctionWithReset.resetWarningCache = emptyFunction;\n\nmodule.exports = function() {\n  function shim(props, propName, componentName, location, propFullName, secret) {\n    if (secret === ReactPropTypesSecret) {\n      // It is still safe when called from React.\n      return;\n    }\n    var err = new Error(\n      'Calling PropTypes validators directly is not supported by the `prop-types` package. ' +\n      'Use PropTypes.checkPropTypes() to call them. ' +\n      'Read more at http://fb.me/use-check-prop-types'\n    );\n    err.name = 'Invariant Violation';\n    throw err;\n  };\n  shim.isRequired = shim;\n  function getShim() {\n    return shim;\n  };\n  // Important!\n  // Keep this list in sync with production version in `./factoryWithTypeCheckers.js`.\n  var ReactPropTypes = {\n    array: shim,\n    bigint: shim,\n    bool: shim,\n    func: shim,\n    number: shim,\n    object: shim,\n    string: shim,\n    symbol: shim,\n\n    any: shim,\n    arrayOf: getShim,\n    element: shim,\n    elementType: shim,\n    instanceOf: getShim,\n    node: shim,\n    objectOf: getShim,\n    oneOf: getShim,\n    oneOfType: getShim,\n    shape: getShim,\n    exact: getShim,\n\n    checkPropTypes: emptyFunctionWithReset,\n    resetWarningCache: emptyFunction\n  };\n\n  ReactPropTypes.PropTypes = ReactPropTypes;\n\n  return ReactPropTypes;\n};\n", "/**\n * Copyright (c) 2013-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\nif (process.env.NODE_ENV !== 'production') {\n  var ReactIs = require('react-is');\n\n  // By explicitly using `prop-types` you are opting into new development behavior.\n  // http://fb.me/prop-types-in-prod\n  var throwOnDirectAccess = true;\n  module.exports = require('./factoryWithTypeCheckers')(ReactIs.isElement, throwOnDirectAccess);\n} else {\n  // By explicitly using `prop-types` you are opting into new production behavior.\n  // http://fb.me/prop-types-in-prod\n  module.exports = require('./factoryWithThrowingShims')();\n}\n", "/*!\n\tCopyright (c) 2018 <PERSON>.\n\tLicensed under the MIT License (MIT), see\n\thttp://jedwatson.github.io/classnames\n*/\n/* global define */\n\n(function () {\n\t'use strict';\n\n\tvar hasOwn = {}.hasOwnProperty;\n\n\tfunction classNames () {\n\t\tvar classes = '';\n\n\t\tfor (var i = 0; i < arguments.length; i++) {\n\t\t\tvar arg = arguments[i];\n\t\t\tif (arg) {\n\t\t\t\tclasses = appendClass(classes, parseValue(arg));\n\t\t\t}\n\t\t}\n\n\t\treturn classes;\n\t}\n\n\tfunction parseValue (arg) {\n\t\tif (typeof arg === 'string' || typeof arg === 'number') {\n\t\t\treturn arg;\n\t\t}\n\n\t\tif (typeof arg !== 'object') {\n\t\t\treturn '';\n\t\t}\n\n\t\tif (Array.isArray(arg)) {\n\t\t\treturn classNames.apply(null, arg);\n\t\t}\n\n\t\tif (arg.toString !== Object.prototype.toString && !arg.toString.toString().includes('[native code]')) {\n\t\t\treturn arg.toString();\n\t\t}\n\n\t\tvar classes = '';\n\n\t\tfor (var key in arg) {\n\t\t\tif (hasOwn.call(arg, key) && arg[key]) {\n\t\t\t\tclasses = appendClass(classes, key);\n\t\t\t}\n\t\t}\n\n\t\treturn classes;\n\t}\n\n\tfunction appendClass (value, newClass) {\n\t\tif (!newClass) {\n\t\t\treturn value;\n\t\t}\n\t\n\t\tif (value) {\n\t\t\treturn value + ' ' + newClass;\n\t\t}\n\t\n\t\treturn value + newClass;\n\t}\n\n\tif (typeof module !== 'undefined' && module.exports) {\n\t\tclassNames.default = classNames;\n\t\tmodule.exports = classNames;\n\t} else if (typeof define === 'function' && typeof define.amd === 'object' && define.amd) {\n\t\t// register as 'classnames', consistent with npm package name\n\t\tdefine('classnames', [], function () {\n\t\t\treturn classNames;\n\t\t});\n\t} else {\n\t\twindow.classNames = classNames;\n\t}\n}());\n", "import PropTypes from 'prop-types'\nimport React, { HTMLAttributes, forwardRef, useState, useMemo } from 'react'\nimport classNames from 'classnames'\nimport './CIcon.css'\n\nexport interface CIconProps extends Omit<HTMLAttributes<SVGSVGElement>, 'content'> {\n  /**\n   * A string of all className you want applied to the component.\n   */\n  className?: string\n  /**\n   * Use `icon={...}` instead of\n   *\n   * @deprecated 3.0\n   */\n  content?: string | string[]\n  /**\n   * Use for replacing default CIcon component classes. Prop is overriding the 'size' prop.\n   */\n  customClassName?: string | string[]\n  /**\n   * Name of the icon placed in React object or SVG content.\n   */\n  icon?: string | string[]\n  /**\n   * The height attribute defines the vertical length of an icon.\n   */\n  height?: number\n  /**\n   * Use `icon=\"...\"` instead of\n   *\n   * @deprecated 3.0\n   */\n  name?: string\n  /**\n   * Size of the icon. Available sizes: 'sm', 'lg', 'xl', 'xxl', '3xl...9xl', 'custom', 'custom-size'.\n   */\n  size?:\n    | 'custom'\n    | 'custom-size'\n    | 'sm'\n    | 'lg'\n    | 'xl'\n    | 'xxl'\n    | '3xl'\n    | '4xl'\n    | '5xl'\n    | '6xl'\n    | '7xl'\n    | '8xl'\n    | '9xl'\n  /**\n   * If defined component will be rendered using 'use' tag.\n   */\n  use?: string\n  /**\n   * The viewBox attribute defines the position and dimension of an SVG viewport.\n   */\n  viewBox?: string\n  /**\n   * Title tag content.\n   */\n  title?: string\n  /**\n   * The width attribute defines the horizontal length of an icon.\n   */\n  width?: number\n}\n\nconst toCamelCase = (str: string) => {\n  return str\n    .replace(/([-_][a-z0-9])/gi, ($1) => {\n      return $1.toUpperCase()\n    })\n    .replace(/-/gi, '')\n}\n\nexport const CIcon = forwardRef<SVGSVGElement, CIconProps>(\n  (\n    { className, content, customClassName, height, icon, name, size, title, use, width, ...rest },\n    ref,\n  ) => {\n    const [change, setChange] = useState(0)\n    const _icon = icon || content || name\n\n    if (content) {\n      process &&\n        process.env &&\n        process.env.NODE_ENV === 'development' &&\n        console.warn(\n          '[CIcon] The `content` property is deprecated and will be removed in v3, please use `icon={...}` instead of.',\n        )\n    }\n    if (name) {\n      process &&\n        process.env &&\n        process.env.NODE_ENV === 'development' &&\n        console.warn(\n          '[CIcon] The `name` property is deprecated and will be removed in v3, please use `icon=\"...\"` instead of.',\n        )\n    }\n\n    useMemo(() => setChange(change + 1), [_icon, JSON.stringify(_icon)])\n\n    const titleCode = title ? `<title>${title}</title>` : ''\n\n    const code = useMemo(() => {\n      const iconName =\n        _icon && typeof _icon === 'string' && _icon.includes('-') ? toCamelCase(_icon) : _icon\n\n      if (Array.isArray(_icon)) {\n        return _icon\n      }\n\n      if (typeof _icon === 'string' && (React as { [key: string]: any })['icons']) {\n        return (React as { [key: string]: any })[iconName as string]\n      }\n    }, [change])\n\n    const iconCode = useMemo(() => {\n      return Array.isArray(code) ? code[1] || code[0] : code\n    }, [change])\n\n    const scale = (() => {\n      return Array.isArray(code) && code.length > 1 ? code[0] : '64 64'\n    })()\n\n    const viewBox = (() => {\n      return rest['viewBox'] || `0 0 ${scale}`\n    })()\n\n    const _className = customClassName\n      ? classNames(customClassName)\n      : classNames(\n          'icon',\n          {\n            [`icon-${size}`]: size,\n            [`icon-custom-size`]: height || width,\n          },\n          className,\n        )\n\n    return (\n      <>\n        {use ? (\n          <svg\n            xmlns=\"http://www.w3.org/2000/svg\"\n            className={_className}\n            {...(height && { height: height })}\n            {...(width && { width: width })}\n            role=\"img\"\n            aria-hidden=\"true\"\n            {...rest}\n            ref={ref}\n          >\n            <use href={use}></use>\n          </svg>\n        ) : (\n          <svg\n            xmlns=\"http://www.w3.org/2000/svg\"\n            viewBox={viewBox}\n            className={_className}\n            {...(height && { height: height })}\n            {...(width && { width: width })}\n            role=\"img\"\n            aria-hidden=\"true\"\n            dangerouslySetInnerHTML={{ __html: titleCode + iconCode }}\n            {...rest}\n            ref={ref}\n          />\n        )}\n        {title && <span className=\"visually-hidden\">{title}</span>}\n      </>\n    )\n  },\n)\n\nCIcon.propTypes = {\n  className: PropTypes.string,\n  content: PropTypes.oneOfType([PropTypes.array, PropTypes.string]),\n  customClassName: PropTypes.string,\n  height: PropTypes.number,\n  icon: PropTypes.oneOfType([PropTypes.array, PropTypes.string]),\n  name: PropTypes.string,\n  size: PropTypes.oneOf([\n    'custom',\n    'custom-size',\n    'sm',\n    'lg',\n    'xl',\n    'xxl',\n    '3xl',\n    '4xl',\n    '5xl',\n    '6xl',\n    '7xl',\n    '8xl',\n    '9xl',\n  ]),\n  title: PropTypes.string,\n  use: PropTypes.string,\n  viewBox: PropTypes.string,\n  width: PropTypes.number,\n}\n\nCIcon.displayName = 'CIcon'\n", "import React, { Children, HTMLAttributes, forwardRef } from 'react'\nimport PropTypes from 'prop-types'\nimport classNames from 'classnames'\nimport './CIcon.css'\n\nexport interface CIconSvgProps extends Omit<HTMLAttributes<SVGSVGElement>, 'content'> {\n  /**\n   * A string of all className you want applied to the component.\n   */\n  className?: string\n  /**\n   * Use for replacing default CIcon component classes. Prop is overriding the 'size' prop.\n   */\n  customClassName?: string | string[]\n  /**\n   * The height attribute defines the vertical length of an icon.\n   */\n  height?: number\n  /**\n   * Size of the icon. Available sizes: 'sm', 'lg', 'xl', 'xxl', '3xl...9xl', 'custom', 'custom-size'.\n   */\n  size?:\n    | 'custom'\n    | 'custom-size'\n    | 'sm'\n    | 'lg'\n    | 'xl'\n    | 'xxl'\n    | '3xl'\n    | '4xl'\n    | '5xl'\n    | '6xl'\n    | '7xl'\n    | '8xl'\n    | '9xl'\n  /**\n   * Title tag content.\n   */\n  title?: string\n  /**\n   * The width attribute defines the horizontal length of an icon.\n   */\n  width?: number\n}\n\nexport const CIconSvg = forwardRef<SVGSVGElement, CIconSvgProps>(\n  ({ children, className, customClassName, height, size, title, width, ...rest }, ref) => {\n    const _className = customClassName\n      ? classNames(customClassName)\n      : classNames(\n          'icon',\n          {\n            [`icon-${size}`]: size,\n            [`icon-custom-size`]: height || width,\n          },\n          className,\n        )\n\n    return (\n      <>\n        {Children.map(children, (child) => {\n          if (React.isValidElement(child)) {\n            return React.cloneElement(child as React.ReactElement<any>, {\n              'aria-hidden': true,\n              className: _className,\n              focusable: 'false',\n              ref: ref,\n              role: 'img',\n              ...rest,\n            })\n          }\n\n          return\n        })}\n        {title && <span className=\"visually-hidden\">{title}</span>}\n      </>\n    )\n  },\n)\n\nCIconSvg.propTypes = {\n  className: PropTypes.string,\n  customClassName: PropTypes.string,\n  height: PropTypes.number,\n  size: PropTypes.oneOf([\n    'custom',\n    'custom-size',\n    'sm',\n    'lg',\n    'xl',\n    'xxl',\n    '3xl',\n    '4xl',\n    '5xl',\n    '6xl',\n    '7xl',\n    '8xl',\n    '9xl',\n  ]),\n  title: PropTypes.string,\n  width: PropTypes.number,\n}\n\nCIconSvg.displayName = 'CIconSvg'\n"], "mappings": ";;;;;;;;;AA+BO,IAAI,WAAW,WAAW;AAC7B,aAAW,OAAO,UAAU,SAASA,UAAS,GAAG;AAC7C,aAAS,GAAG,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,GAAG,KAAK;AACjD,UAAI,UAAU,CAAC;AACf,eAAS,KAAK,EAAG,KAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC,EAAG,GAAE,CAAC,IAAI,EAAE,CAAC;IACvF;AACQ,WAAO;EACf;AACI,SAAO,SAAS,MAAM,MAAM,SAAS;AACzC;AAEO,SAAS,OAAO,GAAG,GAAG;AACzB,MAAI,IAAI,CAAA;AACR,WAAS,KAAK,EAAG,KAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC,KAAK,EAAE,QAAQ,CAAC,IAAI;AAC9E,MAAE,CAAC,IAAI,EAAE,CAAC;AACd,MAAI,KAAK,QAAQ,OAAO,OAAO,0BAA0B;AACrD,aAAS,IAAI,GAAG,IAAI,OAAO,sBAAsB,CAAC,GAAG,IAAI,EAAE,QAAQ,KAAK;AACpE,UAAI,EAAE,QAAQ,EAAE,CAAC,CAAC,IAAI,KAAK,OAAO,UAAU,qBAAqB,KAAK,GAAG,EAAE,CAAC,CAAC;AACzE,UAAE,EAAE,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;IAChC;AACI,SAAO;AACX;;;;;;;;;;;AEvCA,MAAI,MAAuC;AACzC,KAAC,WAAW;AAKd,UAAI,YAAY,OAAO,WAAW,cAAc,OAAO;AACvD,UAAI,qBAAqB,YAAY,OAAO,IAAI,eAAe,IAAI;AACnE,UAAI,oBAAoB,YAAY,OAAO,IAAI,cAAc,IAAI;AACjE,UAAI,sBAAsB,YAAY,OAAO,IAAI,gBAAgB,IAAI;AACrE,UAAI,yBAAyB,YAAY,OAAO,IAAI,mBAAmB,IAAI;AAC3E,UAAI,sBAAsB,YAAY,OAAO,IAAI,gBAAgB,IAAI;AACrE,UAAI,sBAAsB,YAAY,OAAO,IAAI,gBAAgB,IAAI;AACrE,UAAI,qBAAqB,YAAY,OAAO,IAAI,eAAe,IAAI;AAGnE,UAAI,wBAAwB,YAAY,OAAO,IAAI,kBAAkB,IAAI;AACzE,UAAI,6BAA6B,YAAY,OAAO,IAAI,uBAAuB,IAAI;AACnF,UAAI,yBAAyB,YAAY,OAAO,IAAI,mBAAmB,IAAI;AAC3E,UAAI,sBAAsB,YAAY,OAAO,IAAI,gBAAgB,IAAI;AACrE,UAAI,2BAA2B,YAAY,OAAO,IAAI,qBAAqB,IAAI;AAC/E,UAAI,kBAAkB,YAAY,OAAO,IAAI,YAAY,IAAI;AAC7D,UAAI,kBAAkB,YAAY,OAAO,IAAI,YAAY,IAAI;AAC7D,UAAI,mBAAmB,YAAY,OAAO,IAAI,aAAa,IAAI;AAC/D,UAAI,yBAAyB,YAAY,OAAO,IAAI,mBAAmB,IAAI;AAC3E,UAAI,uBAAuB,YAAY,OAAO,IAAI,iBAAiB,IAAI;AACvE,UAAI,mBAAmB,YAAY,OAAO,IAAI,aAAa,IAAI;AAE/D,eAAS,mBAAmB,MAAM;AAChC,eAAO,OAAO,SAAS,YAAY,OAAO,SAAS;QACnD,SAAS,uBAAuB,SAAS,8BAA8B,SAAS,uBAAuB,SAAS,0BAA0B,SAAS,uBAAuB,SAAS,4BAA4B,OAAO,SAAS,YAAY,SAAS,SAAS,KAAK,aAAa,mBAAmB,KAAK,aAAa,mBAAmB,KAAK,aAAa,uBAAuB,KAAK,aAAa,sBAAsB,KAAK,aAAa,0BAA0B,KAAK,aAAa,0BAA0B,KAAK,aAAa,wBAAwB,KAAK,aAAa,oBAAoB,KAAK,aAAa;;AAGplB,eAAS,OAAO,QAAQ;AACtB,YAAI,OAAO,WAAW,YAAY,WAAW,MAAM;AACjD,cAAI,WAAW,OAAO;AAEtB,kBAAQ,UAAQ;YACd,KAAK;AACH,kBAAI,OAAO,OAAO;AAElB,sBAAQ,MAAI;gBACV,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;AACH,yBAAO;gBAET;AACE,sBAAI,eAAe,QAAQ,KAAK;AAEhC,0BAAQ,cAAY;oBAClB,KAAK;oBACL,KAAK;oBACL,KAAK;oBACL,KAAK;oBACL,KAAK;AACH,6BAAO;oBAET;AACE,6BAAO;;;YAKjB,KAAK;AACH,qBAAO;;;AAIb,eAAO;;AAGT,UAAI,YAAY;AAChB,UAAI,iBAAiB;AACrB,UAAI,kBAAkB;AACtB,UAAI,kBAAkB;AACtB,UAAI,UAAU;AACd,UAAI,aAAa;AACjB,UAAI,WAAW;AACf,UAAI,OAAO;AACX,UAAI,OAAO;AACX,UAAI,SAAS;AACb,UAAI,WAAW;AACf,UAAI,aAAa;AACjB,UAAI,WAAW;AACf,UAAI,sCAAsC;AAE1C,eAAS,YAAY,QAAQ;AAC3B;AACE,cAAI,CAAC,qCAAqC;AACxC,kDAAsC;AAEtC,oBAAQ,MAAM,EAAE,+KAAyL;;;AAI7M,eAAO,iBAAiB,MAAM,KAAK,OAAO,MAAM,MAAM;;AAExD,eAAS,iBAAiB,QAAQ;AAChC,eAAO,OAAO,MAAM,MAAM;;AAE5B,eAAS,kBAAkB,QAAQ;AACjC,eAAO,OAAO,MAAM,MAAM;;AAE5B,eAAS,kBAAkB,QAAQ;AACjC,eAAO,OAAO,MAAM,MAAM;;AAE5B,eAAS,UAAU,QAAQ;AACzB,eAAO,OAAO,WAAW,YAAY,WAAW,QAAQ,OAAO,aAAa;;AAE9E,eAAS,aAAa,QAAQ;AAC5B,eAAO,OAAO,MAAM,MAAM;;AAE5B,eAAS,WAAW,QAAQ;AAC1B,eAAO,OAAO,MAAM,MAAM;;AAE5B,eAAS,OAAO,QAAQ;AACtB,eAAO,OAAO,MAAM,MAAM;;AAE5B,eAAS,OAAO,QAAQ;AACtB,eAAO,OAAO,MAAM,MAAM;;AAE5B,eAAS,SAAS,QAAQ;AACxB,eAAO,OAAO,MAAM,MAAM;;AAE5B,eAAS,WAAW,QAAQ;AAC1B,eAAO,OAAO,MAAM,MAAM;;AAE5B,eAAS,aAAa,QAAQ;AAC5B,eAAO,OAAO,MAAM,MAAM;;AAE5B,eAAS,WAAW,QAAQ;AAC1B,eAAO,OAAO,MAAM,MAAM;;AAGX,0BAAA,YAAG;AACE,0BAAA,iBAAG;AACF,0BAAA,kBAAG;AACH,0BAAA,kBAAG;AACX,0BAAA,UAAG;AACA,0BAAA,aAAG;AACL,0BAAA,WAAG;AACP,0BAAA,OAAG;AACH,0BAAA,OAAG;AACD,0BAAA,SAAG;AACD,0BAAA,WAAG;AACD,0BAAA,aAAG;AACL,0BAAA,WAAG;AACA,0BAAA,cAAG;AACE,0BAAA,mBAAG;AACF,0BAAA,oBAAG;AACH,0BAAA,oBAAG;AACX,0BAAA,YAAG;AACA,0BAAA,eAAG;AACL,0BAAA,aAAG;AACP,0BAAA,SAAG;AACH,0BAAA,SAAG;AACD,0BAAA,WAAG;AACD,0BAAA,aAAG;AACD,0BAAA,eAAG;AACL,0BAAA,aAAG;AACK,0BAAA,qBAAG;AACf,0BAAA,SAAG;IACjB,GAAG;EACH;;;;;;;AClLA,MAAI,OAAuC;AACzCC,YAAA,UAAiBC,8BAAA;EACnB,OAAO;AACLD,YAAA,UAAiBE,2BAAA;EACnB;;;;;;;;ACEA,MAAI,wBAAwB,OAAO;AACnC,MAAI,iBAAiB,OAAO,UAAU;AACtC,MAAI,mBAAmB,OAAO,UAAU;AAExC,WAAS,SAAS,KAAK;AACtB,QAAI,QAAQ,QAAQ,QAAQ,QAAW;AACtC,YAAM,IAAI,UAAU,uDAAuD;;AAG5E,WAAO,OAAO,GAAG;;AAGlB,WAAS,kBAAkB;AAC1B,QAAI;AACH,UAAI,CAAC,OAAO,QAAQ;AACnB,eAAO;;AAMR,UAAI,QAAQ,IAAI,OAAO,KAAK;AAC5B,YAAM,CAAC,IAAI;AACX,UAAI,OAAO,oBAAoB,KAAK,EAAE,CAAC,MAAM,KAAK;AACjD,eAAO;;AAIR,UAAI,QAAQ,CAAA;AACZ,eAAS,IAAI,GAAG,IAAI,IAAI,KAAK;AAC5B,cAAM,MAAM,OAAO,aAAa,CAAC,CAAC,IAAI;;AAEvC,UAAI,SAAS,OAAO,oBAAoB,KAAK,EAAE,IAAI,SAAU,GAAG;AAC/D,eAAO,MAAM,CAAC;MACjB,CAAG;AACD,UAAI,OAAO,KAAK,EAAE,MAAM,cAAc;AACrC,eAAO;;AAIR,UAAI,QAAQ,CAAA;AACZ,6BAAuB,MAAM,EAAE,EAAE,QAAQ,SAAU,QAAQ;AAC1D,cAAM,MAAM,IAAI;MACnB,CAAG;AACD,UAAI,OAAO,KAAK,OAAO,OAAO,CAAA,GAAI,KAAK,CAAC,EAAE,KAAK,EAAE,MAC/C,wBAAwB;AACzB,eAAO;;AAGR,aAAO;aACC,KAAK;AAEb,aAAO;;;AAIT,iBAAiB,gBAAe,IAAK,OAAO,SAAS,SAAU,QAAQ,QAAQ;AAC9E,QAAI;AACJ,QAAI,KAAK,SAAS,MAAM;AACxB,QAAI;AAEJ,aAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AAC1C,aAAO,OAAO,UAAU,CAAC,CAAC;AAE1B,eAAS,OAAO,MAAM;AACrB,YAAI,eAAe,KAAK,MAAM,GAAG,GAAG;AACnC,aAAG,GAAG,IAAI,KAAK,GAAG;;;AAIpB,UAAI,uBAAuB;AAC1B,kBAAU,sBAAsB,IAAI;AACpC,iBAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACxC,cAAI,iBAAiB,KAAK,MAAM,QAAQ,CAAC,CAAC,GAAG;AAC5C,eAAG,QAAQ,CAAC,CAAC,IAAI,KAAK,QAAQ,CAAC,CAAC;;;;;AAMpC,WAAO;;;;;;;;;AC/ER,MAAI,uBAAuB;AAE3B,2BAAiB;;;;;;;;ACXjB,QAAiB,SAAS,KAAK,KAAK,OAAO,UAAU,cAAc;;;;;;;;ACSnE,MAAI,eAAe,WAAW;EAAA;AAE9B,MAAI,MAAuC;AACzC,QAAI,uBAAuBD,4BAAA;AAC3B,QAAI,qBAAqB,CAAA;AACzB,QAAIE,OAAMD,WAAA;AAEV,mBAAe,SAAS,MAAM;AAC5B,UAAI,UAAU,cAAc;AAC5B,UAAI,OAAO,YAAY,aAAa;AAClC,gBAAQ,MAAM,OAAO;;AAEvB,UAAI;AAIF,cAAM,IAAI,MAAM,OAAO;MAC7B,SAAa,GAAG;MAAA;IAChB;;AAcA,WAAS,eAAe,WAAW,QAAQ,UAAU,eAAe,UAAU;AAC5E,QAAI,MAAuC;AACzC,eAAS,gBAAgB,WAAW;AAClC,YAAIC,KAAI,WAAW,YAAY,GAAG;AAChC,cAAI;AAIJ,cAAI;AAGF,gBAAI,OAAO,UAAU,YAAY,MAAM,YAAY;AACjD,kBAAI,MAAM;iBACP,iBAAiB,iBAAiB,OAAO,WAAW,YAAY,eAAe,+FACC,OAAO,UAAU,YAAY,IAAI;cAEhI;AACY,kBAAI,OAAO;AACX,oBAAM;;AAER,oBAAQ,UAAU,YAAY,EAAE,QAAQ,cAAc,eAAe,UAAU,MAAM,oBAAoB;mBAClG,IAAI;AACX,oBAAQ;;AAEV,cAAI,SAAS,EAAE,iBAAiB,QAAQ;AACtC;eACG,iBAAiB,iBAAiB,6BACnC,WAAW,OAAO,eAAe,6FAC6B,OAAO,QAAQ;YAIzF;;AAEQ,cAAI,iBAAiB,SAAS,EAAE,MAAM,WAAW,qBAAqB;AAGpE,+BAAmB,MAAM,OAAO,IAAI;AAEpC,gBAAI,QAAQ,WAAW,SAAQ,IAAK;AAEpC;cACE,YAAY,WAAW,YAAY,MAAM,WAAW,SAAS,OAAO,QAAQ;YACxF;;;;;;AAYA,iBAAe,oBAAoB,WAAW;AAC5C,QAAI,MAAuC;AACzC,2BAAqB,CAAA;;;AAIzB,qBAAiB;;;;;;;;AC7FjB,MAAI,UAAUF,eAAA;AACd,MAAI,SAASC,oBAAA;AAEb,MAAI,uBAAuBE,4BAAA;AAC3B,MAAID,OAAME,WAAA;AACV,MAAI,iBAAiBC,sBAAA;AAErB,MAAI,eAAe,WAAW;EAAA;AAE9B,MAAI,MAAuC;AACzC,mBAAe,SAAS,MAAM;AAC5B,UAAI,UAAU,cAAc;AAC5B,UAAI,OAAO,YAAY,aAAa;AAClC,gBAAQ,MAAM,OAAO;;AAEvB,UAAI;AAIF,cAAM,IAAI,MAAM,OAAO;MAC7B,SAAa,GAAG;MAAA;IAChB;;AAGA,WAAS,+BAA+B;AACtC,WAAO;;AAGT,4BAAiB,SAAS,gBAAgB,qBAAqB;AAE7D,QAAI,kBAAkB,OAAO,WAAW,cAAc,OAAO;AAC7D,QAAI,uBAAuB;AAgB3B,aAAS,cAAc,eAAe;AACpC,UAAI,aAAa,kBAAkB,mBAAmB,cAAc,eAAe,KAAK,cAAc,oBAAoB;AAC1H,UAAI,OAAO,eAAe,YAAY;AACpC,eAAO;;;AAmDX,QAAI,YAAY;AAIhB,QAAI,iBAAiB;MACnB,OAAO,2BAA2B,OAAO;MACzC,QAAQ,2BAA2B,QAAQ;MAC3C,MAAM,2BAA2B,SAAS;MAC1C,MAAM,2BAA2B,UAAU;MAC3C,QAAQ,2BAA2B,QAAQ;MAC3C,QAAQ,2BAA2B,QAAQ;MAC3C,QAAQ,2BAA2B,QAAQ;MAC3C,QAAQ,2BAA2B,QAAQ;MAE3C,KAAK,qBAAoB;MACzB,SAAS;MACT,SAAS,yBAAwB;MACjC,aAAa,6BAA4B;MACzC,YAAY;MACZ,MAAM,kBAAiB;MACvB,UAAU;MACV,OAAO;MACP,WAAW;MACX,OAAO;MACP,OAAO;IACX;AAOE,aAAS,GAAG,GAAG,GAAG;AAEhB,UAAI,MAAM,GAAG;AAGX,eAAO,MAAM,KAAK,IAAI,MAAM,IAAI;MACtC,OAAW;AAEL,eAAO,MAAM,KAAK,MAAM;;;AAY5B,aAAS,cAAc,SAAS,MAAM;AACpC,WAAK,UAAU;AACf,WAAK,OAAO,QAAQ,OAAO,SAAS,WAAW,OAAM,CAAA;AACrD,WAAK,QAAQ;;AAGf,kBAAc,YAAY,MAAM;AAEhC,aAAS,2BAA2B,UAAU;AAC5C,UAAI,MAAuC;AACzC,YAAI,0BAA0B,CAAA;AAC9B,YAAI,6BAA6B;;AAEnC,eAAS,UAAU,YAAY,OAAO,UAAU,eAAe,UAAU,cAAc,QAAQ;AAC7F,wBAAgB,iBAAiB;AACjC,uBAAe,gBAAgB;AAE/B,YAAI,WAAW,sBAAsB;AACnC,cAAI,qBAAqB;AAEvB,gBAAI,MAAM,IAAI;cACZ;YAGZ;AACU,gBAAI,OAAO;AACX,kBAAM;UAChB,WAA4D,OAAO,YAAY,aAAa;AAElF,gBAAI,WAAW,gBAAgB,MAAM;AACrC,gBACE,CAAC,wBAAwB,QAAQ;YAEjC,6BAA6B,GAC7B;AACA;gBACE,6EACuB,eAAe,gBAAgB,gBAAgB;cAIpF;AACY,sCAAwB,QAAQ,IAAI;AACpC;;;;AAIN,YAAI,MAAM,QAAQ,KAAK,MAAM;AAC3B,cAAI,YAAY;AACd,gBAAI,MAAM,QAAQ,MAAM,MAAM;AAC5B,qBAAO,IAAI,cAAc,SAAS,WAAW,OAAO,eAAe,8BAA8B,SAAS,gBAAgB,8BAA8B;;AAE1J,mBAAO,IAAI,cAAc,SAAS,WAAW,OAAO,eAAe,iCAAiC,MAAM,gBAAgB,mCAAmC;;AAE/J,iBAAO;QACf,OAAa;AACL,iBAAO,SAAS,OAAO,UAAU,eAAe,UAAU,YAAY;;;AAI1E,UAAI,mBAAmB,UAAU,KAAK,MAAM,KAAK;AACjD,uBAAiB,aAAa,UAAU,KAAK,MAAM,IAAI;AAEvD,aAAO;;AAGT,aAAS,2BAA2B,cAAc;AAChD,eAAS,SAAS,OAAO,UAAU,eAAe,UAAU,cAAc,QAAQ;AAChF,YAAI,YAAY,MAAM,QAAQ;AAC9B,YAAI,WAAW,YAAY,SAAS;AACpC,YAAI,aAAa,cAAc;AAI7B,cAAI,cAAc,eAAe,SAAS;AAE1C,iBAAO,IAAI;YACT,aAAa,WAAW,OAAO,eAAe,gBAAgB,MAAM,cAAc,oBAAoB,gBAAgB,mBAAmB,MAAM,eAAe;YAC9J,EAAC,aAA0B;UACrC;;AAEM,eAAO;;AAET,aAAO,2BAA2B,QAAQ;;AAG5C,aAAS,uBAAuB;AAC9B,aAAO,2BAA2B,4BAA4B;;AAGhE,aAAS,yBAAyB,aAAa;AAC7C,eAAS,SAAS,OAAO,UAAU,eAAe,UAAU,cAAc;AACxE,YAAI,OAAO,gBAAgB,YAAY;AACrC,iBAAO,IAAI,cAAc,eAAe,eAAe,qBAAqB,gBAAgB,iDAAiD;;AAE/I,YAAI,YAAY,MAAM,QAAQ;AAC9B,YAAI,CAAC,MAAM,QAAQ,SAAS,GAAG;AAC7B,cAAI,WAAW,YAAY,SAAS;AACpC,iBAAO,IAAI,cAAc,aAAa,WAAW,OAAO,eAAe,gBAAgB,MAAM,WAAW,oBAAoB,gBAAgB,wBAAwB;;AAEtK,iBAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,cAAI,QAAQ,YAAY,WAAW,GAAG,eAAe,UAAU,eAAe,MAAM,IAAI,KAAK,oBAAoB;AACjH,cAAI,iBAAiB,OAAO;AAC1B,mBAAO;;;AAGX,eAAO;;AAET,aAAO,2BAA2B,QAAQ;;AAG5C,aAAS,2BAA2B;AAClC,eAAS,SAAS,OAAO,UAAU,eAAe,UAAU,cAAc;AACxE,YAAI,YAAY,MAAM,QAAQ;AAC9B,YAAI,CAAC,eAAe,SAAS,GAAG;AAC9B,cAAI,WAAW,YAAY,SAAS;AACpC,iBAAO,IAAI,cAAc,aAAa,WAAW,OAAO,eAAe,gBAAgB,MAAM,WAAW,oBAAoB,gBAAgB,qCAAqC;;AAEnL,eAAO;;AAET,aAAO,2BAA2B,QAAQ;;AAG5C,aAAS,+BAA+B;AACtC,eAAS,SAAS,OAAO,UAAU,eAAe,UAAU,cAAc;AACxE,YAAI,YAAY,MAAM,QAAQ;AAC9B,YAAI,CAAC,QAAQ,mBAAmB,SAAS,GAAG;AAC1C,cAAI,WAAW,YAAY,SAAS;AACpC,iBAAO,IAAI,cAAc,aAAa,WAAW,OAAO,eAAe,gBAAgB,MAAM,WAAW,oBAAoB,gBAAgB,0CAA0C;;AAExL,eAAO;;AAET,aAAO,2BAA2B,QAAQ;;AAG5C,aAAS,0BAA0B,eAAe;AAChD,eAAS,SAAS,OAAO,UAAU,eAAe,UAAU,cAAc;AACxE,YAAI,EAAE,MAAM,QAAQ,aAAa,gBAAgB;AAC/C,cAAI,oBAAoB,cAAc,QAAQ;AAC9C,cAAI,kBAAkB,aAAa,MAAM,QAAQ,CAAC;AAClD,iBAAO,IAAI,cAAc,aAAa,WAAW,OAAO,eAAe,gBAAgB,MAAM,kBAAkB,oBAAoB,gBAAgB,mBAAmB,kBAAkB,oBAAoB,KAAK;;AAEnN,eAAO;;AAET,aAAO,2BAA2B,QAAQ;;AAG5C,aAAS,sBAAsB,gBAAgB;AAC7C,UAAI,CAAC,MAAM,QAAQ,cAAc,GAAG;AAClC,YAAI,MAAuC;AACzC,cAAI,UAAU,SAAS,GAAG;AACxB;cACE,iEAAiE,UAAU,SAAS;YAEhG;UACA,OAAe;AACL,yBAAa,wDAAwD;;;AAGzE,eAAO;;AAGT,eAAS,SAAS,OAAO,UAAU,eAAe,UAAU,cAAc;AACxE,YAAI,YAAY,MAAM,QAAQ;AAC9B,iBAAS,IAAI,GAAG,IAAI,eAAe,QAAQ,KAAK;AAC9C,cAAI,GAAG,WAAW,eAAe,CAAC,CAAC,GAAG;AACpC,mBAAO;;;AAIX,YAAI,eAAe,KAAK,UAAU,gBAAgB,SAAS,SAAS,KAAK,OAAO;AAC9E,cAAI,OAAO,eAAe,KAAK;AAC/B,cAAI,SAAS,UAAU;AACrB,mBAAO,OAAO,KAAK;;AAErB,iBAAO;QACf,CAAO;AACD,eAAO,IAAI,cAAc,aAAa,WAAW,OAAO,eAAe,iBAAiB,OAAO,SAAS,IAAI,QAAQ,kBAAkB,gBAAgB,wBAAwB,eAAe,IAAI;;AAEnM,aAAO,2BAA2B,QAAQ;;AAG5C,aAAS,0BAA0B,aAAa;AAC9C,eAAS,SAAS,OAAO,UAAU,eAAe,UAAU,cAAc;AACxE,YAAI,OAAO,gBAAgB,YAAY;AACrC,iBAAO,IAAI,cAAc,eAAe,eAAe,qBAAqB,gBAAgB,kDAAkD;;AAEhJ,YAAI,YAAY,MAAM,QAAQ;AAC9B,YAAI,WAAW,YAAY,SAAS;AACpC,YAAI,aAAa,UAAU;AACzB,iBAAO,IAAI,cAAc,aAAa,WAAW,OAAO,eAAe,gBAAgB,MAAM,WAAW,oBAAoB,gBAAgB,yBAAyB;;AAEvK,iBAAS,OAAO,WAAW;AACzB,cAAIH,KAAI,WAAW,GAAG,GAAG;AACvB,gBAAI,QAAQ,YAAY,WAAW,KAAK,eAAe,UAAU,eAAe,MAAM,KAAK,oBAAoB;AAC/G,gBAAI,iBAAiB,OAAO;AAC1B,qBAAO;;;;AAIb,eAAO;;AAET,aAAO,2BAA2B,QAAQ;;AAG5C,aAAS,uBAAuB,qBAAqB;AACnD,UAAI,CAAC,MAAM,QAAQ,mBAAmB,GAAG;AACvC,eAAwC,aAAa,wEAAwE,IAAI;AACjI,eAAO;;AAGT,eAAS,IAAI,GAAG,IAAI,oBAAoB,QAAQ,KAAK;AACnD,YAAI,UAAU,oBAAoB,CAAC;AACnC,YAAI,OAAO,YAAY,YAAY;AACjC;YACE,gGACc,yBAAyB,OAAO,IAAI,eAAe,IAAI;UAC/E;AACQ,iBAAO;;;AAIX,eAAS,SAAS,OAAO,UAAU,eAAe,UAAU,cAAc;AACxE,YAAI,gBAAgB,CAAA;AACpB,iBAASI,KAAI,GAAGA,KAAI,oBAAoB,QAAQA,MAAK;AACnD,cAAIC,WAAU,oBAAoBD,EAAC;AACnC,cAAI,gBAAgBC,SAAQ,OAAO,UAAU,eAAe,UAAU,cAAc,oBAAoB;AACxG,cAAI,iBAAiB,MAAM;AACzB,mBAAO;;AAET,cAAI,cAAc,QAAQL,KAAI,cAAc,MAAM,cAAc,GAAG;AACjE,0BAAc,KAAK,cAAc,KAAK,YAAY;;;AAGtD,YAAI,uBAAwB,cAAc,SAAS,IAAK,6BAA6B,cAAc,KAAK,IAAI,IAAI,MAAK;AACrH,eAAO,IAAI,cAAc,aAAa,WAAW,OAAO,eAAe,oBAAoB,MAAM,gBAAgB,MAAM,uBAAuB,IAAI;;AAEpJ,aAAO,2BAA2B,QAAQ;;AAG5C,aAAS,oBAAoB;AAC3B,eAAS,SAAS,OAAO,UAAU,eAAe,UAAU,cAAc;AACxE,YAAI,CAAC,OAAO,MAAM,QAAQ,CAAC,GAAG;AAC5B,iBAAO,IAAI,cAAc,aAAa,WAAW,OAAO,eAAe,oBAAoB,MAAM,gBAAgB,2BAA2B;;AAE9I,eAAO;;AAET,aAAO,2BAA2B,QAAQ;;AAG5C,aAAS,sBAAsB,eAAe,UAAU,cAAc,KAAK,MAAM;AAC/E,aAAO,IAAI;SACR,iBAAiB,iBAAiB,OAAO,WAAW,YAAY,eAAe,MAAM,MAAM,+FACX,OAAO;MAC9F;;AAGE,aAAS,uBAAuB,YAAY;AAC1C,eAAS,SAAS,OAAO,UAAU,eAAe,UAAU,cAAc;AACxE,YAAI,YAAY,MAAM,QAAQ;AAC9B,YAAI,WAAW,YAAY,SAAS;AACpC,YAAI,aAAa,UAAU;AACzB,iBAAO,IAAI,cAAc,aAAa,WAAW,OAAO,eAAe,gBAAgB,WAAW,QAAQ,kBAAkB,gBAAgB,wBAAwB;;AAEtK,iBAAS,OAAO,YAAY;AAC1B,cAAI,UAAU,WAAW,GAAG;AAC5B,cAAI,OAAO,YAAY,YAAY;AACjC,mBAAO,sBAAsB,eAAe,UAAU,cAAc,KAAK,eAAe,OAAO,CAAC;;AAElG,cAAI,QAAQ,QAAQ,WAAW,KAAK,eAAe,UAAU,eAAe,MAAM,KAAK,oBAAoB;AAC3G,cAAI,OAAO;AACT,mBAAO;;;AAGX,eAAO;;AAET,aAAO,2BAA2B,QAAQ;;AAG5C,aAAS,6BAA6B,YAAY;AAChD,eAAS,SAAS,OAAO,UAAU,eAAe,UAAU,cAAc;AACxE,YAAI,YAAY,MAAM,QAAQ;AAC9B,YAAI,WAAW,YAAY,SAAS;AACpC,YAAI,aAAa,UAAU;AACzB,iBAAO,IAAI,cAAc,aAAa,WAAW,OAAO,eAAe,gBAAgB,WAAW,QAAQ,kBAAkB,gBAAgB,wBAAwB;;AAGtK,YAAI,UAAU,OAAO,CAAA,GAAI,MAAM,QAAQ,GAAG,UAAU;AACpD,iBAAS,OAAO,SAAS;AACvB,cAAI,UAAU,WAAW,GAAG;AAC5B,cAAIA,KAAI,YAAY,GAAG,KAAK,OAAO,YAAY,YAAY;AACzD,mBAAO,sBAAsB,eAAe,UAAU,cAAc,KAAK,eAAe,OAAO,CAAC;;AAElG,cAAI,CAAC,SAAS;AACZ,mBAAO,IAAI;cACT,aAAa,WAAW,OAAO,eAAe,YAAY,MAAM,oBAAoB,gBAAgB,qBACjF,KAAK,UAAU,MAAM,QAAQ,GAAG,MAAM,IAAI,IAC7D,mBAAmB,KAAK,UAAU,OAAO,KAAK,UAAU,GAAG,MAAM,IAAI;YACjF;;AAEQ,cAAI,QAAQ,QAAQ,WAAW,KAAK,eAAe,UAAU,eAAe,MAAM,KAAK,oBAAoB;AAC3G,cAAI,OAAO;AACT,mBAAO;;;AAGX,eAAO;;AAGT,aAAO,2BAA2B,QAAQ;;AAG5C,aAAS,OAAO,WAAW;AACzB,cAAQ,OAAO,WAAS;QACtB,KAAK;QACL,KAAK;QACL,KAAK;AACH,iBAAO;QACT,KAAK;AACH,iBAAO,CAAC;QACV,KAAK;AACH,cAAI,MAAM,QAAQ,SAAS,GAAG;AAC5B,mBAAO,UAAU,MAAM,MAAM;;AAE/B,cAAI,cAAc,QAAQ,eAAe,SAAS,GAAG;AACnD,mBAAO;;AAGT,cAAI,aAAa,cAAc,SAAS;AACxC,cAAI,YAAY;AACd,gBAAI,WAAW,WAAW,KAAK,SAAS;AACxC,gBAAI;AACJ,gBAAI,eAAe,UAAU,SAAS;AACpC,qBAAO,EAAE,OAAO,SAAS,KAAI,GAAI,MAAM;AACrC,oBAAI,CAAC,OAAO,KAAK,KAAK,GAAG;AACvB,yBAAO;;;YAGvB,OAAiB;AAEL,qBAAO,EAAE,OAAO,SAAS,KAAI,GAAI,MAAM;AACrC,oBAAI,QAAQ,KAAK;AACjB,oBAAI,OAAO;AACT,sBAAI,CAAC,OAAO,MAAM,CAAC,CAAC,GAAG;AACrB,2BAAO;;;;;UAKzB,OAAe;AACL,mBAAO;;AAGT,iBAAO;QACT;AACE,iBAAO;;;AAIb,aAAS,SAAS,UAAU,WAAW;AAErC,UAAI,aAAa,UAAU;AACzB,eAAO;;AAIT,UAAI,CAAC,WAAW;AACd,eAAO;;AAIT,UAAI,UAAU,eAAe,MAAM,UAAU;AAC3C,eAAO;;AAIT,UAAI,OAAO,WAAW,cAAc,qBAAqB,QAAQ;AAC/D,eAAO;;AAGT,aAAO;;AAIT,aAAS,YAAY,WAAW;AAC9B,UAAI,WAAW,OAAO;AACtB,UAAI,MAAM,QAAQ,SAAS,GAAG;AAC5B,eAAO;;AAET,UAAI,qBAAqB,QAAQ;AAI/B,eAAO;;AAET,UAAI,SAAS,UAAU,SAAS,GAAG;AACjC,eAAO;;AAET,aAAO;;AAKT,aAAS,eAAe,WAAW;AACjC,UAAI,OAAO,cAAc,eAAe,cAAc,MAAM;AAC1D,eAAO,KAAK;;AAEd,UAAI,WAAW,YAAY,SAAS;AACpC,UAAI,aAAa,UAAU;AACzB,YAAI,qBAAqB,MAAM;AAC7B,iBAAO;QACf,WAAiB,qBAAqB,QAAQ;AACtC,iBAAO;;;AAGX,aAAO;;AAKT,aAAS,yBAAyB,OAAO;AACvC,UAAI,OAAO,eAAe,KAAK;AAC/B,cAAQ,MAAI;QACV,KAAK;QACL,KAAK;AACH,iBAAO,QAAQ;QACjB,KAAK;QACL,KAAK;QACL,KAAK;AACH,iBAAO,OAAO;QAChB;AACE,iBAAO;;;AAKb,aAAS,aAAa,WAAW;AAC/B,UAAI,CAAC,UAAU,eAAe,CAAC,UAAU,YAAY,MAAM;AACzD,eAAO;;AAET,aAAO,UAAU,YAAY;;AAG/B,mBAAe,iBAAiB;AAChC,mBAAe,oBAAoB,eAAe;AAClD,mBAAe,YAAY;AAE3B,WAAO;;;;AEzlBT,IAAI,MAAuC;AACrC,YAAUM,eAAA;AAIV,wBAAsB;AAC1BC,YAAA,UAAiBC,+BAAA,EAAqC,QAAQ,WAAW,mBAAmB;AAC9F,OAAO;AAGLD,YAAc,UAAGE,gCAAqC,EAAA;AACxD;AAVM;AAIA;;;;;ACLN,GAAC,WAAY;AAGZ,QAAI,SAAS,CAAA,EAAG;AAEhB,aAASC,cAAc;AACtB,UAAI,UAAU;AAEd,eAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AAC1C,YAAI,MAAM,UAAU,CAAC;AACrB,YAAI,KAAK;AACR,oBAAU,YAAY,SAAS,WAAW,GAAG,CAAC;;;AAIhD,aAAO;;AAGR,aAAS,WAAY,KAAK;AACzB,UAAI,OAAO,QAAQ,YAAY,OAAO,QAAQ,UAAU;AACvD,eAAO;;AAGR,UAAI,OAAO,QAAQ,UAAU;AAC5B,eAAO;;AAGR,UAAI,MAAM,QAAQ,GAAG,GAAG;AACvB,eAAOA,YAAW,MAAM,MAAM,GAAG;;AAGlC,UAAI,IAAI,aAAa,OAAO,UAAU,YAAY,CAAC,IAAI,SAAS,SAAQ,EAAG,SAAS,eAAe,GAAG;AACrG,eAAO,IAAI,SAAQ;;AAGpB,UAAI,UAAU;AAEd,eAAS,OAAO,KAAK;AACpB,YAAI,OAAO,KAAK,KAAK,GAAG,KAAK,IAAI,GAAG,GAAG;AACtC,oBAAU,YAAY,SAAS,GAAG;;;AAIpC,aAAO;;AAGR,aAAS,YAAa,OAAO,UAAU;AACtC,UAAI,CAAC,UAAU;AACd,eAAO;;AAGR,UAAI,OAAO;AACV,eAAO,QAAQ,MAAM;;AAGtB,aAAO,QAAQ;;AAGhB,QAAqC,OAAO,SAAS;AACpD,MAAAA,YAAW,UAAUA;AACrB,aAAA,UAAiBA;IACnB,OAKQ;AACN,aAAO,aAAaA;;EAEtB,GAAC;;;;ACPD,IAAM,cAAc,SAAC,KAAW;AAC9B,SAAO,IACJ,QAAQ,oBAAoB,SAAC,IAAE;AAC9B,WAAO,GAAG,YAAW;EACvB,CAAC,EACA,QAAQ,OAAO,EAAE;AACtB;IAEa,YAAQ,yBACnB,SACE,IACA,KAAG;;AADD,MAAA,YAAS,GAAA,WAAE,UAAO,GAAA,SAAE,kBAAe,GAAA,iBAAE,SAAM,GAAA,QAAE,OAAI,GAAA,MAAE,OAAI,GAAA,MAAE,OAAI,GAAA,MAAE,QAAK,GAAA,OAAE,MAAG,GAAA,KAAE,QAAK,GAAA,OAAK,OAAI,OAAA,IAA3F,CAAA,aAAA,WAAA,mBAAA,UAAA,QAAA,QAAA,QAAA,SAAA,OAAA,OAAA,CAA6F;AAGvF,MAAA,SAAsB,uBAAS,CAAC,GAA/B,SAAM,GAAA,CAAA,GAAE,YAAS,GAAA,CAAA;AACxB,MAAM,QAAQ,QAAQ,WAAW;AAEjC,MAAI,SAAS;AACX,eACE,QAAQ,OACR,QACA,QAAQ,KACN,6GAA6G;;AAGnH,MAAI,MAAM;AACR,eACE,QAAQ,OACR,QACA,QAAQ,KACN,0GAA0G;;AAIhH,4BAAQ,WAAA;AAAM,WAAA,UAAU,SAAS,CAAC;EAApB,GAAuB,CAAC,OAAO,KAAK,UAAU,KAAK,CAAC,CAAC;AAEnE,MAAM,YAAY,QAAQ,UAAU,OAAA,OAAe,UAAA,IAAG;AAEtD,MAAM,WAAO,sBAAQ,WAAA;AACnB,QAAM,WACJ,SAAS,OAAO,UAAU,YAAY,MAAM,SAAS,GAAG,IAAI,YAAY,KAAK,IAAI;AAEnF,QAAI,MAAM,QAAQ,KAAK,GAAG;AACxB,aAAO;;AAGT,QAAI,OAAO,UAAU,YAAa,aAAAC,QAAiC,OAAO,GAAG;AAC3E,aAAQ,aAAAA,QAAiC,QAAkB;;EAE/D,GAAG,CAAC,MAAM,CAAC;AAEX,MAAM,eAAW,sBAAQ,WAAA;AACvB,WAAO,MAAM,QAAQ,IAAI,IAAI,KAAK,CAAC,KAAK,KAAK,CAAC,IAAI;EACpD,GAAG,CAAC,MAAM,CAAC;AAEX,MAAM,QAAS,WAAA;AACb,WAAO,MAAM,QAAQ,IAAI,KAAK,KAAK,SAAS,IAAI,KAAK,CAAC,IAAI;IAC3D;AAED,MAAM,UAAW,WAAA;AACf,WAAO,KAAK,SAAS,KAAK,OAAO,OAAA,KAAK;IACvC;AAED,MAAM,aAAa,kBACf,WAAW,eAAe,IAC1B,WACE,SAAM,KAAA,CAAA,GAEJ,GAAC,QAAQ,OAAA,IAAI,CAAE,IAAG,MAClB,GAAC,kBAAkB,IAAG,UAAU,OAElC,KAAA,SAAS;AAGf,SACE,aAAAA,QAAA;IAAA,aAAAA,QAAA;IAAA;IACG,MACC,aAAAA,QACE;MAAA;MAAA,SAAA,EAAA,OAAM,8BACN,WAAW,WAAU,GAChB,UAAU,EAAE,OAAc,GAC1B,SAAS,EAAE,MAAY,GAAG,EAC/B,MAAK,OAAK,eACE,OAAM,GACd,MACJ,EAAA,IAAQ,CAAA;MAER,aAAAA,QAAA,cAAA,OAAA,EAAK,MAAM,IAAG,CAAA;IAAQ,IAGxB,aAAAA,QACE,cAAA,OAAA,SAAA,EAAA,OAAM,8BACN,SACA,WAAW,WAAU,GAChB,UAAU,EAAE,OAAc,GAC1B,SAAS,EAAE,MAAY,GAC5B,EAAA,MAAK,OAAK,eACE,QACZ,yBAAyB,EAAE,QAAQ,YAAY,SAAQ,EAAE,GACrD,MACJ,EAAA,IAAQ,CAAA,CAAA;IAGX,SAAS,aAAAA,QAAM,cAAA,QAAA,EAAA,WAAU,kBAAiB,GAAE,KAAK;EAAQ;AAGhE,CAAC;AAGH,MAAM,YAAY;EAChB,WAAW,UAAU;EACrB,SAAS,UAAU,UAAU,CAAC,UAAU,OAAO,UAAU,MAAM,CAAC;EAChE,iBAAiB,UAAU;EAC3B,QAAQ,UAAU;EAClB,MAAM,UAAU,UAAU,CAAC,UAAU,OAAO,UAAU,MAAM,CAAC;EAC7D,MAAM,UAAU;EAChB,MAAM,UAAU,MAAM;IACpB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;GACD;EACD,OAAO,UAAU;EACjB,KAAK,UAAU;EACf,SAAS,UAAU;EACnB,OAAO,UAAU;;AAGnB,MAAM,cAAc;IChKP,eAAW,yBACtB,SAAC,IAA+E,KAAG;;AAAhF,MAAA,WAAQ,GAAA,UAAE,YAAS,GAAA,WAAE,kBAAe,GAAA,iBAAE,SAAM,GAAA,QAAE,OAAI,GAAA,MAAE,QAAK,GAAA,OAAE,QAAK,GAAA,OAAK,OAAvE,OAAA,IAAA,CAAA,YAAA,aAAA,mBAAA,UAAA,QAAA,SAAA,OAAA,CAA6E;AAC5E,MAAM,aAAa,kBACf,WAAW,eAAe,IAC1B,WACE,SAAM,KAAA,CAAA,GAEJ,GAAC,QAAQ,OAAA,IAAI,CAAE,IAAG,MAClB,GAAC,kBAAkB,IAAG,UAAU,OAElC,KAAA,SAAS;AAGf,SACE,aAAAA,QAAA;IAAA,aAAAA,QAAA;IAAA;IACG,sBAAS,IAAI,UAAU,SAAC,OAAK;AAC5B,UAAI,aAAAA,QAAM,eAAe,KAAK,GAAG;AAC/B,eAAO,aAAAA,QAAM,aAAa,OAAgC,SAAA,EACxD,eAAe,MACf,WAAW,YACX,WAAW,SACX,KACA,MAAM,MAAK,GACR,IAAI,CAAA;;AAIX;IACF,CAAC;IACA,SAAS,aAAAA,QAAM,cAAA,QAAA,EAAA,WAAU,kBAAiB,GAAE,KAAK;EAAQ;AAGhE,CAAC;AAGH,SAAS,YAAY;EACnB,WAAW,UAAU;EACrB,iBAAiB,UAAU;EAC3B,QAAQ,UAAU;EAClB,MAAM,UAAU,MAAM;IACpB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;GACD;EACD,OAAO,UAAU;EACjB,OAAO,UAAU;;AAGnB,SAAS,cAAc;", "names": ["__assign", "reactIsModule", "require$$0", "require$$1", "has", "require$$2", "require$$3", "require$$4", "i", "checker", "require$$0", "propTypesModule", "require$$1", "require$$2", "classNames", "React"]}