import React from 'react'

const Dashboard = React.lazy(() => import('./views/dashboard/Dashboard'))
const Contacts = React.lazy(() => import('./views/contacts/Contacts'))
const Settings = React.lazy(() => import('./views/settings/Settings'))
const Test = React.lazy(() => import('./views/test/Test'))
const ActionItemCard = React.lazy(() => import('./views/ActionItemCard'))
const Approvals = React.lazy(() => import('./views/approvals/Approvals'))
const ApprovalItemCard = React.lazy(() => import('./views/ApprovalItemCard'))
const CommunicationHistory = React.lazy(() => import('./views/pages/CommunicationHistory'))
const TransactionHistory = React.lazy(() => import('./components/TransactionHistory'))
const NotificationsPage = React.lazy(() => import('./views/notifications/NotificationsPage'))

const Colors = React.lazy(() => import('./views/theme/colors/Colors'))
const Typography = React.lazy(() => import('./views/theme/typography/Typography'))

const routes = [
  { path: '/', exact: true, name: 'Home' },
  { path: '/dashboard', name: 'Dashboard', element: Dashboard },
  { path: '/contacts', name: 'Contacts', element: Contacts },
  { path: '/settings', name: 'Dashboard', element: Settings },
  { path: '/test', name: 'Test', element: Test },
  { path: '/ActionItemCard', name: 'Test', element: ActionItemCard },
  { path: '/approvals', name: 'Approvals', element: Approvals },
  { path: '/ApprovalItemCard', name: 'Test', element: ApprovalItemCard },
  { path: '/communications/:defaulterId', name: 'Communication History', element: CommunicationHistory },
  { path: '/transactions', name: 'Transaction History', element: TransactionHistory },
  { path: '/notifications', name: 'Notifications', element: NotificationsPage },
]

export default routes
