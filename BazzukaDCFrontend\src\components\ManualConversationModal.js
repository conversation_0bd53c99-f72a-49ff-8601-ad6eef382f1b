import React, { useState, useEffect } from 'react'
import {
  CModal,
  CModalHeader,
  CModalTitle,
  CModalBody,
  CModalFooter,
  CButton,
  CForm,
  CFormLabel,
  CFormInput,
  CFormTextarea,
  CFormCheck,
  CAlert,
  CSpinner,
} from '@coreui/react'
import { getApiUrl } from '../utils/apiConfig'

const ManualConversationModal = ({ visible, onClose, onSuccess }) => {
  const [formData, setFormData] = useState({
    defaulter_id: '',
    summary: '',
    direction: 'inbound',
  })
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState('')

  // Reset form when modal opens/closes
  useEffect(() => {
    if (visible) {
      setFormData({
        defaulter_id: '',
        summary: '',
        direction: 'inbound',
      })
      setError('')
      setSuccess('')
    }
  }, [visible])

  const handleInputChange = (e) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    setIsLoading(true)
    setError('')
    setSuccess('')

    try {
      // Validate form
      if (!formData.defaulter_id || !formData.summary) {
        setError('Please fill in all required fields')
        setIsLoading(false)
        return
      }

      const response = await fetch(getApiUrl('/v0/communications/manual'), {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'ngrok-skip-browser-warning': 'true',
          'Access-Control-Allow-Origin': '*',
        },
        body: JSON.stringify(formData),
      })

      const result = await response.json()

      if (response.ok) {
        setSuccess('Human conversation logged successfully!')
        setTimeout(() => {
          onSuccess && onSuccess()
          onClose()
        }, 1500)
      } else {
        setError(result.error || 'Failed to log conversation')
      }
    } catch (err) {
      console.error('Error logging conversation:', err)
      setError('Failed to log conversation. Please try again.')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <CModal visible={visible} onClose={onClose} size="lg">
      <CModalHeader>
        <CModalTitle>Log Human Conversation</CModalTitle>
      </CModalHeader>
      
      <CForm onSubmit={handleSubmit}>
        <CModalBody>
          {error && (
            <CAlert color="danger" className="mb-3">
              {error}
            </CAlert>
          )}
          
          {success && (
            <CAlert color="success" className="mb-3">
              {success}
            </CAlert>
          )}

          <div className="mb-3">
            <CFormLabel htmlFor="defaulter_id">
              Customer ID <span className="text-danger">*</span>
            </CFormLabel>
            <CFormInput
              type="text"
              id="defaulter_id"
              name="defaulter_id"
              value={formData.defaulter_id}
              onChange={handleInputChange}
              placeholder="Enter customer/defaulter ID"
              required
              disabled={isLoading}
            />
            <small className="text-muted">
              Enter the ID of the customer you had the conversation with
            </small>
          </div>

          <div className="mb-3">
            <CFormLabel htmlFor="summary">
              Conversation Summary <span className="text-danger">*</span>
            </CFormLabel>
            <CFormTextarea
              id="summary"
              name="summary"
              value={formData.summary}
              onChange={handleInputChange}
              placeholder="Describe what happened in the conversation, including key points, customer responses, commitments made, etc."
              rows={5}
              required
              disabled={isLoading}
            />
            <small className="text-muted">
              Provide a detailed summary of the conversation context and outcomes
            </small>
          </div>

          <div className="mb-3">
            <CFormLabel>
              Call Direction <span className="text-danger">*</span>
            </CFormLabel>
            <div className="mt-2">
              <CFormCheck
                type="radio"
                name="direction"
                id="inbound"
                value="inbound"
                label="Inbound (Customer called you)"
                checked={formData.direction === 'inbound'}
                onChange={handleInputChange}
                disabled={isLoading}
              />
              <CFormCheck
                type="radio"
                name="direction"
                id="outbound"
                value="outbound"
                label="Outbound (You called the customer)"
                checked={formData.direction === 'outbound'}
                onChange={handleInputChange}
                disabled={isLoading}
              />
            </div>
          </div>
        </CModalBody>

        <CModalFooter>
          <CButton
            color="secondary"
            onClick={onClose}
            disabled={isLoading}
          >
            Cancel
          </CButton>
          <CButton
            color="primary"
            type="submit"
            disabled={isLoading || !formData.defaulter_id || !formData.summary}
          >
            {isLoading ? (
              <>
                <CSpinner size="sm" className="me-2" />
                Logging...
              </>
            ) : (
              'Log Conversation'
            )}
          </CButton>
        </CModalFooter>
      </CForm>
    </CModal>
  )
}

export default ManualConversationModal
