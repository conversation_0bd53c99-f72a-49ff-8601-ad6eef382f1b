JSON_TYPES = ["string", "number", "object", "array", "boolean"]


class ToolParam:
    def __init__(
        self,
        name,
        json_type,
        description,
        required=False,
        children=None,
        items_type=None,
    ):
        self.name = name
        self.description = description
        self.required = required

        if json_type.lower() in JSON_TYPES:
            self._type = json_type
        else:
            raise ValueError("Invalid param type for tool.")

        self.children = None
        self.items_type = None
        if json_type == "object":
            self.children = children
        if json_type == "array":
            if items_type and items_type.lower() in JSON_TYPES:
                self.items_type = items_type
            else:
                raise ValueError("Invalid items type for array.")

    def has_children(self):
        return self.children is not None

    def has_items_type(self):
        return self.items_type is not None


class ToolParams:
    def __init__(self, params):
        self.params = params

    def __iter__(self):
        for item in self.params:
            yield item


class Tool:
    def __init__(self, name, description, func, params: ToolParams):
        self.name = name
        self.description = description
        self.func = func
        self.params = params
        self.schema = self.build_schema()

    # NOTE: only async functions are supported
    def execute(self, args=None):
        if args:
            return self.func(self.interface, **args)
        else:
            return self.func(self.interface)

    def build_schema(self, format="openai"):
        # TODO: properly handle no params
        def build_schema(param):
            schema = {"type": param._type, "description": param.description}
            if param.has_children() and param._type == "object":
                schema["properties"] = {
                    child.name: build_schema(child) for child in param.children
                }
            if param.has_items_type() and param._type == "array":
                schema["items"] = {"type": param.items_type}
            return schema

        schema = {
            "type": "object",
            "properties": {param.name: build_schema(param) for param in self.params},
            "required": [
                param.name for param in self.params if param.required
            ],  # Note this does not handle nested required fields
            "additionalProperties": False,
        }
        return schema

    def registerTo(self, engine):
        self.interface = engine.interface


class ToolEngine:
    def __init__(self, interface):
        # TODO: make more flexible; allow other objects than IssueRepository
        self.interface = interface()
        self.tools = {}

    def register(self, tool):
        tool.registerTo(self)
        self.tools[tool.name] = tool

    def execute(self, func, args):
        print(f"[ToolEngine] Executing tool: {func} with args: {args}")
        try:
            if self.tools[func].params is None:
                result = self.tools[func].execute()
            else:
                result = self.tools[func].execute(args)
            print(f"[ToolEngine] Tool {func} executed successfully, result: {result}")
            return result
        except Exception as e:
            print(f"[ToolEngine] Error executing tool {func}: {e}")
            raise

    def get_tools(self, format="openai"):
        output = []
        for tool in self.tools.values():
            output.append(
                {
                    "type": "function",
                    "function": {
                        "name": tool.name,
                        "description": tool.description,
                        "parameters": tool.schema,
                    },
                }
            )
        return output
