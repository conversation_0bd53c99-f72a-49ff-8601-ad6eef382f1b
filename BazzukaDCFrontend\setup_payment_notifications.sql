-- Minimal setup script for payment_links notifications in dev schema
-- This script <PERSON><PERSON><PERSON> sets up the Postgres Changes publication without any custom triggers or functions

-- First, make sure any problematic triggers are dropped
-- Only drop the custom trigger, not the system constraint triggers
DROP TRIGGER IF EXISTS handle_payment_links_changes ON dev.payment_links;
DROP FUNCTION IF EXISTS dev.broadcast_payment_links_changes() CASCADE;

-- Create the supabase_realtime publication if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_publication WHERE pubname = 'supabase_realtime') THEN
        CREATE PUBLICATION supabase_realtime;
        RAISE NOTICE 'Created publication supabase_realtime';
    ELSE
        -- If it exists, we'll recreate it to be safe
        DROP PUBLICATION IF EXISTS supabase_realtime;
        CREATE PUBLICATION supabase_realtime;
        RAISE NOTICE 'Recreated publication supabase_realtime';
    END IF;
END
$$;

-- Add the payment_links table to the publication
-- This is the ONLY thing needed for Postgres Changes to work
ALTER PUBLICATION supabase_realtime ADD TABLE dev.payment_links;

-- Enable RLS on the payment_links table
ALTER TABLE dev.payment_links ENABLE ROW LEVEL SECURITY;

-- Create a policy to allow authenticated users to select from the payment_links table
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies 
        WHERE schemaname = 'dev' 
        AND tablename = 'payment_links' 
        AND policyname = 'Allow authenticated users to view payment_links'
    ) THEN
        CREATE POLICY "Allow authenticated users to view payment_links"
        ON dev.payment_links
        FOR SELECT
        TO authenticated
        USING (true);
        RAISE NOTICE 'Created policy for payment_links';
    ELSE
        RAISE NOTICE 'Policy for payment_links already exists';
    END IF;
END
$$;

-- Verify the setup
SELECT pubname, schemaname, tablename 
FROM pg_publication_tables 
WHERE pubname = 'supabase_realtime';

-- List RLS policies on the payment_links table
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual
FROM pg_policies
WHERE schemaname = 'dev' AND tablename = 'payment_links';

-- List any remaining triggers on the payment_links table
SELECT 
    tgname AS trigger_name,
    tgrelid::regclass AS table_name
FROM 
    pg_trigger
WHERE 
    tgrelid = 'dev.payment_links'::regclass
    AND tgname NOT LIKE 'RI_ConstraintTrigger%'; -- Exclude system constraint triggers

-- Test the setup with an insert (uncomment to test)
-- INSERT INTO dev.payment_links (amount, issue_id, _approved)
-- VALUES (12500, 123, 'pending')
-- RETURNING id, amount, issue_id, _approved;
