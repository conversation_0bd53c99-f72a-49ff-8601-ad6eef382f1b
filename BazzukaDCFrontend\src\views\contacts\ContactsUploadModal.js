import React, { useState } from 'react'
import PropTypes from 'prop-types'
import {
  CModal,
  CModalHeader,
  CModalTitle,
  CModalBody,
  CModalFooter,
  CButton,
  CFormInput,
} from '@coreui/react'
import Papa from 'papaparse'

const ImportModal = ({ show, onClose, onUpload }) => {
  const [file, setFile] = useState(null)

  const browseFiles = () => {
    const fileInput = document.querySelector('input[type="file"]')
    fileInput.click()
  }

  const handleDragOver = (e) => {
    e.preventDefault()
    document.getElementById('dropzone').style.backgroundColor = '#f0f0f0'
    document.getElementById('dropzone').style.border = '2px dashed #999'
    document.getElementById('dropzoneMessage').style.color = '#999'
    document.getElementById('dropzoneMessage').innerText = 'Drop file here.'
  }

  const handleDragLeave = (e) => {
    e.preventDefault()
    document.getElementById('dropzone').style.backgroundColor = ''
    document.getElementById('dropzone').style.border = '2px dashed #ccc'
    document.getElementById('dropzoneMessage').style.color = ''
    document.getElementById('dropzoneMessage').innerText =
      'Drag and drop a CSV file here, or click to select a file.'
  }

  const handleDrop = (e) => {
    e.preventDefault()
    document.getElementById('dropzone').style.backgroundColor = ''
    document.getElementById('dropzone').style.border = '2px dashed #ccc'
    document.getElementById('dropzoneMessage').style.color = ''
    document.getElementById('dropzoneMessage').innerText =
      'Drag and drop a CSV file here, or click to select a file.'
    const droppedFile = e.dataTransfer.files[0]
    if (droppedFile && droppedFile.type === 'text/csv') {
      setFile(droppedFile)
    }
  }

  const handleFileChange = (e) => {
    const selectedFile = e.target.files[0]
    if (selectedFile && selectedFile.type === 'text/csv') {
      setFile(selectedFile)
    }
  }

  const handleUpload = () => {
    const company = 'IEC'
    if (file) {
      Papa.parse(file, {
        header: true,
        complete: (results) => {
          // Updated mapping for carl_sample_assumptions.csv
          var mappedContacts = results.data.map((row) => {
            // Parse charged off date
            let delinquencyDate = row.datechargedoff || '';
            let delinquency = '0';
            if (delinquencyDate) {
              const chargedOff = new Date(delinquencyDate);
              const now = new Date();
              // Calculate difference in days
              const diffTime = now - chargedOff;
              delinquency = Math.floor(diffTime / (1000 * 60 * 60 * 24)).toString();
            }
            return {
              studnum: row.accountnumber || '',
              name: (row.firstname || '') + (row.middlename ? ' ' + row.middlename : '') + (row.lastname ? ' ' + row.lastname : ''),
              email: row.emailaddress || '',
              phone: row.cellphone || row.primaryphone || row.workphone || '',
              ssn: row.socialsecuritynumber ? row.socialsecuritynumber.slice(-4) : '',
              dob: row.birthdate || '',
              delinquency: delinquency, // Now calculated from charged off date
              delinquency_date: delinquencyDate, // Pass charged off date
              outstanding_amount: row.currentbalance || row.principal || '',
              last_paid_date: row.lastpaymentdate || '',
              last_paid_amt: row.LastPayAmount || '',
            }
          })

          // Filter out rows with insufficient data
          var contacts = mappedContacts.filter(contact => {
            const hasStudnum = contact.studnum && contact.studnum.trim() !== '';
            const hasName = contact.name && contact.name.trim() !== '';
            const hasEmail = contact.email && contact.email.trim() !== '';
            const hasPhone = contact.phone && contact.phone.trim() !== '';
            return hasStudnum && (hasName || hasEmail || hasPhone);
          })

          onUpload(contacts)
          setFile(null)
          close()
        },
        error: (error) => {
          console.error('Error parsing CSV file:', error)
        },
      })
    }
  }

  const close = () => {
    setFile(null)
    onClose()
  }

  return (
    <CModal visible={show} onClose={close} backdrop="static">
      <CModalHeader closeButton>
        <CModalTitle>Upload Contacts</CModalTitle>
      </CModalHeader>
      <CModalBody>
        <div
          id="dropzone"
          onDrop={handleDrop}
          onClick={browseFiles}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          style={{
            border: '2px dashed #ccc',
            padding: '20px',
            textAlign: 'center',
            marginBottom: '20px',
          }}
        >
          {file ? (
            <p id="dropzoneMessage">{file.name}</p>
          ) : (
            <p id="dropzoneMessage">Drag and drop a CSV file here, or click to select a file.</p>
          )}
          <CFormInput
            type="file"
            accept=".csv"
            onChange={handleFileChange}
            style={{ display: 'none' }}
          />
        </div>
      </CModalBody>
      <CModalFooter>
        <CButton color="secondary" onClick={close}>
          Cancel
        </CButton>
        <CButton color="primary" onClick={handleUpload}>
          Upload
        </CButton>
      </CModalFooter>
    </CModal>
  )
}

ImportModal.propTypes = {
  show: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  onUpload: PropTypes.func.isRequired,
}

export default ImportModal
