import os
from flask import Blueprint, request, jsonify
from app.core.overdue_handler import handle_overdue_batch

internal_webhook_bp = Blueprint('internal_webhook', __name__)

@internal_webhook_bp.route('/internal', methods=['POST'])
def internal_webhook():
    # Check for internal header
    expected_secret = os.environ.get('INTERNAL_WEBHOOK_SECRET')
    if request.headers.get('X-Internal-Secret') != expected_secret:
        return jsonify({'error': 'Unauthorized'}), 401
    data = request.get_json()
    if not data or 'events' not in data:
        return jsonify({'error': 'Missing events'}), 400
    result = handle_overdue_batch(data['events'])
    return jsonify(result), 200 