from flask import Blueprint, request, jsonify, g
from datetime import datetime
from typing import Optional, Dict, Any
from enum import Enum
from pydantic import BaseModel, validator
import os
from app.utils.supabase.queries import (
    insert_action_item,
    update_action_item,
    delete_action_item,
    get_defaulter_by_id,
    get_action_items_by_org_id,
)
from app.core.defaulters import defaulters

drafts_bp = Blueprint("drafts_bp", __name__)


# Models
class ActionChannel(str, Enum):
    CALL = "call"
    EMAIL = "email"


class DraftBase(BaseModel):
    action_date: str
    action_time: str
    action_channel: ActionChannel
    action_reason: str
    action_channel_content: Optional[str] = None
    payment_likelihood: Optional[float] = None
    category: Optional[str] = None
    defaulter_id: str

    @validator("action_date")
    def validate_date(cls, v):
        try:
            datetime.strptime(v, "%Y-%m-%d")
            return v
        except ValueError:
            raise ValueError("action_date must be in YYYY-MM-DD format")

    @validator("action_time")
    def validate_time(cls, v):
        try:
            datetime.strptime(v, "%H:%M:%S")
            return v
        except ValueError:
            raise ValueError("action_time must be in HH:MM:SS format")


class DraftUpdate(BaseModel):
    action_date: Optional[str] = None
    action_time: Optional[str] = None
    action_channel: Optional[ActionChannel] = None
    action_reason: Optional[str] = None
    action_channel_content: Optional[str] = None

    @validator("action_date")
    def validate_date(cls, v):
        if v is None:
            return v
        try:
            datetime.strptime(v, "%Y-%m-%d")
            return v
        except ValueError:
            raise ValueError("action_date must be in YYYY-MM-DD format")

    @validator("action_time")
    def validate_time(cls, v):
        if v is None:
            return v
        try:
            datetime.strptime(v, "%H:%M:%S")
            return v
        except ValueError:
            raise ValueError("action_time must be in HH:MM:SS format")


# Routes
@drafts_bp.route("/drafts", methods=["GET", "POST", "OPTIONS"])
def handle_drafts():
    """
    GET: Fetch drafts with pagination
    POST: Create or update a draft (action item).
    If id is provided, the existing draft is updated.
    If id is not provided, a new draft is created.
    """
    if request.method == "OPTIONS":
        return jsonify({"methods": ["GET", "POST", "OPTIONS"]}), 200

    if request.method == "GET":
        # Get org_id for filtering
        if os.environ.get("FLASK_DEBUG"):
            org_id = "8241d390-a8b5-4f59-b0a9-b95c074db3f5"
        else:
            try:
                org_id = g.user.user_metadata.get("org_id")
            except Exception as e:
                return jsonify({}), 500

        limit = min(int(request.args.get("limit", 10)), 1000)
        offset = int(request.args.get("offset", 0))
        drafts = get_action_items_by_org_id(org_id, limit=limit, offset=offset).data

        for draft in drafts:
            draft.pop("issues", None)
            defaulter = defaulters.get_defaulter_by_id(draft["defaulter_id"])
            draft["contact"] = {}
            # TODO: move name to contact only
            draft["name"] = defaulter["name"]
            draft["contact"]["name"] = defaulter["name"]
            if draft["action_channel"] == "call":
                draft["contact"]["phone_number"] = defaulter["phone"]
            elif draft["action_channel"] == "email":
                draft["contact"]["email_address"] = defaulter["email"]

        return jsonify({"data": drafts}), 200

    try:
        data = request.json

        # Check if this is an update operation
        if "id" in data:
            # Update existing draft
            draft_id = data.pop("id")

            # Print debug info
            print(f"Updating draft {draft_id} with data: {data}")

            # Prepare update data
            update_data = {}
            if "action_date" in data:
                update_data["action_date"] = data["action_date"]
            if "action_time" in data:
                update_data["action_time"] = data["action_time"]
            if "action_channel" in data:
                update_data["action_channel"] = data["action_channel"]
            if "action_reason" in data:
                update_data["action_reason"] = data["action_reason"]

            # Update the action item
            result = update_action_item(draft_id, update_data)
            if not result:
                return (
                    jsonify({"status": "error", "message": "Failed to update draft"}),
                    500,
                )

            return jsonify({"status": "success"}), 200

        # Create new draft
        required_fields = [
            "action_date",
            "action_time",
            "action_channel",
            "action_reason",
            "defaulter_id",
        ]
        for field in required_fields:
            if field not in data:
                return (
                    jsonify(
                        {
                            "status": "error",
                            "message": f"Missing required field: {field}",
                        }
                    ),
                    400,
                )

        # Extract the data
        action_date = data["action_date"]
        action_time = data["action_time"]
        action_channel = data["action_channel"]
        action_reason = data["action_reason"]
        defaulter_id = data["defaulter_id"]

        # Validate that the defaulter exists
        try:
            defaulter_result = get_defaulter_by_id(defaulter_id)
            if not defaulter_result.data or len(defaulter_result.data) == 0:
                return (
                    jsonify(
                        {
                            "status": "error",
                            "message": f"No defaulter found with ID: {defaulter_id}. Please check the Customer ID and try again.",
                        }
                    ),
                    400,
                )
        except Exception as e:
            print(f"Error validating defaulter ID {defaulter_id}: {str(e)}")
            return (
                jsonify(
                    {
                        "status": "error",
                        "message": f"Error validating defaulter ID: {defaulter_id}. Please check the Customer ID and try again.",
                    }
                ),
                400,
            )

        # Additional validation for date/time
        try:
            # Validate date format
            datetime.strptime(action_date, "%Y-%m-%d")
            # Validate time format
            datetime.strptime(action_time, "%H:%M:%S")
        except ValueError as e:
            return (
                jsonify(
                    {
                        "status": "error",
                        "message": "Invalid date or time format. Please use YYYY-MM-DD for date and HH:MM:SS for time.",
                    }
                ),
                400,
            )

        # Get additional optional fields
        action_channel_content = data.get("action_channel_content", "")
        payment_likelihood = data.get("payment_likelihood", 0)
        category = data.get("category", "outreach")

        # Insert the action item
        result = insert_action_item(
            action_date,
            action_time,
            action_channel,
            action_channel_content,
            defaulter_id,
            action_reason,
            payment_likelihood,
            category,
        )

        if not result or not result.data:
            return (
                jsonify({"status": "error", "message": "Failed to create draft"}),
                500,
            )

        return jsonify({"status": "success", "id": result.data[0]["id"]}), 201

    except Exception as e:
        return jsonify({"status": "error", "message": str(e)}), 500


@drafts_bp.route("/drafts/<action_item_id>", methods=["PUT", "DELETE", "OPTIONS"])
def update_draft(action_item_id):
    """
    Update or delete a specific draft by its ID.
    """
    if request.method == "OPTIONS":
        return jsonify({"methods": ["PUT", "DELETE", "OPTIONS"]}), 200

    elif request.method == "PUT":
        try:
            data = request.json

            # Prepare update data
            update_data = {}
            if "action_date" in data:
                update_data["action_date"] = data["action_date"]
            if "action_time" in data:
                update_data["action_time"] = data["action_time"]
            if "action_channel" in data:
                update_data["action_channel"] = data["action_channel"]
            if "action_reason" in data:
                update_data["action_reason"] = data["action_reason"]

            # Update the action item
            result = update_action_item(action_item_id, update_data)
            if not result:
                return (
                    jsonify({"status": "error", "message": "Failed to update draft"}),
                    500,
                )

            return jsonify({"status": "success"}), 200

        except Exception as e:
            return jsonify({"status": "error", "message": str(e)}), 500

    elif request.method == "DELETE":
        try:
            # Delete the action item
            result = delete_action_item(action_item_id)
            if not result:
                return (
                    jsonify({"status": "error", "message": "Failed to delete draft"}),
                    500,
                )

            return jsonify({"status": "success"}), 200

        except Exception as e:
            return jsonify({"status": "error", "message": str(e)}), 500
