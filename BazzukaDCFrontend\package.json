{"name": "@coreui/coreui-free-react-admin-template", "version": "5.2.0", "description": "CoreUI Free React Admin Template", "homepage": ".", "bugs": {"url": "https://github.com/coreui/coreui-free-react-admin-template/issues"}, "repository": {"type": "git", "url": "**************:coreui/coreui-free-react-admin-template.git"}, "license": "MIT", "author": "The CoreUI Team (https://github.com/orgs/coreui/people)", "scripts": {"build": "vite build", "lint": "eslint \"src/**/*.js\"", "serve": "vite preview", "start": "vite"}, "dependencies": {"@coreui/chartjs": "^4.0.0", "@coreui/coreui": "^5.2.0", "@coreui/icons": "^3.0.1", "@coreui/icons-react": "^2.3.0", "@coreui/react": "^5.4.1", "@coreui/react-chartjs": "^3.0.0", "@coreui/utils": "^2.0.2", "@popperjs/core": "^2.11.8", "@supabase/auth-ui-react": "^0.4.7", "@supabase/auth-ui-shared": "^0.1.8", "@supabase/supabase-js": "^2.46.2", "chart.js": "^4.4.6", "classnames": "^2.5.1", "core-js": "^3.39.0", "papaparse": "^5.4.1", "prop-types": "^15.8.1", "react": "^18.3.1", "react-dom": "^18.3.1", "react-redux": "^9.1.2", "react-router-dom": "^6.28.0", "redux": "5.0.1", "simplebar-react": "^3.2.6"}, "devDependencies": {"@vitejs/plugin-react": "^4.3.3", "autoprefixer": "^10.4.20", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^4.6.2", "postcss": "^8.4.49", "prettier": "3.3.3", "sass": "^1.81.0", "tailwindcss": "^3.4.16", "typescript": "^5.7.2", "vite": "^5.4.11"}}