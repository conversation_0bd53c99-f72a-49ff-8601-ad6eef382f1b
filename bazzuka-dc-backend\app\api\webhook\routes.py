from . import webhook_blueprint

from .vapi import vapi_webhooks_bp
from .email import email_webhooks_bp
from .internal import internal_webhook_bp

webhook_blueprint.register_blueprint(vapi_webhooks_bp)
webhook_blueprint.register_blueprint(email_webhooks_bp)
webhook_blueprint.register_blueprint(internal_webhook_bp)

# --- STRIPE WEBHOOK ENDPOINT ---
from flask import request, jsonify, current_app
import os
import stripe as stripe_sdk
from app.core.stripe import process_webhook

@webhook_blueprint.route('/stripe', methods=['POST'])
def stripe_webhook():
    payload = request.data
    sig_header = request.headers.get('Stripe-Signature')
    endpoint_secret = os.getenv('STRIPE_WEBHOOK_SECRET')
    
    print(f"[STRIPE WEBHOOK] Received webhook request")
    print(f"[STRIPE WEBHOOK] Payload length: {len(payload)}")
    print(f"[STRIPE WEBHOOK] Signature header present: {sig_header is not None}")
    print(f"[STRIPE WEBHOOK] Endpoint secret present: {endpoint_secret is not None}")
    
    event = None
    try:
        event = stripe_sdk.Webhook.construct_event(
            payload, sig_header, endpoint_secret
        )
        print(f"[STRIPE WEBHOOK] Successfully constructed event: {event.get('type', 'unknown')}")
    except ValueError as e:
        # Invalid payload
        print(f"[STRIPE WEBHOOK] Invalid payload error: {e}")
        return jsonify({'error': 'Invalid payload'}), 400
    except stripe_sdk.error.SignatureVerificationError as e:
        # Invalid signature
        print(f"[STRIPE WEBHOOK] Invalid signature error: {e}")
        return jsonify({'error': 'Invalid signature'}), 400
    except Exception as e:
        # Catch any other unexpected errors
        print(f"[STRIPE WEBHOOK] Unexpected error: {e}")
        return jsonify({'error': 'Webhook processing failed'}), 400

    # Process the event
    try:
        process_webhook(event)
        print(f"[STRIPE WEBHOOK] Successfully processed event: {event.get('type', 'unknown')}")
    except Exception as e:
        print(f"[STRIPE WEBHOOK] Error processing webhook: {e}")
        return jsonify({'error': 'Event processing failed'}), 500
        
    return jsonify({'status': 'success'}), 200

@webhook_blueprint.route('/stripe-invoice', methods=['POST'])
def stripe_invoice_webhook():
    return stripe_webhook()
