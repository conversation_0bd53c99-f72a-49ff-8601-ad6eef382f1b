from .client import supabase


def update_user_metadata(user, new_metadata):
    """
    Update the metadata of a user.

    Args:
        user_id (str): The user ID.
        new_metadata (dict): The new metadata to update.

    Returns:
        dict: The supabase response.
    """
    # Merge new metadata with existing raw_user_meta_data
    updated_metadata = {**user.user_metadata, **new_metadata}

    # Update the user
    response = supabase.auth.admin.update_user_by_id(
        user.id, {"user_metadata": updated_metadata}
    )

    return response
