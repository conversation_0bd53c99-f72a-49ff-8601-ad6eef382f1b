{"version": 3, "sources": ["../../@coreui/utils/src/deepObjectsMerge.ts", "../../@coreui/utils/src/getStyle.ts", "../../@coreui/utils/src/getColor.ts", "../../@coreui/utils/src/hexToRgb.ts", "../../@coreui/utils/src/hexToRgba.ts", "../../@coreui/utils/src/makeUid.ts", "../../@coreui/utils/src/omitByKeys.ts", "../../@coreui/utils/src/pickByKeys.ts", "../../@coreui/utils/src/rgbToHex.ts"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Core<PERSON> Utils (__COREUI_VERSION__): deepObjectsMerge.ts\n * Licensed under MIT (https://github.com/coreui/coreui-utils/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst deepObjectsMerge = (target: object, source: object) => {\n  // Iterate through `source` properties and if an `Object` set property to merge of `target` and `source` properties\n  for (const key of Object.keys(source)) {\n    if (source[key] instanceof Object) {\n      Object.assign(source[key], deepObjectsMerge(target[key], source[key]))\n    }\n  }\n\n  // Join `target` and modified `source`\n  Object.assign(target || {}, source)\n  return target\n}\n\nexport default deepObjectsMerge\n", "/**\n * --------------------------------------------------------------------------\n * Core<PERSON> Utils (__COREUI_VERSION__): getStyle.ts\n * Licensed under MIT (https://github.com/coreui/coreui-utils/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst getStyle = (property: string, element?: Element) => {\n  if (typeof window === 'undefined') {\n    return\n  }\n\n  if (typeof document === 'undefined') {\n    return\n  }\n\n  const _element = element ?? document.body\n\n  return window.getComputedStyle(_element, null).getPropertyValue(property).replace(/^\\s/, '')\n}\n\nexport default getStyle\n", "/**\n * --------------------------------------------------------------------------\n * Core<PERSON> Utils (__COREUI_VERSION__): getColor.ts\n * Licensed under MIT (https://github.com/coreui/coreui-utils/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport getStyle from './getStyle'\n\nconst getColor = (rawProperty: string, element?: Element) => {\n  const property = `--${rawProperty}`\n  const style = getStyle(property, element)\n  return style ? style : rawProperty\n}\n\nexport default getColor\n", "/**\n * --------------------------------------------------------------------------\n * Core<PERSON> Utils (__COREUI_VERSION__): hexToRgb.ts\n * Licensed under MIT (https://github.com/coreui/coreui-utils/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\n/* eslint-disable no-magic-numbers */\nconst hexToRgb = (color: string) => {\n  if (typeof color === 'undefined') {\n    throw new TypeError('Hex color is not defined')\n  }\n\n  const hex = color.match(/^#(?:[0-9a-f]{3}){1,2}$/i)\n\n  if (!hex) {\n    // throw new Error(`${color} is not a valid hex color`)\n  }\n\n  let r\n  let g\n  let b\n\n  if (color.length === 7) {\n    r = parseInt(color.slice(1, 3), 16)\n    g = parseInt(color.slice(3, 5), 16)\n    b = parseInt(color.slice(5, 7), 16)\n  } else {\n    r = parseInt(color.slice(1, 2), 16)\n    g = parseInt(color.slice(2, 3), 16)\n    b = parseInt(color.slice(3, 5), 16)\n  }\n\n  return `rgba(${r}, ${g}, ${b})`\n}\n\nexport default hexToRgb\n", "/**\n * --------------------------------------------------------------------------\n * Core<PERSON> Utils (__COREUI_VERSION__): hexToRgba.ts\n * Licensed under MIT (https://github.com/coreui/coreui-utils/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\n/* eslint-disable no-magic-numbers */\nconst hexToRgba = (color: string, opacity = 100) => {\n  if (typeof color === 'undefined') {\n    throw new TypeError('Hex color is not defined')\n  }\n\n  const hex = color.match(/^#(?:[0-9a-f]{3}){1,2}$/i)\n\n  if (!hex) {\n    throw new Error(`${color} is not a valid hex color`)\n  }\n\n  let r\n  let g\n  let b\n\n  if (color.length === 7) {\n    r = parseInt(color.slice(1, 3), 16)\n    g = parseInt(color.slice(3, 5), 16)\n    b = parseInt(color.slice(5, 7), 16)\n  } else {\n    r = parseInt(color.slice(1, 2), 16)\n    g = parseInt(color.slice(2, 3), 16)\n    b = parseInt(color.slice(3, 5), 16)\n  }\n\n  return `rgba(${r}, ${g}, ${b}, ${opacity / 100})`\n}\n\nexport default hexToRgba\n", "/**\n * --------------------------------------------------------------------------\n * Core<PERSON> Utils (__COREUI_VERSION__): makeUid.ts\n * Licensed under MIT (https://github.com/coreui/coreui-utils/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\n//function for UI releted ID assignment, due to one in 10^15 probability of duplication\nconst makeUid = () => {\n  const key = Math.random().toString(36).substr(2)\n  return 'uid-' + key\n}\n\nexport default makeUid", "/**\n * --------------------------------------------------------------------------\n * Core<PERSON> Utils (__COREUI_VERSION__): omitByKeys.ts\n * Licensed under MIT (https://github.com/coreui/coreui-utils/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst omitByKeys = (originalObject: object, keys: string | string[]) => {\n  const newObj = {}\n  const objKeys = Object.keys(originalObject)\n  for (let i = 0; i < objKeys.length; i++) {\n    !keys.includes(objKeys[i]) && (newObj[objKeys[i]] = originalObject[objKeys[i]])\n  }\n  return newObj\n}\n\nexport default omitByKeys\n", "/**\n * --------------------------------------------------------------------------\n * Core<PERSON> Utils (__COREUI_VERSION__): pickByKeys.ts\n * Licensed under MIT (https://github.com/coreui/coreui-utils/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst pickByKeys = (originalObject: object, keys: string | string[]) => {\n  const newObj = {}\n  for (let i = 0; i < keys.length; i++) {\n    newObj[keys[i]] = originalObject[keys[i]]\n  }\n  return newObj\n}\n\nexport default pickByKeys\n", "/**\n * --------------------------------------------------------------------------\n * Core<PERSON> Utils (__COREUI_VERSION__): rgbToHex.ts\n * Licensed under MIT (https://github.com/coreui/coreui-utils/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst rgbToHex = (color: string) => {\n  if (typeof color === 'undefined') {\n    throw new TypeError('Hex color is not defined')\n  }\n\n  if (color === 'transparent') {\n    return '#00000000'\n  }\n\n  const rgb = color.match(/^rgba?[\\s+]?\\([\\s+]?(\\d+)[\\s+]?,[\\s+]?(\\d+)[\\s+]?,[\\s+]?(\\d+)[\\s+]?/i)\n\n  if (!rgb) {\n    throw new Error(`${color} is not a valid rgb color`)\n  }\n\n  const r = `0${parseInt(rgb[1], 10).toString(16)}`\n  const g = `0${parseInt(rgb[2], 10).toString(16)}`\n  const b = `0${parseInt(rgb[3], 10).toString(16)}`\n\n  return `#${r.slice(-2)}${g.slice(-2)}${b.slice(-2)}`\n}\n\nexport default rgbToHex\n"], "mappings": ";;;AAOA,IAAM,mBAAmB,SAAC,QAAgB,QAAc;AAEtD,WAAqC,KAAA,GAAnB,KAAA,OAAO,KAAK,MAAM,GAAlB,KAAmB,GAAA,QAAnB,MAAqB;AAAlC,QAAM,MAAG,GAAA,EAAA;AACZ,QAAI,OAAO,GAAG,aAAa,QAAQ;AACjC,aAAO,OAAO,OAAO,GAAG,GAAG,iBAAiB,OAAO,GAAG,GAAG,OAAO,GAAG,CAAC,CAAC;IACtE;EACF;AAGD,SAAO,OAAO,UAAU,CAAA,GAAI,MAAM;AAClC,SAAO;AACT;;;ACXA,IAAM,WAAW,SAAC,UAAkB,SAAiB;AACnD,MAAI,OAAO,WAAW,aAAa;AACjC;EACD;AAED,MAAI,OAAO,aAAa,aAAa;AACnC;EACD;AAED,MAAM,WAAW,YAAA,QAAA,YAAA,SAAA,UAAW,SAAS;AAErC,SAAO,OAAO,iBAAiB,UAAU,IAAI,EAAE,iBAAiB,QAAQ,EAAE,QAAQ,OAAO,EAAE;AAC7F;;;ACVA,IAAM,WAAW,SAAC,aAAqB,SAAuB;AAAvB,MAAA,YAAA,QAAA;AAAA,cAAU,SAAS;EAAI;AAC5D,MAAM,WAAW,KAAK,OAAA,WAAW;AACjC,MAAM,QAAQ,SAAS,UAAU,OAAO;AACxC,SAAO,QAAQ,QAAQ;AACzB;;;ACLM,IAAA,WAAW,SAAC,OAAa;AAC7B,MAAI,OAAO,UAAU,aAAa;AAChC,UAAM,IAAI,UAAU,0BAA0B;EAC/C;AAEW,QAAM,MAAM,0BAA0B;AAMlD,MAAI;AACJ,MAAI;AACJ,MAAI;AAEJ,MAAI,MAAM,WAAW,GAAG;AACtB,QAAI,SAAS,MAAM,MAAM,GAAG,CAAC,GAAG,EAAE;AAClC,QAAI,SAAS,MAAM,MAAM,GAAG,CAAC,GAAG,EAAE;AAClC,QAAI,SAAS,MAAM,MAAM,GAAG,CAAC,GAAG,EAAE;EACnC,OAAM;AACL,QAAI,SAAS,MAAM,MAAM,GAAG,CAAC,GAAG,EAAE;AAClC,QAAI,SAAS,MAAM,MAAM,GAAG,CAAC,GAAG,EAAE;AAClC,QAAI,SAAS,MAAM,MAAM,GAAG,CAAC,GAAG,EAAE;EACnC;AAED,SAAO,QAAA,OAAQ,GAAC,IAAA,EAAA,OAAK,GAAM,IAAA,EAAA,OAAA,GAAC,GAAA;AAC9B;;;AC1BA,IAAM,YAAY,SAAC,OAAe,SAAa;AAAb,MAAA,YAAA,QAAA;AAAA,cAAa;EAAA;AAC7C,MAAI,OAAO,UAAU,aAAa;AAChC,UAAM,IAAI,UAAU,0BAA0B;EAC/C;AAED,MAAM,MAAM,MAAM,MAAM,0BAA0B;AAElD,MAAI,CAAC,KAAK;AACR,UAAM,IAAI,MAAM,GAAA,OAAG,OAAK,2BAAA,CAA2B;EACpD;AAED,MAAI;AACJ,MAAI;AACJ,MAAI;AAEJ,MAAI,MAAM,WAAW,GAAG;AACtB,QAAI,SAAS,MAAM,MAAM,GAAG,CAAC,GAAG,EAAE;AAClC,QAAI,SAAS,MAAM,MAAM,GAAG,CAAC,GAAG,EAAE;AAClC,QAAI,SAAS,MAAM,MAAM,GAAG,CAAC,GAAG,EAAE;EACnC,OAAM;AACL,QAAI,SAAS,MAAM,MAAM,GAAG,CAAC,GAAG,EAAE;AAClC,QAAI,SAAS,MAAM,MAAM,GAAG,CAAC,GAAG,EAAE;AAClC,QAAI,SAAS,MAAM,MAAM,GAAG,CAAC,GAAG,EAAE;EACnC;AAED,SAAO,QAAA,OAAQ,GAAC,IAAA,EAAA,OAAK,GAAC,IAAA,EAAA,OAAK,GAAC,IAAA,EAAA,OAAK,UAAU,KAAG,GAAA;AAChD;;;AC1BA,IAAM,UAAU,WAAA;AACd,MAAM,MAAM,KAAK,OAAM,EAAG,SAAS,EAAE,EAAE,OAAO,CAAC;AAC/C,SAAO,SAAS;AAClB;;;ACJA,IAAM,aAAa,SAAC,gBAAwB,MAAuB;AACjE,MAAM,SAAS,CAAA;AACf,MAAM,UAAU,OAAO,KAAK,cAAc;AAC1C,WAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,KAAC,KAAK,SAAS,QAAQ,CAAC,CAAC,MAAM,OAAO,QAAQ,CAAC,CAAC,IAAI,eAAe,QAAQ,CAAC,CAAC;EAC9E;AACD,SAAO;AACT;;;ACPA,IAAM,aAAa,SAAC,gBAAwB,MAAuB;AACjE,MAAM,SAAS,CAAA;AACf,WAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,WAAO,KAAK,CAAC,CAAC,IAAI,eAAe,KAAK,CAAC,CAAC;EACzC;AACD,SAAO;AACT;;;ACNM,IAAA,WAAW,SAAC,OAAa;AAC7B,MAAI,OAAO,UAAU,aAAa;AAChC,UAAM,IAAI,UAAU,0BAA0B;EAC/C;AAED,MAAI,UAAU,eAAe;AAC3B,WAAO;EACR;AAED,MAAM,MAAM,MAAM,MAAM,sEAAsE;AAE9F,MAAI,CAAC,KAAK;AACR,UAAM,IAAI,MAAM,GAAA,OAAG,OAAK,2BAAA,CAA2B;EACpD;AAED,MAAM,IAAI,IAAA,OAAI,SAAS,IAAI,CAAC,GAAG,EAAE,EAAE,SAAS,EAAE,CAAC;AAC/C,MAAM,IAAI,IAAA,OAAI,SAAS,IAAI,CAAC,GAAG,EAAE,EAAE,SAAS,EAAE,CAAC;AAC/C,MAAM,IAAI,IAAA,OAAI,SAAS,IAAI,CAAC,GAAG,EAAE,EAAE,SAAS,EAAE,CAAC;AAE/C,SAAO,IAAA,OAAI,EAAE,MAAM,EAAE,CAAC,EAAG,OAAA,EAAE,MAAM,EAAE,CAAC,EAAA,OAAG,EAAE,MAAM,EAAE,CAAC;AACpD;", "names": []}