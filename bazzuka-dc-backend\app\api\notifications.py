from flask import Blueprint, jsonify, request, abort
from app.utils.supabase.queries import get_defaulter_by_id, get_customer_requested_followups, get_pending_payment_approvals, get_issue_with_defaulter_by_issue_id
from app.utils.supabase.client import supabase
import uuid

notifications_bp = Blueprint('notifications', __name__)

@notifications_bp.route('', methods=['GET'])
def get_notifications():
    notif_resp = supabase.table('notifications').select('*').order('created_at', desc=True).limit(100).execute()
    # get the defaulter info
    notifications = []
    for n in notif_resp.data or []:
        defaulter = get_defaulter_by_id(n.get("defaulter_id")).data[0]
        notifications.append({
            "id": n.get("id"),
            "message": n.get("text"),
            "date": n.get("created_at", ""),
            "defaulter_id": n.get("defaulter_id"),
            "defaulter_name": defaulter.get("name"),
            "is_done": n.get("is_done", False),
            "is_read": n.get("is_read", False),
            "is_canceled": n.get("is_canceled", False),
        })
    return jsonify({"notifications": notifications, "count": len(notifications)})


@notifications_bp.route('/<id>', methods=['PATCH'])
def update_notification(id):
    data = request.get_json()
    allowed_fields = {'is_done', 'is_read', 'is_canceled'}
    update_data = {k: v for k, v in data.items() if k in allowed_fields}
    if not update_data:
        return jsonify({'error': 'No valid fields to update'}), 400
    try:
        result = supabase.table('notifications').update(update_data).eq('id', id).execute()
    except Exception as e:
        return jsonify({'error': str(e)}), 500
    if not getattr(result, 'data', None):
        return jsonify({'error': 'Notification not found'}), 404
    return jsonify({'notification': result.data[0]})