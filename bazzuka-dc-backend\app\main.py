import os
import logging

from dotenv import load_dotenv
from flask import Flask, request
from flask_cors import CORS

# NOTE: We are overriding the environment variables for development purposes
# Need to reconsider this in the future for deployments
load_dotenv(override=True)

from .api import api as api_blueprint

app = Flask(__name__)
CORS(
    app,
    supports_credentials=True,
    resources={r"/v0/*": {"origins": "*"}},
)
# CORS(app)

# from app.utils.supabase.client import supabase as s

# Configure logging
log_level = os.getenv("LOG_LEVEL", "INFO").upper()
logging.basicConfig(
    level=getattr(logging, log_level),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),  # Console handler
    ]
)
# Logging configured

# Register Blueprints
app.register_blueprint(api_blueprint, url_prefix="/v0")


# Define routes
@app.get("/")
def index():
    return {"hi": "Hello, World!"}


# List all registered endpoints
def list_endpoints():
    output = []
    for rule in app.url_map.iter_rules():
        if rule.endpoint != "static":
            methods = ",".join(rule.methods)
            output.append((rule.rule, methods))
    return output


# Print the list of registered endpoints to the terminal
endpoints = list_endpoints()
for endpoint in endpoints:
    print(f"Endpoint: {endpoint[0]}, Methods: {endpoint[1]}")

if __name__ == "__main__":
    app.run(port=5000, host="0.0.0.0")
