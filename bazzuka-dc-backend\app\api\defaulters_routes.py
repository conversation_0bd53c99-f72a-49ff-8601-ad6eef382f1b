from flask import Blueprint, jsonify, request
from app.utils.supabase.queries import get_defaulters_basic_info

defaulters_bp = Blueprint("defaulters", __name__)

@defaulters_bp.route("/defaulters", methods=["GET", "OPTIONS"])
def get_all_defaulters():
    if request.method == "OPTIONS":
        return jsonify({"message": "ok"}), 200

    try:
        # Get all defaulters from the database
        # For now, we'll get a reasonable limit of defaulters
        # In a production environment, you might want to add pagination
        
        result = get_defaulters_basic_info(100)
        
        if not result.data:
            return jsonify({"defaulters": []}), 200
        
        return jsonify({"defaulters": result.data}), 200
        
    except Exception as e:
        # print(f"Error fetching defaulters: {e}")
        return jsonify({"error": "Failed to fetch defaulters"}), 500 