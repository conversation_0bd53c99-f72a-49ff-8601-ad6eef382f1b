import React, { useEffect, useRef, useState } from 'react'
import PropTypes from 'prop-types'

import {
  CRow,
  CCol,
  <PERSON>ropdown,
  CDropdownMenu,
  CDropdownItem,
  CDropdownToggle,
  CWidgetStatsA,
} from '@coreui/react'
import { getStyle } from '@coreui/utils'
import { CChartBar, CChartLine } from '@coreui/react-chartjs'
import CIcon from '@coreui/icons-react'
import { cilArrowBottom, cilArrowTop, cilOptions } from '@coreui/icons'
import { cilCalendar } from '@coreui/icons'
import { CModal, CModalHeader, CModalTitle, CModalBody, CModalFooter, CButton, CFormInput } from '@coreui/react'
import { getApiUrl } from '../../utils/apiConfig'

const customModalStyles = `
  .date-range-modal .modal-content {
    background-color: #2c2c34;
    color: white;
  }

  .date-range-modal .modal-header {
    border-bottom: 1px solid #404040;
  }

  .date-range-modal .modal-footer {
    border-top: 1px solid #404040;
  }

  .date-range-modal input[type="date"] {
    background-color: #2c2c34;
    color: white;
    border: 1px solid #404040;
    border-radius: 4px;
    padding: 8px;
    margin: 5px;
    width: 200px;
  }

  .date-range-modal input[type="date"]::-webkit-calendar-picker-indicator {
    filter: invert(1);
  }
`

const loadingStyles = `
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
  .loading-spinner {
    display: inline-block;
    width: 1em;
    height: 1em;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: white;
    animation: spin 1s linear infinite;
  }
`

const WidgetsDropdown = (props) => {
  const widgetChartRef1 = useRef(null)
  const widgetChartRef2 = useRef(null)
  const [stats, setStats] = useState({
    calls: 0,
    emails: 0,
    payments: 0,
    call_duration: 0,
    inbound_emails: 0,
    outbound_emails: 0,
    promise_to_pay: 0
  })
  const [loading, setLoading] = useState(true)
  const [showDateModal, setShowDateModal] = useState(false)
  const [startDate, setStartDate] = useState('')
  const [endDate, setEndDate] = useState('')

  useEffect(() => {
    const style = document.createElement('style')
    style.textContent = loadingStyles + customModalStyles
    document.head.appendChild(style)
    return () => {
      document.head.removeChild(style)
    }
  }, [])

  const fetchStats = async () => {
    setLoading(true)
    try {
      // Mock data for the stats
      const mockData = {
        calls: 15,
        call_duration: 520,
        texts: 42,
        text_duration: 67,
        emails: 28,
        email_duration: 45,
        inbound_calls: 8,
        outbound_calls: 7,
        inbound_texts: 23,
        outbound_texts: 19,
        inbound_emails: 21,
        outbound_emails: 7,
        promise_to_pay: 2
      }

      // Use relative URL to connect to the local backend
      const params = new URLSearchParams()
      if (startDate && endDate) {
        params.append('start_date', startDate)
        params.append('end_date', endDate)
      }
      const url = getApiUrl(`/v0/stats${params.toString() ? `?${params.toString()}` : ''}`)

      console.log('Fetching stats from:', url)
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'ngrok-skip-browser-warning': 'true',
          'Access-Control-Allow-Origin': '*',
        },
      })
      .catch(err => {
        console.error('Error fetching stats:', err)
        throw err
      })
      const data = await response.json()
      console.log('API response:', data)

      // If we got data from the API, use it
      if (data && data.data) {
        setStats(data.data)
      } else {
        // Otherwise use mock data
        console.log('Using mock data because API returned empty response')
        setStats(mockData)
      }
    } catch (apiError) {
      console.error('API error, using mock data:', apiError)
      setStats(mockData)
    }

    setLoading(false)
  }

  useEffect(() => {
    fetchStats()
  }, [])

  useEffect(() => {
    document.documentElement.addEventListener('ColorSchemeChange', () => {
      if (widgetChartRef1.current) {
        setTimeout(() => {
          widgetChartRef1.current.data.datasets[0].pointBackgroundColor = getStyle('--cui-primary')
          widgetChartRef1.current.update()
        })
      }

      if (widgetChartRef2.current) {
        setTimeout(() => {
          widgetChartRef2.current.data.datasets[0].pointBackgroundColor = getStyle('--cui-info')
          widgetChartRef2.current.update()
        })
      }
    })
  }, [widgetChartRef1, widgetChartRef2])

  // Format call duration in hours and minutes
  const formatCallDuration = (minutes) => {
    if (!minutes) return '0h 0m'
    const hours = Math.floor(minutes / 60)
    const remainingMinutes = Math.floor(minutes % 60)
    return `${hours}h ${remainingMinutes}m`
  }

  console.log('Current stats:', stats)

  return (
    <>
      <div className="d-flex justify-content-end mb-3">
        <CButton color="primary" onClick={() => setShowDateModal(true)}>
          <CIcon icon={cilCalendar} className="me-2" />
          {startDate && endDate ? `${startDate} - ${endDate}` : 'Select Date Range'}
        </CButton>
      </div>
      <CModal
        visible={showDateModal}
        onClose={() => setShowDateModal(false)}
        className="date-range-modal"
      >
        <CModalHeader>
          <CModalTitle>Select Date Range</CModalTitle>
        </CModalHeader>
        <CModalBody className="d-flex flex-column align-items-center p-4">
          <div className="mb-3">
            <label className="me-2">Start Date:</label>
            <input
              type="date"
              value={startDate}
              onChange={(e) => setStartDate(e.target.value)}
              max={new Date().toISOString().split('T')[0]}
            />
          </div>
          <div>
            <label className="me-2">End Date:</label>
            <input
              type="date"
              value={endDate}
              onChange={(e) => setEndDate(e.target.value)}
              max={new Date().toISOString().split('T')[0]}
              min={startDate}
            />
          </div>
        </CModalBody>
        <CModalFooter>
          <CButton color="secondary" onClick={() => setShowDateModal(false)}>
            Cancel
          </CButton>
          <CButton color="primary" onClick={() => {
            setShowDateModal(false)
            fetchStats()
          }}>
            Apply
          </CButton>
          <CButton color="info" onClick={() => {
            setStartDate('')
            setEndDate('')
            setShowDateModal(false)
            fetchStats()
          }}>
            Show All Time
          </CButton>
        </CModalFooter>
      </CModal>
      <CRow className={props.className} xs={{ gutter: 4 }}>
        <CCol sm={6} xl={4} xxl={3}>
          <CWidgetStatsA
            color="primary"
            value={
              <>
                {loading ? <span className="loading-spinner"></span> : stats.calls}{' '}
                <span className="fs-6 fw-normal">
                  ({loading ? '...' : formatCallDuration(stats.call_duration)})
                </span>
              </>
            }
            title="Total Calls"
            action={
              <CDropdown alignment="end">
                <CDropdownToggle color="transparent" caret={false} className="text-white p-0">
                  <CIcon icon={cilOptions} />
                </CDropdownToggle>
                <CDropdownMenu>
                  <CDropdownItem>View Details</CDropdownItem>
                  <CDropdownItem>Export Data</CDropdownItem>
                </CDropdownMenu>
              </CDropdown>
            }
            chart={
              <CChartLine
                ref={widgetChartRef1}
                className="mt-3 mx-3"
                style={{ height: '70px' }}
                data={{
                  labels: ['January', 'February', 'March', 'April', 'May', 'June', 'July'],
                  datasets: [
                    {
                      label: 'My First dataset',
                      backgroundColor: 'transparent',
                      borderColor: 'rgba(255,255,255,.55)',
                      pointBackgroundColor: getStyle('--cui-primary'),
                      data: [75, 59, 84, 84, 51, 55, 40],
                    },
                  ],
                }}
                options={{
                  plugins: {
                    legend: {
                      display: false,
                    },
                  },
                  maintainAspectRatio: false,
                  scales: {
                    x: {
                      border: {
                        display: false,
                      },
                      grid: {
                        display: false,
                        drawBorder: false,
                      },
                      ticks: {
                        display: false,
                      },
                    },
                    y: {
                      min: 30,
                      max: 89,
                      display: false,
                      grid: {
                        display: false,
                      },
                      ticks: {
                        display: false,
                      },
                    },
                  },
                  elements: {
                    line: {
                      borderWidth: 1,
                      tension: 0.4,
                    },
                    point: {
                      radius: 4,
                      hitRadius: 10,
                      hoverRadius: 4,
                    },
                  },
                }}
              />
            }
          />
        </CCol>
        <CCol sm={6} xl={4} xxl={3}>
          <CWidgetStatsA
            color="info"
            value={
              <>
                {loading ? <span className="loading-spinner"></span> : stats.emails}{' '}
                <span className="fs-6 fw-normal">
                  (In: {loading ? '...' : stats.inbound_emails} | Out: {loading ? '...' : stats.outbound_emails})
                </span>
              </>
            }
            title="Total Emails"
            action={
              <CDropdown alignment="end">
                <CDropdownToggle color="transparent" caret={false} className="text-white p-0">
                  <CIcon icon={cilOptions} />
                </CDropdownToggle>
                <CDropdownMenu>
                  <CDropdownItem>View Details</CDropdownItem>
                  <CDropdownItem>Export Data</CDropdownItem>
                </CDropdownMenu>
              </CDropdown>
            }
            chart={
              <CChartLine
                ref={widgetChartRef2}
                className="mt-3 mx-3"
                style={{ height: '70px' }}
                data={{
                  labels: ['January', 'February', 'March', 'April', 'May', 'June', 'July'],
                  datasets: [
                    {
                      label: 'My First dataset',
                      backgroundColor: 'transparent',
                      borderColor: 'rgba(255,255,255,.55)',
                      pointBackgroundColor: getStyle('--cui-info'),
                      data: [1, 18, 9, 17, 34, 22, 11],
                    },
                  ],
                }}
                options={{
                  plugins: {
                    legend: {
                      display: false,
                    },
                  },
                  maintainAspectRatio: false,
                  scales: {
                    x: {
                      border: {
                        display: false,
                      },
                      grid: {
                        display: false,
                        drawBorder: false,
                      },
                      ticks: {
                        display: false,
                      },
                    },
                    y: {
                      min: -9,
                      max: 39,
                      display: false,
                      grid: {
                        display: false,
                      },
                      ticks: {
                        display: false,
                      },
                    },
                  },
                  elements: {
                    line: {
                      borderWidth: 1,
                    },
                    point: {
                      radius: 4,
                      hitRadius: 10,
                      hoverRadius: 4,
                    },
                  },
                }}
              />
            }
          />
        </CCol>
        <CCol sm={6} xl={4} xxl={3}>
          <CWidgetStatsA
            color="warning"
            value={
              <>
                {loading ? <span className="loading-spinner"></span> : (stats.payments ? stats.payments.toLocaleString() : '0')}{' '}
                <span className="fs-6 fw-normal">
                  (Total Collected)
                </span>
              </>
            }
            title="Cash Collected"
            action={
              <CDropdown alignment="end">
                <CDropdownToggle color="transparent" caret={false} className="text-white p-0">
                  <CIcon icon={cilOptions} />
                </CDropdownToggle>
                <CDropdownMenu>
                  <CDropdownItem>View Details</CDropdownItem>
                  <CDropdownItem>Export Data</CDropdownItem>
                </CDropdownMenu>
              </CDropdown>
            }
            chart={
              <CChartLine
                className="mt-3"
                style={{ height: '70px' }}
                data={{
                  labels: ['January', 'February', 'March', 'April', 'May', 'June', 'July'],
                  datasets: [
                    {
                      label: 'My First dataset',
                      backgroundColor: 'rgba(255,255,255,.2)',
                      borderColor: 'rgba(255,255,255,.55)',
                      data: [78, 81, 80, 45, 34, 12, 40],
                      fill: true,
                    },
                  ],
                }}
                options={{
                  plugins: {
                    legend: {
                      display: false,
                    },
                  },
                  maintainAspectRatio: false,
                  scales: {
                    x: {
                      display: false,
                    },
                    y: {
                      display: false,
                    },
                  },
                  elements: {
                    line: {
                      borderWidth: 2,
                      tension: 0.4,
                    },
                    point: {
                      radius: 0,
                      hitRadius: 10,
                      hoverRadius: 4,
                    },
                  },
                }}
              />
            }
          />
        </CCol>
        <CCol sm={6} xl={4} xxl={3}>
          <CWidgetStatsA
            color="danger"
            value={
              <>
                {loading ? <span className="loading-spinner"></span> : stats.promise_to_pay}{' '}
                <span className="fs-6 fw-normal">
                  (In Progress)
                </span>
              </>
            }
            title="Promise to Pay"
            action={
              <CDropdown alignment="end">
                <CDropdownToggle color="transparent" caret={false} className="text-white p-0">
                  <CIcon icon={cilOptions} />
                </CDropdownToggle>
                <CDropdownMenu>
                  <CDropdownItem>View Details</CDropdownItem>
                  <CDropdownItem>Export Data</CDropdownItem>
                </CDropdownMenu>
              </CDropdown>
            }
            chart={
              <CChartBar
                className="mt-3 mx-3"
                style={{ height: '70px' }}
                data={{
                  labels: [
                    'January',
                    'February',
                    'March',
                    'April',
                    'May',
                    'June',
                    'July',
                    'August',
                    'September',
                    'October',
                    'November',
                    'December',
                    'January',
                    'February',
                    'March',
                    'April',
                  ],
                  datasets: [
                    {
                      label: 'My First dataset',
                      backgroundColor: 'rgba(255,255,255,.2)',
                      borderColor: 'rgba(255,255,255,.55)',
                      data: [78, 81, 80, 45, 34, 12, 40, 85, 65, 23, 12, 98, 34, 84, 67, 82],
                      barPercentage: 0.6,
                    },
                  ],
                }}
                options={{
                  maintainAspectRatio: false,
                  plugins: {
                    legend: {
                      display: false,
                    },
                  },
                  scales: {
                    x: {
                      grid: {
                        display: false,
                        drawTicks: false,
                      },
                      ticks: {
                        display: false,
                      },
                    },
                    y: {
                      border: {
                        display: false,
                      },
                      grid: {
                        display: false,
                        drawBorder: false,
                        drawTicks: false,
                      },
                      ticks: {
                        display: false,
                      },
                    },
                  },
                }}
              />
            }
          />
        </CCol>
      </CRow>
    </>
  )
}

WidgetsDropdown.propTypes = {
  className: PropTypes.string,
  withCharts: PropTypes.bool,
}

export default WidgetsDropdown