import React from 'react'
import CIcon from '@coreui/icons-react'
import { cilSpeedometer, cilEnvelopeClosed, cilAddressBook, cilFork, cilCheckCircle, cilMoney } from '@coreui/icons'
import { CNavGroup, CNavItem, CNavTitle } from '@coreui/react'

const _nav = [
  {
    component: CNavItem,
    name: 'Dashboard',
    to: '/dashboard',
    icon: <CIcon icon={cilSpeedometer} customClassName="nav-icon" />,
    /*badge: {
      color: 'info',
      text: 'NEW',
    },*/
  },
  // {
  //   component: CNavItem,
  //   name: 'Mailbox',
  //   to: '/mailbox',
  //   icon: <CIcon icon={cilEnvelopeClosed} customClassName="nav-icon" />,
  // },
  {
    component: CNavItem,
    name: 'Contacts',
    to: '/contacts',
    icon: <CIcon icon={cilAddressBook} customClassName="nav-icon" />,
  },
  {
    component: CNavItem,
    name: 'Approvals',
    to: '/approvals',
    icon: <CIcon icon={cilCheckCircle} customClassName="nav-icon" />,
  },
  {
    component: CNavItem,
    name: 'Transactions',
    to: '/transactions',
    icon: <CIcon icon={cilMoney} customClassName="nav-icon" />,
  },
  // {
  //   component: CNavItem,
  //   name: 'Workflows',
  //   to: '/workflows',
  //   icon: <CIcon icon={cilFork} customClassName="nav-icon" />,
  // },
]

export default _nav
