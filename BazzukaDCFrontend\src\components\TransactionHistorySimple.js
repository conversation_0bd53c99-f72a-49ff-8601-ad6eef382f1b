import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>ard<PERSON>ody,
  <PERSON>ard<PERSON>eader,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>utton
} from '@coreui/react';
import { getApiUrl } from '../utils/apiConfig'

const TransactionHistorySimple = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [apiStatus, setApiStatus] = useState('checking');

  const testAPI = async () => {
    setLoading(true);
    setError(null);
    
    try {
      console.log('Testing API endpoint...');
      
      const response = await fetch(getApiUrl('/v0/transactions?limit=5'), {
        headers: {
          'ngrok-skip-browser-warning': 'true',
          'Content-Type': 'application/json'
        }
      });

      console.log('API Response status:', response.status);
      
      if (!response.ok) {
        const errorText = await response.text();
        console.error('API Error:', errorText);
        setError(`API Error: ${response.status} - ${errorText}`);
        setApiStatus('error');
      } else {
        const data = await response.json();
        console.log('API Response data:', data);
        setApiStatus('success');
      }
    } catch (error) {
      console.error('Network error:', error);
      setError(`Network Error: ${error.message}`);
      setApiStatus('error');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    testAPI();
  }, []);

  return (
    <div>
      <CCard>
        <CCardHeader>
          <h4>Transaction History - Debug Mode</h4>
        </CCardHeader>
        <CCardBody>
          <div className="mb-3">
            <strong>API Status: </strong>
            {apiStatus === 'checking' && <span className="text-warning">Checking...</span>}
            {apiStatus === 'success' && <span className="text-success">✓ Connected</span>}
            {apiStatus === 'error' && <span className="text-danger">✗ Error</span>}
          </div>

          {loading && (
            <div className="text-center py-4">
              <CSpinner color="primary" />
              <p className="mt-2">Testing API connection...</p>
            </div>
          )}

          {error && (
            <CAlert color="danger">
              <h6>API Connection Error:</h6>
              <pre>{error}</pre>
            </CAlert>
          )}

          {apiStatus === 'success' && (
            <CAlert color="success">
              <h6>✓ API Connection Successful!</h6>
              <p>The transaction API is working. Check the browser console for detailed response data.</p>
            </CAlert>
          )}

          <div className="mt-3">
            <CButton color="primary" onClick={testAPI} disabled={loading}>
              {loading ? 'Testing...' : 'Test API Again'}
            </CButton>
          </div>

          <hr className="my-4" />

          <h6>Debug Information:</h6>
          <ul>
            <li><strong>Component:</strong> TransactionHistorySimple</li>
            <li><strong>API Endpoint:</strong> {getApiUrl('/v0/transactions')}</li>
            <li><strong>Expected Backend:</strong> Flask server on same domain</li>
            <li><strong>Current Time:</strong> {new Date().toLocaleString()}</li>
          </ul>

          <div className="mt-3">
            <small className="text-muted">
              Open browser console (F12) to see detailed API logs and responses.
            </small>
          </div>
        </CCardBody>
      </CCard>
    </div>
  );
};

export default TransactionHistorySimple;
