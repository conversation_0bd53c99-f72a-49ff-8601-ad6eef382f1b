import os
import json
import requests

from nylas import Client as NylasClient
from nylas.models.auth import URLForAuthenticationConfig, CodeExchangeRequest
import sendgrid
from sendgrid.helpers.mail import Mail, Email, To, Content
from .vapi import generate_call_forwarding_payload

SENDGRID_API_KEY = os.environ.get("SENDGRID_API_KEY", "")
VAPI_BASE_URL = os.environ.get("VAPI_BASE_URL", "https://api.vapi.ai")
VAPI_API_KEY = os.environ.get("VAPI_API_KEY", "")
NYLAS_API_KEY = os.environ.get("NYLAS_API_KEY", "")
NYLAS_CLIENT_ID = os.environ.get("NYLAS_CLIENT_ID", "")
NYLAS_API_URI = os.environ.get("NYLAS_API_URI", "https://api.us.nylas.com")
PHONE_NUMBER_ID = os.environ.get("VAPI_PHONE_NUMBER_ID")

sg = sendgrid.SendGridAPIClient(api_key=SENDGRID_API_KEY)
nylas = NylasClient(api_key=NYLAS_API_KEY, api_uri=NYLAS_API_URI)

from .issues import IssueRepository
from .defaulters import defaulters
from .ai import PhonePromptGenerator
from app.utils.supabase.queries import get_org, get_summaries_by_defaulter_id
from .localstorage import inputs_storage

issues = IssueRepository()
phone_prompt_generator = PhonePromptGenerator()


def get_assistant(data):
    """
    We really need a better place for this!!!!!
    """
    caller_number = data["recipientNumber"]
    defaulter = defaulters.get_defaulter_by_phone(caller_number)
    if defaulter is None:
        raise ValueError(f"Defaulter with phone number {caller_number} not found")
        
    defaulter_id = defaulter["id"]

    caller_name = defaulter["name"]
    first_name = caller_name.split(" ")[0]
    last_name = caller_name.split(" ")[1]
    
    org = get_org().data[0]
    org_metadata = org["metadata"]

    ai_args = {}
    # ai_args["case_info"] = defaulter  # Pass as dictionary
    ai_args["defaulter_id"] = defaulter_id
    # ai_args["strategy"] = defaulter["customizations"]["negotiation_strategy"]
    # ai_args["commlogs"] = issues.get_comm_history(defaulter_id)
    ai_args["direction"] = "outbound"
    ai_args["defaulter_name"] = caller_name
    ai_args["org_type"] = org_metadata.get("type")

    org_name = org.get("name")
    ai_args["org_name"] = org_name
    ai_args["promise_failure_policy"] = org_metadata.get("promise_failure_policy")
    ai_args["policy"] = org_metadata.get("settlement_plans")
    # inputs_storage.set(defaulter_id, ai_args, "ssn", defaulter["customizations"]["ssn"])
    # inputs_storage.add_password(defaulter_id, "dob", defaulter["customizations"]["dob"])

    if org_metadata.get("call_forwarding"):
        ai_args["call_forwarding"] = org_metadata["call_forwarding"]
    if org_metadata.get("verification_types"):
        ai_args["verification_types"] = org_metadata["verification_types"]

    messages = phone_prompt_generator.generate(ai_args)

    assistant_response = {
        "name": "Debby",
        # "firstMessage": f"Hey, can I speak with {caller_name}?",
        "model": {
            "provider": "custom-llm",
            "url": "https://api.openai.com/v1", # NOTE: THIS IS FOR CUSTOM-LLM ONLY
            "model": "ft:gpt-4.1-2025-04-14:bazzuka-ai:ftv1:BkvHJQdA",
            "metadataSendMode": "off", # NOTE: THIS IS FOR CUSTOM-LLM ONLY
            # "temperature": 0.4,
            "messages": messages,
            "tools": [
                {
                    "type": "function",
                    "async": False,
                    "function": {
                        "name": "get_info",
                        "description": "Get the information about the defaulter and case.",
                        "parameters": {
                            "type": "object",
                            "properties": {
                                "defaulter_id": {
                                    "type": "string",
                                    "description": "The defaulter_id from the system prompt.",
                                },
                                "verification_type": {
                                    "type": "string",
                                    "description": "The type of verification for organizations requiring verification. The only supported values are 'ssn' meaning last four digits of Social Security Number, and 'dob' meaning date of birth.",
                                },
                                "verification_value": {
                                    "type": "string",
                                    "description": "The verification value for organizations requiring verification. For 'ssn', this should be the defaulter's Social Security Number, for 'dob', it should be the user's date of birth parsed as 'MM/DD/YYYY'.",
                                },
                            },
                            "required": [
                                "defaulter_id",
                            ],
                        },
                    },
                }, 
                {
                    "type": "function",
                    "async": True,
                    "function": {
                        "name": "schedule_one_off_communication",
                        "description": "Schedule a one-off communication with the user. This should be used if the user requests a one-off communication, or if the user is not interested in a payment plan.",
                        "parameters": {
                            "type": "object",
                            "properties": {
                                "defaulter_id": {
                                    "type": "number",
                                    "description": "The user's defaulter_id.",
                                },
                                "channel": {
                                    "type": "string",
                                    "description": "The channel to communicate with the user. This can be 'email' or 'call'.",
                                },
                                "date": {
                                    "type": "string",
                                    "description": "The date of the communication in the format 'YYYY-MM-DD'.",
                                },
                                "time": {
                                    "type": "string",
                                    "description": "The time of the communication in the format 'HH:MM'.",
                                },
                                "reason": {
                                    "type": "string",
                                    "description": "The reason for the communication.",
                                },
                                "is_human_followup": {
                                    "type": "boolean",
                                    "description": "Whether the follow-up should be with a human (true) or AI (false). Defaults to false (AI)."
                                },
                            },
                            "required": [
                                "defaulter_id",
                                "channel",
                                "date",
                                "time",
                                "reason",
                                "is_human_followup"
                            ],
                        },
                    },
                },
                # {
                #     "type": "function",
                #     "async": True,
                #     "function": {
                #         "name": "get_upcoming_payments",
                #         "description": "Get the upcoming payments for a particular issue.",
                #         "parameters": {
                #             "type": "object",
                #             "properties": {
                #                 "from_date": {
                #                     "type": "string",
                #                     "description": "The start date of the range to get the upcoming payments for in the format 'YYYY-MM-DD' (default is the beginning of time).",
                #                 },
                #                 "to_date": {
                #                     "type": "string",
                #                     "description": "The end date of the range to get the upcoming payments for in the format 'YYYY-MM-DD' (default is one year from today).",
                #                 },
                #                 "limit": {
                #                     "type": "number",
                #                     "description": "The maximum number of upcoming payments to return.",
                #                 },
                #             },
                #             "required": [],
                #         },
                #     },
                # },
                #     {
                #     "type": "function",
                #     "async": True,
                #     "function": {
                #         "name": "delete_payment_plan_for_issue  ",
                #         "description": "Delete a payment plan for a particular issue, call this if the user wants to cancel a payment plan or if the user wants to change the payment plan.",
                #         "parameters": {
                #             "type": "object",
                #             "properties": {
                #                 "issue_id": {
                #                     "type": "number",
                #                     "description": "The issue_id of the corresponding account. Should be a number like 199.",
                #                 },
                #             },
                #             "required": ["issue_id"],
                #         },
                #     },
                # },
            ],
        },
        "voice": {
            "provider": "11labs",
            "voiceId": "paula",
            # "voiceId": "sarah",
        },
        "transcriber": {
            "provider": "deepgram",
            "model": "nova-3",
            "language": "en",
            "smartFormat": True,
            "keywords": [
                    f"{first_name}:4",
                    f"{last_name}:3",
                ] + [f"{word}:3" for word in org_name.split(" ")],
        },
        "serverUrl": os.getenv("ROOT_DOMAIN_PUBLIC") + "/v0/webhook/vapi",
    }

    if org_metadata.get("call_forwarding"):
        assistant_response["model"]["tools"].append(
            generate_call_forwarding_payload(org_metadata["call_forwarding"])
        )

    return assistant_response


class Messenger:
    """
    The messenger is strictly responsible for sending communications through different channels.
    It should not be responsible for any other logic.
    """

    def __init__(self):
        pass

    def _invoke_email_nylas(self, data):
        subject = data["subject"]
        text = data["text"]
        # reply_to = data.get("replyTo", "<EMAIL>")   # NOTE: Do we need a replyTo field?
        recipientName = data.get("recipientName", "New Customer")
        recipientAddress = data["recipientAddress"]

        # NOTE: temporarily hardcoded
        grant_id = "490ffcaa-0de8-4a0e-951b-c652b570e14d"

        # Construct the email body
        body = {
            "subject": subject,
            "body": f"""
            <html>
                <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #222;">
                    {text}
                </body
            </html>
            """,
            "to": [{"name": recipientName, "email": recipientAddress}],
        }

        if data.get("reply_to_message_id"):
            body["reply_to_message_id"] = data["reply_to_message_id"]

        message = nylas.messages.send(grant_id, request_body=body).data

        return message.id

    def _invoke_email_sg(self, data):
        recipientAddress = data.get("recipientAddress", [])
        assert recipientAddress

        from_email = Email("<EMAIL>")  # Change to your verified sender
        to_email = To(recipientAddress)  # Change to your recipient
        subject = data["subject"]
        content = Content("text/plain", data["text"])
        mail = Mail(from_email, to_email, subject, content)
        mail_json = mail.get()
        response = sg.client.mail.send.post(request_body=mail_json)

        if response.status_code >= 400:
            raise ValueError(f"Failed to send email: {response.body}")

        return response.headers.get("X-Message-Id")

    def _invoke_call_vapi(self, data):
        recipientNumber = data.get(
            "recipientNumber", []
        )  # TODO: Deprecate this method of getting recipientNumber
        if not recipientNumber:
            defaulter = defaulters.get_defaulter_by_id(data["defaulter_id"])
            if defaulter is None:
                raise ValueError(f"Defaulter with ID {data['defaulter_id']} not found")
            recipientNumber = defaulter["phone"]
        assert recipientNumber

        phoneNumberId = PHONE_NUMBER_ID
        # assistantId = "9f90e427-acaf-4d14-881c-bd5e1a3f1bbd"

        request_args = {
            "phoneNumberId": phoneNumberId,
            "assistant": get_assistant(data),
            "customer": {
                "number": recipientNumber,
            },
        }

        # VAPI call request

        response = requests.post(
            f"{VAPI_BASE_URL}/call",
            headers={
                "Content-Type": "application/json",
                "Authorization": f"Bearer {VAPI_API_KEY}",
            },
            json=request_args,
        )
        response.raise_for_status()
        return response.json().get("id")

    def send(self, channel, data):
        if channel == "email":
            try:
                return self._invoke_email_nylas(data)
            except Exception as e:
                # Temporary fallback to SendGrid
                return self._invoke_email_sg(data)
        elif channel == "call":
            return self._invoke_call_vapi(data)
        else:
            raise ValueError(f"Unknown channel: {channel}")
