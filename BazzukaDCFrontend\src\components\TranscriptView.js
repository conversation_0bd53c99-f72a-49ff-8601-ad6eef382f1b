import React from 'react';
import AudioPlayer from './AudioPlayer';

const formatCallTranscript = (transcript) => {
  if (!transcript) return '';
  // Split by newlines and wrap each line in a div for spacing
  return transcript.split('\n').map((line, i) => (
    `<div key=${i} style="margin-bottom: 8px">${line}</div>`
  )).join('');
};

const TranscriptView = ({ transcript, channel, summary, audioUrl }) => {
  return (
    <div>
      {channel === 'call' ? (
        <div>
          {summary && <p className="mb-3"><strong>Summary:</strong> {summary}</p>}
          {audioUrl && (
            <div className="mb-3">
              <AudioPlayer audioUrl={audioUrl} />
            </div>
          )}
          <div className="bg-light p-3 rounded">
            <div
              className="mb-0 transcript-content"
              style={{ 
                lineHeight: '1.5',
                fontFamily: 'Courier, monospace',
                whiteSpace: 'pre-wrap'
              }}
              dangerouslySetInnerHTML={{ __html: formatCallTranscript(transcript) }}
            />
          </div>
        </div>
      ) : (
        <div
          className="transcript-content"
          dangerouslySetInnerHTML={{ __html: transcript }}
        />
      )}
    </div>
  );
};

export default TranscriptView; 