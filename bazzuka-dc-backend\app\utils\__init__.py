from .supabase.client import *
import hashlib
import os


def hash_sensitive_data(value: str) -> str:
    """
    Hashes a sensitive string value using SHA-256 and returns the hex digest.
    """
    if not isinstance(value, str):
        value = str(value)
    return hashlib.sha256(value.encode("utf-8")).hexdigest()


def validate_internal_secret(request):
    """
    Validates the X-Internal-Secret header against the OUTBOUND_SECRET_TOKEN env variable.
    Returns True if valid, False otherwise.
    """
    expected_token = os.environ.get("OUTBOUND_SECRET_TOKEN")
    provided_token = request.headers.get("X-Internal-Secret")
    return provided_token is not None and provided_token == expected_token
