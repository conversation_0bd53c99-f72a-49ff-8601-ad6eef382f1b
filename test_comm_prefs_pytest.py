"""
Pytest-based test suite for communication preferences agent false positives.

This test suite validates that the agent does not call tools inappropriately
for conversations that don't contain explicit opt-out or restriction requests.

Run with: pytest test_comm_prefs_pytest.py -v
"""

import json
import openai
import pytest
from typing import Dict, Any

# Set up OpenAI client
client = openai.OpenAI(api_key="********************************************************")

# The current prompt from the agent
COMM_PREFS_PROMPT = """Analyze the conversation transcript between a debt collector and a debtor to identify any explicit requests related to communication preferences.

**CRITICAL: Only call tools for EXPLICIT, DIRECT requests. Do NOT infer, assume, or interpret implicit preferences. When in doubt, DO NOT call any tools.**

- If—and only if—the debtor **explicitly** asks to stop receiving messages entirely through a specific channel (e.g., call, email, text), call the `opt_out_of_communications` tool with the appropriate channel.
    - You must always respect and act on any clear opt-out request.
    - Example phrases include: "Please stop calling me," "Don't email me anymore," or "I no longer want to receive texts."
- If the debtor **explicitly** specifies limits on when they can be contacted (e.g., only during certain hours, days of the week, or not on holidays), call the `restrict_communications` tool with the relevant restrictions.
    - Example phrases include: "Only call me after 5pm," "Do not contact me on weekends," or "Never call me at work."
- If you are given existing restrictions, you should append new restrictions before passing them into the `restrict_communications` tool.
- If the debtor requests not to be contacted at work, assume working hours are Monday to Friday, 9 AM to 5 PM, by default unless otherwise specified.
- **Never infer or assume a preference if the debtor does not directly state it.** For example, simply reaching voicemail, not answering a call, or automated greetings **do NOT** constitute a request or preference.
- Only act if the debtor makes a clear and affirmative request related to communication channels or timing.

**Key principle: If the debtor does not use explicit opt-out language or specific restriction requests, DO NOT call any tools.**

## Output Format
There should be no output, only tool calls if necessary."""

# Tool definitions
TOOLS = [
    {
        "type": "function",
        "function": {
            "name": "opt_out_of_communications",
            "description": "Opt a defaulter out entirely of specific communication channels.",
            "parameters": {
                "type": "object",
                "properties": {
                    "defaulter_id": {"type": "string"},
                    "channels": {"type": "array", "items": {"type": "string"}}
                },
                "required": ["defaulter_id", "channels"]
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "restrict_communications",
            "description": "Restrict a defaulter's communication by day of the week, time of the day, and/or holidays.",
            "parameters": {
                "type": "object",
                "properties": {
                    "defaulter_id": {"type": "string"},
                    "description": {"type": "string"}
                },
                "required": ["defaulter_id", "description"]
            }
        }
    }
]


class TestCommPrefsAgent:
    """Test class for communication preferences agent false positive detection."""
    
    @pytest.fixture
    def defaulter_id(self):
        """Fixture providing a test defaulter ID."""
        return "test_defaulter_123"
    
    @pytest.fixture
    def mock_database_calls(self):
        """Fixture to mock database calls."""
        with patch('app.utils.supabase.queries.get_restrictions_by_defaulter_id') as mock_get_restrictions, \
             patch('app.core.payments.PaymentsTool.opt_out_of_communications') as mock_opt_out, \
             patch('app.core.payments.PaymentsTool.restrict_communication') as mock_restrict:
            
            # Setup mock returns
            mock_get_restrictions.return_value = Mock(data=[])
            mock_opt_out.return_value = "Success: Opted out of communications."
            mock_restrict.return_value = "Success: Communication restricted."
            
            yield {
                'get_restrictions': mock_get_restrictions,
                'opt_out': mock_opt_out,
                'restrict': mock_restrict
            }
    
    def test_normal_greeting_conversation(self, defaulter_id, mock_database_calls):
        """Test that normal greeting doesn't trigger tools."""
        conversation = (
            "Collector: Hi Debby, this is John from ABC Collections regarding your account. "
            "How are you today?\n"
            "Debby: Hi John, I'm doing okay. What can I help you with?"
        )
        
        analyze_communication_preferences(conversation, defaulter_id)
        
        # Assert no tools were called
        assert not mock_database_calls['opt_out'].called, "opt_out_of_communications should not be called"
        assert not mock_database_calls['restrict'].called, "restrict_communications should not be called"
    
    def test_voicemail_reached(self, defaulter_id, mock_database_calls):
        """Test that reaching voicemail doesn't trigger opt-out."""
        conversation = (
            "Collector: Hi Debby, this is John from ABC Collections. "
            "I'm calling about your account. Please give me a call back at 555-0123.\n"
            "[Voicemail system]: You have reached Debby's voicemail..."
        )
        
        analyze_communication_preferences(conversation, defaulter_id)
        
        assert not mock_database_calls['opt_out'].called
        assert not mock_database_calls['restrict'].called
    
    def test_debby_being_rude_but_not_opting_out(self, defaulter_id, mock_database_calls):
        """Test that rude behavior without opt-out doesn't trigger tools."""
        conversation = (
            "Collector: Hi Debby, I'm calling about your overdue account.\n"
            "Debby: I don't want to talk to you right now. You people are so annoying!\n"
            "Collector: I understand you're frustrated, but we need to discuss this.\n"
            "Debby: Whatever, just make it quick."
        )
        
        analyze_communication_preferences(conversation, defaulter_id)
        
        assert not mock_database_calls['opt_out'].called
        assert not mock_database_calls['restrict'].called
    
    def test_debby_acting_annoyed_about_call(self, defaulter_id, mock_database_calls):
        """Test that expressing annoyance doesn't trigger restrictions."""
        conversation = (
            "Collector: Hello Debby, this is Sarah from Collections.\n"
            "Debby: Ugh, not again. This is so frustrating. I'm having a terrible day and now this.\n"
            "Collector: I'm sorry to hear that. Can we talk about your account?\n"
            "Debby: Fine, but I'm not happy about it."
        )
        
        analyze_communication_preferences(conversation, defaulter_id)
        
        assert not mock_database_calls['opt_out'].called
        assert not mock_database_calls['restrict'].called
    
    def test_debby_doesnt_answer_initially(self, defaulter_id, mock_database_calls):
        """Test that not answering immediately doesn't trigger restrictions."""
        conversation = (
            "Collector: Hello? Debby?\n"
            "[No response for 10 seconds]\n"
            "Collector: Hello, is this Debby?\n"
            "Debby: Yes, sorry, I was distracted. What do you need?"
        )
        
        analyze_communication_preferences(conversation, defaulter_id)
        
        assert not mock_database_calls['opt_out'].called
        assert not mock_database_calls['restrict'].called
    
    def test_debby_mentions_being_busy_but_continues(self, defaulter_id, mock_database_calls):
        """Test that mentioning being busy but continuing conversation doesn't trigger tools."""
        conversation = (
            "Collector: Hi Debby, calling about your account.\n"
            "Debby: Oh, I'm really busy right now with work stuff, but I guess we can talk quickly.\n"
            "Collector: I appreciate that. Let's discuss your payment options.\n"
            "Debby: Okay, what are my options?"
        )
        
        analyze_communication_preferences(conversation, defaulter_id)
        
        assert not mock_database_calls['opt_out'].called
        assert not mock_database_calls['restrict'].called
    
    def test_automated_greeting_message(self, defaulter_id, mock_database_calls):
        """Test that automated messages don't trigger actions."""
        conversation = (
            "[Automated message]: Thank you for calling. Your call is important to us. "
            "Please hold while we connect you.\n"
            "Collector: Hello, is this Debby?\n"
            "Debby: Yes, this is Debby."
        )
        
        analyze_communication_preferences(conversation, defaulter_id)
        
        assert not mock_database_calls['opt_out'].called
        assert not mock_database_calls['restrict'].called
    
    def test_debby_asks_about_payment_no_scheduling(self, defaulter_id, mock_database_calls):
        """Test that discussing payment without scheduling doesn't trigger tools."""
        conversation = (
            "Collector: Hi Debby, we need to discuss your overdue balance.\n"
            "Debby: How much do I owe exactly?\n"
            "Collector: Your current balance is $1,250.\n"
            "Debby: That's a lot. I need to figure out how to handle this."
        )
        
        analyze_communication_preferences(conversation, defaulter_id)
        
        assert not mock_database_calls['opt_out'].called
        assert not mock_database_calls['restrict'].called
    
    def test_debby_mentions_work_no_restriction_request(self, defaulter_id, mock_database_calls):
        """Test that mentioning work without restriction request doesn't trigger tools."""
        conversation = (
            "Collector: Hello Debby, calling about your account.\n"
            "Debby: I'm at work right now but I can talk for a few minutes.\n"
            "Collector: Thank you. Let's discuss your payment plan.\n"
            "Debby: Okay, what do you suggest?"
        )
        
        analyze_communication_preferences(conversation, defaulter_id)
        
        assert not mock_database_calls['opt_out'].called
        assert not mock_database_calls['restrict'].called
    
    def test_general_complaint_about_debt_collection(self, defaulter_id, mock_database_calls):
        """Test that general complaints don't trigger opt-out."""
        conversation = (
            "Collector: Hi Debby, this is about your outstanding balance.\n"
            "Debby: I hate dealing with debt collectors. This whole situation is stressful.\n"
            "Collector: I understand this is difficult.\n"
            "Debby: Yeah, it really is. But I know I need to deal with it."
        )
        
        analyze_communication_preferences(conversation, defaulter_id)
        
        assert not mock_database_calls['opt_out'].called
        assert not mock_database_calls['restrict'].called


class TestCommPrefsAgentTruePositives:
    """Test class to ensure true positives still work correctly."""
    
    @pytest.fixture
    def defaulter_id(self):
        return "test_defaulter_123"
    
    @pytest.fixture
    def mock_database_calls(self):
        with patch('app.utils.supabase.queries.get_restrictions_by_defaulter_id') as mock_get_restrictions, \
             patch('app.core.payments.PaymentsTool.opt_out_of_communications') as mock_opt_out, \
             patch('app.core.payments.PaymentsTool.restrict_communication') as mock_restrict:
            
            mock_get_restrictions.return_value = Mock(data=[])
            mock_opt_out.return_value = "Success: Opted out of communications."
            mock_restrict.return_value = "Success: Communication restricted."
            
            yield {
                'get_restrictions': mock_get_restrictions,
                'opt_out': mock_opt_out,
                'restrict': mock_restrict
            }
    
    def test_explicit_stop_calling_request(self, defaulter_id, mock_database_calls):
        """Test that explicit stop calling request triggers opt-out."""
        conversation = (
            "Collector: Hi Debby, calling about your account.\n"
            "Debby: Please stop calling me. I don't want any more phone calls."
        )
        
        analyze_communication_preferences(conversation, defaulter_id)
        
        # This SHOULD trigger the opt_out tool
        assert mock_database_calls['opt_out'].called, "opt_out_of_communications should be called for explicit request"
    
    def test_explicit_work_hours_restriction(self, defaulter_id, mock_database_calls):
        """Test that explicit work hours restriction triggers restrict tool."""
        conversation = (
            "Collector: Hi Debby, calling about your account.\n"
            "Debby: Don't call me at work. Only call me after 5pm please."
        )
        
        analyze_communication_preferences(conversation, defaulter_id)
        
        # This SHOULD trigger the restrict tool
        assert mock_database_calls['restrict'].called, "restrict_communications should be called for explicit restriction"


if __name__ == "__main__":
    # Run the tests
    pytest.main([__file__, "-v"])
