from datetime import datetime
import pytz
from typing import List
from app.ai.client import <PERSON><PERSON><PERSON>, Action, PromptGenerator
from app.ai.utils import wrap_message
from app.utils.openai.client import openai as openai_client
from app.core.ai.tools import no_tools_engine as tool_engine
from app.utils.supabase.queries import get_org
from .payments_tools import email_payment_tool_engine
from .tools import analyzer_tool_engine
from pydantic import BaseModel

class NegotiationPromptGenerator(PromptGenerator):
    def __init__(self):
        super().__init__()
        self.prompt = ""
        with open("data/prompts/payment_plan_gen.md", "r", encoding="utf-8") as file:
            self.prompt = file.read()
        self.inputs_prologue = ""

    def make_inputs(self, args):
        assert args.get("policy")
        assert args.get("case_info")
        # assert args.get("comm_logs")

        inputs = f"""
        - **Policy**: {args["policy"]}
        - **Case Info**: {args["case_info"]}
        - The status year is: 2024
        """

        if args.get("comm_logs"):
            inputs += f"""
            - **Communication Logs**: {args["comm_logs"]}
            """
        return inputs

    def generate(self, args) -> List[str]:
        messages = []
        messages.insert(0, wrap_message("user", self.prompt))
        messages.append(
            wrap_message("user", self.inputs_prologue + self.make_inputs(args))
        )
        return messages


class NegotiationPromptGeneratorAction(Action):
    def __init__(self):
        super().__init__()
        self.prompt_generator = NegotiationPromptGenerator()
        self.tool_engine = None


class SummarizePromptGenerator(PromptGenerator):
    def __init__(self):
        super().__init__()
        self.prompt = ""
        with open("data/prompts/summarizer.md", "r", encoding="utf-8") as file:
            self.prompt = file.read()
        self.inputs_prologue = "Create a summary given this most recent interaction: \n"

    def make_inputs(self, args):
        # assert args.get("previous_summaries")
        assert args.get("case_info")
        assert args.get("channel")
        assert args.get("timestamp")
        assert args.get("direction")
        # assert args.get("transcript")

        # inputs = ""
        if args.get("previous_summaries"):
            inputs = f"""
            Previous Summaries: \n{args["previous_summaries"]}\n
            """
        inputs = f"""
        Most recent conversation to summarize:
        - Case Info: {args["case_info"]}\n
        - Channel: {args["channel"]}\n
        - Timestamp: {args["timestamp"]}\n
        - Direction: {args["direction"]}\n
        - Transcript: {args["transcript"]}\n
        """
        return inputs


class SummarizeAction(Action):
    def __init__(self):
        super().__init__()
        self.prompt_generator = SummarizePromptGenerator()
        self.tool_engine = tool_engine


class ComposeEmailPromptGenerator(PromptGenerator):
    def __init__(self):
        super().__init__()
        self.prompt = ""
        with open("data/prompts/email_composer.md", "r", encoding="utf-8") as file:
            self.prompt = file.read()
        self.inputs_prologue = ""

    def make_inputs(self, args, timezone='America/Los_Angeles'):
        # Get current time in specified timezone (defaults to Pacific Time)
        tz = pytz.timezone(timezone)
        current_time = datetime.now(tz)
        
        org_name = "Your organization's name is " + str(args.get("org_name", ""))
        defaulter_id = "The defaulter's ID is " + str(args.get("defaulter_id", ""))
        defaulter_name = "The defaulter's name is " + str(args.get("defaulter_name", ""))
        verification_types_str = "The allowed verification types are: "

        # TODO: revisit later
        org = get_org().data[0]
        if org["metadata"]["require_verification_email"] == False:
            verification_types_str += "Verification is not required for this organization."
        elif args.get("verification_types") and len(args["verification_types"]) > 0:
            verification_types_str += "Verification is required and the acceptable verification types are:\n"
            for vtype in args["verification_types"]:
                verification_types_str += f"- {vtype['name']} ('{vtype['field']}')\n"
        else:
            verification_types_str += "There are no verification types required for this organization."
        
        current_datetime = "The current date is " + current_time.strftime("%Y-%m-%d") + " and the time is roughly " + current_time.strftime("%I:%M %p %Z")
        if args.get("org_type"):
            if args["org_type"] == "finance":
                org_type_info = "This organization is a finance company."
            else:
                org_type_info = "This organization is NOT a finance company."
        else:
            org_type_info = "This organization's type is not specified."

        # Inbound Message
        if args.get("inbound_message"):
            inbound_message = "The inbound message is: " + str(args.get("inbound_message", ""))
        else:
            inbound_message = "This is an unsolicited outbound email. This is not a reply to an inbound message."

        input_str = f"""{org_name}\n{defaulter_id}\n{defaulter_name}\n{verification_types_str}\n{current_datetime}\n{org_type_info}\n{inbound_message}"""

        return input_str

    def generate(self, args, timezone='America/Los_Angeles'):
        messages = []
        messages.insert(0, wrap_message("system", self.prompt))
        messages.append(
            wrap_message(
                "user",
                self.inputs_prologue + self.make_inputs(args, timezone),
            )
        )
        return messages


class ComposeEmailAction(Action):
    def __init__(self):
        super().__init__()
        self.prompt_generator = ComposeEmailPromptGenerator()
        self.tool_engine = email_payment_tool_engine


class AnalyzePromptGenerator(PromptGenerator):
    def __init__(self):
        super().__init__()
        self.prompt = ""
        with open("data/prompts/analyzer.md", "r", encoding="utf-8") as file:
            self.prompt = file.read()
        self.inputs_prologue = (
            "Analyze the following issue given this most recent conversation: \n"
        )

    def make_inputs(self, args):
        # assert args.get("current_datetime")
        assert args.get("defaulter_id")
        assert args.get("case_info")
        
        # If overdue_payment is present, use it instead of conversation summary
        if args.get("overdue_payment"):
            event_str = f"""
            Overdue Payment: {args["overdue_payment"]}
            """
        else:
            assert args.get("current_conversation_summary")
            assert args.get("current_conversation_direction")
            event_str = f"""
            Current Communication Direction: {args["current_conversation_direction"]}
            New Communication Summary: {args["current_conversation_summary"]}
            """
        assert args.get("restrictions")

        # Get organization's channel preferences
        org_metadata = get_org().data[0]["metadata"]
        available_channels = []
        if org_metadata.get("communication_channels", {}).get("email"):
            available_channels.append("email")
        if org_metadata.get("communication_channels", {}).get("calls"):
            available_channels.append("call")
        if org_metadata.get("communication_channels", {}).get("texts"):
            available_channels.append("text")

        # Get blocked channels from defaulter's customizations
        blocked_channels = []
        if isinstance(args.get("case_info"), dict) and args["case_info"].get("customizations"):
            blocked_channels = args["case_info"]["customizations"].get("blocked_channels", [])

        # Add organization type information
        org_type = org_metadata.get("type", "")
        org_type_info = "This is a finance company" if org_type == "finance" else "This is NOT a finance company"

        inputs = f"""
        defaulter_id: {args["defaulter_id"]}
        Case Info: {args["case_info"]}
        {event_str}
        """
        
        comm_history = args.get("previous_conversation_logs", [])
        inputs += "\n============= Begin Communication History ==============\n"
        if comm_history:
            for comm in comm_history:
                if comm["type"] == "call":
                    inputs += f"Call ({comm['direction']}) at {comm['timestamp']}:\nSummary: {comm['summary']}\n"
                elif comm["type"] == "email":
                    inputs += f"Email ({comm['direction']}) at {comm['timestamp']}:\nContent: {comm['content']}\n"
        else:
            inputs += "There has been no communication with this customer. This is the first contact.\n"
        inputs += "============= End of Communication History ==============\n"
            
        inputs += f"""
        Available Communication Channels for the Organization: {', '.join(available_channels)}
        Communication Channels Blocked by the Debtor: {', '.join(blocked_channels) if blocked_channels else 'None'}
        Debtor's Other Communications Restrictions: {args["restrictions"]}
        Organization Type: {org_type_info}
        """
        if args.get("holidays"):
            inputs += f"Upcoming Holidays: {args['holidays']}"

        if args.get("collections_strategy"):
            inputs += f"Collections strategy:"
            inputs += f"{args["collections_strategy"]}"

        call_strategy = org_metadata.get("calling_strategy", "")
        if call_strategy:
            inputs += f"The phone calling strategy is: {call_strategy}"

        email_strategy = org_metadata.get("email_strategy", "")
        if email_strategy:
            inputs += f"The email strategy is: {email_strategy}"
            
        text_strategy = org_metadata.get("text_strategy", "")
        if text_strategy:
            inputs += f"The text (SMS) strategy is: {text_strategy}"

        if args.get("error"):
            inputs += f"LINTER ERROR: There was an error in the previous analysis: {args['error']}"

        # Add overdue/failed payment instructions
        if args.get("overdue_payment"):
            inputs += "\nINSTRUCTION: The defaulter has missed a payment or has an overdue payment arrangement. Please generate an action item of type 'overdue-follow-up' and include the overdue payment details in the follow-up."

        return inputs


class ActionItem(BaseModel):
    action_date: str
    action_time: str
    action_channel: str
    action_channel_content: str
    action_reason: str
    payment_likelihood: int

    def to_dict(self):
        return {
            "action_date": self.action_date,
            "action_time": self.action_time,
            "action_channel": self.action_channel,
            "action_channel_content": self.action_channel_content,
            "action_reason": self.action_reason,
            "payment_likelihood": self.payment_likelihood,
        }


class AnalyzeAction(Action):
    def __init__(self):
        super().__init__()
        self.prompt_generator = AnalyzePromptGenerator()
        self.tool_engine = analyzer_tool_engine
        # self.structured_output_type = ActionItem


class PhonePromptGenerator(PromptGenerator):
    def __init__(self):
        super().__init__()
        self.prompt = ""
        with open("data/prompts/full_caller_prompt.md", "r", encoding="utf-8") as file:
            self.prompt = file.read()
        self.inputs_prologue = ""

    def make_inputs(self, args, timezone='America/New_York'):
        # Get current time in specified timezone (defaults to Pacific Time)
        tz = pytz.timezone(timezone)
        current_time = datetime.now(tz)
        
        inputs = f"The defaulter_id is {args['defaulter_id']}.\n"
        inputs += f"The defaulter's name is {args['defaulter_name']}.\n"
        # print(inputs)
        inputs += f"The organization's name is {args['org_name']}.\n"

        if args.get("org_type"):
            if args["org_type"] == "finance":
                inputs += "The organization is a finance company.\n"
            else:
                inputs += "The organization is NOT a finance company.\n"

        inputs += f"This is an {args['direction']} call.\n"

        if args.get("verification_types") and len(args["verification_types"]) > 0:
            inputs += "The verification types are:\n"
            for vtype in args["verification_types"]:
                inputs += f"- {vtype['name']} ('{vtype['field']}')\n"
        else:
            inputs += "No verification is required for this organization. Do not request verification from the defaulter. Immediately call the 'get_info' tool without verification parameters.\n"
        

        inputs += "\nThe current date is " + current_time.strftime("%Y-%m-%d") + "\n"
        inputs += "The time is roughly " + current_time.strftime("%I:%M %p %Z") + "\n"

        if args.get("call_forwarding"):
            inputs += "\n## CALL FORWARDING\n\n"
            for transfer in args["call_forwarding"]:
                inputs += f"- If {transfer['purpose']}, use the `transferCall` function with {transfer['number']} to transfer to {transfer['name']}.\n"

        if args.get("purpose"):
            inputs += "The purpose of this call is to " + args["purpose"] + ".\n"

        if args.get("policy"):
            inputs += "\n## NEGOTIATION POLICY\n\n" + args["policy"] + "\n"

        return inputs

    def generate(self, args, timezone='America/Los_Angeles'):
        messages = []
        messages.append(
            {
                "role": "system",
                "content": self.prompt + self.inputs_prologue + self.make_inputs(args, timezone),
            }
        )
        return messages


def make_ai_client():
    client = AIClient(openai_client)  # ... , model="gpt-4.5-preview"
    summarize_action = SummarizeAction()
    compose_email_action = ComposeEmailAction()
    analyze_action = AnalyzeAction()
    # negotiation_generator = NegotiationPromptGeneratorAction()

    client.register("summarize", summarize_action)
    client.register("compose_email", compose_email_action)
    client.register("analyze", analyze_action)
    # client.register("generate_negotiation_prompt", negotiation_generator)
    return client


def make_negotiation_gen_client():
    client = AIClient(openai_client)  # , model="o1-preview")
    negotiation_generator = NegotiationPromptGeneratorAction()
    client.register("generate_negotiation_prompt", negotiation_generator)
    return client
