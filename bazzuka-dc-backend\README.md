# Bazzuka Backend

This repository hosts the backend server for Bazzuka. The backend handles all the application, agentic, and orchestration logic.

## Prerequisites

Before you begin, ensure you have met the following requirements:
- Python 3.12.10
- Poetry for dependency management

## Installation

To install the necessary libraries and setup the project environment, follow these steps:

1. Clone the repository:
   ```bash
   git clone https://github.com/mwomick/bazzuka-dc-backend.git
2. Navigate to the project directory:
   ```bash
    cd bazzuka-dc-backend
3. Use pyenv to set the Python version 
    ```bash
    pyenv install 3.12.10
    pyenv local 3.12.10
4. Install dependencies using Poetry:
    ```bash
    poetry install

## Running the Project
To run the project, use Poetry to handle the environment:

  ```bash
  poetry run flask --app ./app/main --debug run 
  ```

This command starts the Flask server on http://127.0.0.1:5000/. You can access the server from your web browser at this address.

To receive webhooks in a dev environment, use [Microsoft Dev Tunnels](https://code.visualstudio.com/docs/remote/tunnels) in VS Code. Forward port 5000.

## Testing the Project

This is currently not supported but in the future:

To run the tests from the tests folder using Poetry, run:

  ```bash
  poetry pytest run tests
  ```
