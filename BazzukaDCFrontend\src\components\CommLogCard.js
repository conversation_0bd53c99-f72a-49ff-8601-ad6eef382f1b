import React, { useState } from 'react';
import {
  <PERSON>ard,
  CCardBody,
  CCardHeader,
  CFormInput,
  CButton,
  CProgress,
  CCol,
  CRow,
} from '@coreui/react';
import { cilSave } from '@coreui/icons';
import { CIcon } from '@coreui/icons-react';
import { getApiUrl } from '../utils/apiConfig'

const CommLogCard = ({ commLog, onUpdate }) => {
  const [feedback, setFeedback] = useState(commLog.feedback || '');
  const [isEditing, setIsEditing] = useState(false);

  const handleSave = async () => {
    try {
      const response = await fetch(
        getApiUrl(`/v0/communications`),
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'ngrok-skip-browser-warning': 'true',
            'Access-Control-Allow-Origin': '*',
          },
          body: JSON.stringify({
            id: commLog.id,
            feedback: feedback,
          }),
        }
      );

      if (response.ok) {
        setIsEditing(false);
        if (onUpdate) {
          onUpdate({ ...commLog, feedback });
        }
      }
    } catch (error) {
      console.error('Error updating feedback:', error);
    }
  };

  return (
    <CCard className="mb-4">
      <CCardHeader>
        <strong>Communication Details</strong>
      </CCardHeader>
      <CCardBody>
        <CRow className="mb-3">
          <CCol md={6}>
            <strong>Name:</strong> {commLog.name}
          </CCol>
          <CCol md={6}>
            <strong>Channel:</strong> {commLog.channel}
          </CCol>
        </CRow>
        <CRow className="mb-3">
          <CCol md={6}>
            <strong>Direction:</strong> {commLog.direction}
          </CCol>
          <CCol md={6}>
            <strong>Payment Likelihood:</strong>
            <div className="d-flex align-items-center gap-2">
              <div className="fw-semibold" style={{ minWidth: '45px' }}>
                {(commLog.payment_likelihood * 100) / 5}%
              </div>
              <div className="flex-grow-1">
                <CProgress
                  thin
                  color={
                    commLog.payment_likelihood <= 2
                      ? 'danger'
                      : commLog.payment_likelihood === 3
                      ? 'warning'
                      : 'success'
                  }
                  value={(commLog.payment_likelihood * 100) / 5}
                />
              </div>
            </div>
          </CCol>
        </CRow>
        <CRow className="mb-3">
          <CCol>
            <strong>Feedback:</strong>
            {isEditing ? (
              <div className="d-flex gap-2 mt-2">
                <CFormInput
                  value={feedback}
                  onChange={(e) => setFeedback(e.target.value)}
                  placeholder="Enter feedback..."
                />
                <CButton color="primary" onClick={handleSave}>
                  <CIcon icon={cilSave} className="me-2" />
                  Save
                </CButton>
              </div>
            ) : (
              <div className="mt-2">
                <p>{feedback || 'No feedback provided'}</p>
                <CButton color="primary" onClick={() => setIsEditing(true)}>
                  Edit Feedback
                </CButton>
              </div>
            )}
          </CCol>
        </CRow>
      </CCardBody>
    </CCard>
  );
};

export default CommLogCard;