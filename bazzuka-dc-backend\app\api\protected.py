from flask import Blueprint, jsonify, request, g

from ..utils.supabase.client import supabase as s

from .auth import auth_bp
from .outbound import outbound_bp
from .ping import ping_bp
from .stats import stats_bp
from .communications import communications_bp

protected_bp = Blueprint("protected", __name__)

protected_bp.register_blueprint(ping_bp)
protected_bp.register_blueprint(auth_bp, url_prefix="/auth")
# protected_bp.register_blueprint(stats_bp, url_prefix="/stats")


@protected_bp.before_request
def check_auth():
    if request.method == "OPTIONS":
        return "", 204

    # token = request.headers.get("Authorization")
    token = request.cookies.get("sb-token")

    # if token:
    #     token = token.split(" ")[1]

    if not token:
        return jsonify({"error": "unauthorized"}), 401

    try:
        user = s.auth.get_user(token).user
        g.user = user
    except:
        return jsonify({"error": "unauthorized"}), 401
