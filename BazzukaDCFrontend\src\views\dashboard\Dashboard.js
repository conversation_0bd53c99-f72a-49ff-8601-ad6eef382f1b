import React from 'react'
import classNames from 'classnames'
import { useEffect, useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { UserProvider, useUser } from '../../userContext'


import {
  CAvatar,
  CButton,
  CButtonGroup,
  CCard,
  CCardBody,
  CCardFooter,
  CCardHeader,
  CCol,
  CProgress,
  CRow,
  CTable,
  CTableBody,
  CTableDataCell,
  CTableHead,
  CTableHeaderCell,
  CTableRow,
} from '@coreui/react'
import CIcon from '@coreui/icons-react'
import {
  cibCcAmex,
  cibCcApplePay,
  cibCcMastercard,
  cibCcPaypal,
  cibCcStripe,
  cibCcVisa,
  cibGoogle,
  cibFacebook,
  cibLinkedin,
  cifBr,
  cifEs,
  cifFr,
  cifIn,
  cifPl,
  cifUs,
  cibTwitter,
  cilCloudDownload,
  cilPeople,
  cilUser,
  cilUserFemale,
  cilPlus,
} from '@coreui/icons'

import avatar1 from 'src/assets/images/avatars/1.jpg'
import avatar2 from 'src/assets/images/avatars/2.jpg'
import avatar3 from 'src/assets/images/avatars/3.jpg'
import avatar4 from 'src/assets/images/avatars/4.jpg'
import avatar5 from 'src/assets/images/avatars/5.jpg'
import avatar6 from 'src/assets/images/avatars/6.jpg'

import WidgetsBrand from '../widgets/WidgetsBrand'
import WidgetsDropdown from '../widgets/WidgetsDropdown'
import MainChart from './MainChart'
import Logs from '../../components/LogsTable'
import ActionItemsTable from '../../components/ActionItemsTable'
import LogsTable from '../../components/LogsTable'
import ManualConversationModal from '../../components/ManualConversationModal'

const Dashboard = () => {
  const navigate = useNavigate()
  const [showManualLogModal, setShowManualLogModal] = useState(false)
  const [refreshLogs, setRefreshLogs] = useState(0)

  const user = sessionStorage.getItem('access_token')

  useEffect(() => {
    if (!user) navigate('/login')
  }, [user, navigate])

  const handleManualLogSuccess = () => {
    // Trigger refresh of the logs table
    setRefreshLogs(prev => prev + 1)
  }

  return (
    <UserProvider>
      <WidgetsDropdown className="mb-4" />
      
      <CRow>
        <CCol xs>
          <CCard className="mb-1">
            <CCardHeader>Scheduled</CCardHeader>
            <CCardBody>

              <ActionItemsTable />
            
            </CCardBody>
          </CCard>

          <CCard className="mb-1">
            <CCardHeader className="d-flex justify-content-between align-items-center">
              <span>Completed</span>
              <CButton
                color="primary"
                size="sm"
                onClick={() => setShowManualLogModal(true)}
              >
                <CIcon icon={cilPlus} className="me-1" />
                Log Human Conversation
              </CButton>
            </CCardHeader>
            <CCardBody>

            <LogsTable key={refreshLogs} />

            </CCardBody>
          </CCard>

        </CCol>
      </CRow>

      <ManualConversationModal
        visible={showManualLogModal}
        onClose={() => setShowManualLogModal(false)}
        onSuccess={handleManualLogSuccess}
      />
    </UserProvider>
  )
}

export default Dashboard
