from flask import Blueprint, request, jsonify
from flask_cors import CORS
import random
import string
from app.utils.supabase.queries import get_defaulter_by_phone, get_chatlogs_by_defaulter_id
from app.utils.supabase.client import supabase
from datetime import datetime

simplechat_bp = Blueprint("simplechat_bp", __name__)
CORS(simplechat_bp)

# Generate a random message_id
def generate_message_id():
    return ''.join(random.choices(string.ascii_letters + string.digits, k=16))

# Insert a message into chatlogs
def insert_chatlog(defaulter_id, message, direction):
    return supabase.table('chatlogs').insert({
        'message_id': generate_message_id(),
        'timestamp': datetime.utcnow().isoformat() + 'Z',
        'defaulter_id': defaulter_id,
        'message': message,
        'direction': direction
    }).execute()

# Fetch messages for a defaulter_id
def get_chatlogs(defaulter_id):
    resp = get_chatlogs_by_defaulter_id(defaulter_id)
    return resp.data if hasattr(resp, 'data') else resp.get('data')

@simplechat_bp.route('/receive_message', methods=['POST'])
def receive_message():
    data = request.json
    defaulter_id = data.get('to')
    text = data.get('text')
    if not defaulter_id or not text:
        return jsonify({'error': 'to and text required'}), 400
    insert_chatlog(defaulter_id, text, 'outbound')
    # Insert a static inbound response
    insert_chatlog(defaulter_id, 'Welcome to BazzukaAI.', 'inbound')
    return jsonify({'status': 'received', 'response': 'This is a static response.'}), 200

@simplechat_bp.route('/send_message', methods=['POST'])
def send_message():
    data = request.json
    defaulter_id = data.get('to')
    text = data.get('text')
    if not defaulter_id or not text:
        return jsonify({'error': 'to and text required'}), 400
    insert_chatlog(defaulter_id, text, 'inbound')
    return jsonify({'status': 'sent'}), 200

@simplechat_bp.route('/messages', methods=['GET'])
def get_messages():
    defaulter_id = request.args.get('defaulter_id')
    if not defaulter_id:
        return jsonify({'error': 'defaulter_id required'}), 400
    logs = get_chatlogs(defaulter_id)
    return jsonify({'messages': logs}), 200 