import React, { useEffect, useState } from 'react'
import { supabase } from '../supabaseClient'
import { useNavigate } from 'react-router-dom'

// This component handles browser notifications for both payment plan approvals and human-requested follow-ups via Supabase realtime.

const SupabaseRealtimeListener = () => {
  const [notificationPermission, setNotificationPermission] = useState(Notification.permission)
  const [showPermissionButton, setShowPermissionButton] = useState(Notification.permission === 'default')
  const navigate = useNavigate()

  // Request notification permission
  const requestNotificationPermission = async () => {
    try {
      const permission = await Notification.requestPermission()
      setNotificationPermission(permission)
      setShowPermissionButton(permission !== 'granted')
      console.log('Notification permission:', permission)
    } catch (error) {
      console.error('Error requesting notification permission:', error)
    }
  }

  // Show a notification
  const showNotification = (title, body, data = {}) => {
    // Check if notifications are enabled in settings
    const notificationsEnabled = localStorage.getItem('notificationsEnabled') === 'true'

    if (!notificationsEnabled) {
      console.log('Notifications disabled in settings')
      return
    }

    if (notificationPermission !== 'granted') {
      console.log('Notification permission not granted')
      return
    }

    try {
      const notification = new Notification(title, {
        body,
        icon: '/BazzukaLogo.png', // Use your app's favicon or another appropriate icon
        data
      })

      notification.onclick = () => {
        // Focus on the window and navigate to the approvals page when clicked
        window.focus()
        console.log('Notification clicked, redirecting to:', data.url || '/approvals')

        // Always navigate to approvals page when notification is clicked
        // Use data.url if provided, otherwise default to '/approvals'
        navigate(data.url || '/approvals')

        notification.close()
      }
    } catch (error) {
      console.error('Error showing notification:', error)
    }
  }

  useEffect(() => {
    // Set up Supabase realtime subscription
    const setupRealtimeSubscription = async () => {
      try {
        console.log('Setting up realtime subscription for payment approvals and human follow-ups...')

        // Payment plan notifications (existing)
        const paymentChannel = supabase
          .channel('payment-links-changes')
          .on(
            'postgres_changes',
            {
              event: 'INSERT',
              schema: 'dev',
              table: 'payment_links',
              filter: '_approved=eq.pending',
            },
            (payload) => {
              console.log('New pending payment detected:', payload)
              const amountInDollars = (payload.new.amount / 100).toFixed(2)
              showNotification(
                'New Payment Approval Required',
                `A new payment of $${amountInDollars} is pending approval. Click to view and approve.`,
                {
                  url: '/approvals',
                  paymentId: payload.new.id,
                  amount: payload.new.amount
                }
              )
            }
          )
          .subscribe((status) => {
            console.log('Postgres Changes subscription status (payment_links):', status)
          })

        // Human-requested follow-up notifications (NEW)
        const followupChannel = supabase
          .channel('actionitems-human-followup')
          .on(
            'postgres_changes',
            {
              event: 'INSERT',
              schema: 'dev',
              table: 'actionitems',
              filter: 'is_human_followup=eq.true',
            },
            (payload) => {
              console.log('New human follow-up scheduled:', payload)
              const reason = payload.new.action_reason || 'No reason provided'
              const date = payload.new.action_date || ''
              showNotification(
                'Human Follow-up Scheduled',
                `A human follow-up has been scheduled for ${date}. Reason: ${reason}. Click to view.`,
                {
                  url: '/action-items',
                  actionItemId: payload.new.id
                }
              )
            }
          )
          .subscribe((status) => {
            console.log('Postgres Changes subscription status (actionitems):', status)
          })

        // Clean up function
        return () => {
          paymentChannel.unsubscribe()
          followupChannel.unsubscribe()
        }
      } catch (error) {
        console.error('Error setting up realtime subscription:', error)
      }
    }

    // Only set up the subscription if notification permission is granted and notifications are enabled
    const notificationsEnabled = localStorage.getItem('notificationsEnabled') === 'true'
    if (notificationPermission === 'granted' && notificationsEnabled) {
      setupRealtimeSubscription()
    }
  }, [notificationPermission, navigate])

  // No UI needed - notifications are now controlled from Settings page
  return null
}

export default SupabaseRealtimeListener
