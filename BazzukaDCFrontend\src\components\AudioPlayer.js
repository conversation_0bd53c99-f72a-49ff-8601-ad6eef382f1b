import React, { useState, useRef, useEffect } from 'react';
import { CButton } from '@coreui/react';

const AudioPlayer = ({ audioUrl }) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const audioRef = useRef(null);

  useEffect(() => {
    const audio = audioRef.current;
    if (!audio) return;

    const updateTime = () => setCurrentTime(audio.currentTime);
    const updateDuration = () => setDuration(audio.duration);
    const handleLoadStart = () => setIsLoading(true);
    const handleCanPlay = () => setIsLoading(false);
    const handleEnded = () => setIsPlaying(false);

    audio.addEventListener('timeupdate', updateTime);
    audio.addEventListener('loadedmetadata', updateDuration);
    audio.addEventListener('loadstart', handleLoadStart);
    audio.addEventListener('canplay', handleCanPlay);
    audio.addEventListener('ended', handleEnded);

    return () => {
      audio.removeEventListener('timeupdate', updateTime);
      audio.removeEventListener('loadedmetadata', updateDuration);
      audio.removeEventListener('loadstart', handleLoadStart);
      audio.removeEventListener('canplay', handleCanPlay);
      audio.removeEventListener('ended', handleEnded);
    };
  }, [audioUrl]);

  const togglePlayPause = () => {
    const audio = audioRef.current;
    if (!audio) return;

    if (isPlaying) {
      audio.pause();
    } else {
      audio.play();
    }
    setIsPlaying(!isPlaying);
  };

  const handleSeek = (e) => {
    const audio = audioRef.current;
    if (!audio) return;

    const rect = e.currentTarget.getBoundingClientRect();
    const clickX = e.clientX - rect.left;
    const newTime = (clickX / rect.width) * duration;
    audio.currentTime = newTime;
    setCurrentTime(newTime);
  };

  const formatTime = (time) => {
    if (isNaN(time)) return '0:00';
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  if (!audioUrl) {
    return null;
  }

  return (
    <div className="audio-player" style={{
      border: '1px solid #e0e0e0',
      borderRadius: '8px',
      padding: '12px',
      backgroundColor: '#f8f9fa',
      maxWidth: '400px'
    }}>
      <div className="mb-2">
        <small className="text-muted">🎧 Listen to call recording</small>
      </div>
      <audio ref={audioRef} src={audioUrl} preload="metadata" />

      <div className="d-flex align-items-center gap-3">
        <CButton
          color="primary"
          variant="outline"
          size="sm"
          onClick={togglePlayPause}
          disabled={isLoading}
          style={{ minWidth: '60px' }}
        >
          {isLoading ? '...' : isPlaying ? '⏸️' : '▶️'}
        </CButton>
        
        <div className="flex-grow-1">
          <div 
            className="progress-bar-container"
            style={{ 
              height: '6px', 
              backgroundColor: '#e0e0e0', 
              borderRadius: '3px', 
              cursor: 'pointer',
              position: 'relative'
            }}
            onClick={handleSeek}
          >
            <div 
              className="progress-bar"
              style={{ 
                height: '100%', 
                backgroundColor: '#0d6efd', 
                borderRadius: '3px',
                width: duration ? `${(currentTime / duration) * 100}%` : '0%',
                transition: 'width 0.1s ease'
              }}
            />
          </div>
          
          <div className="d-flex justify-content-between mt-1">
            <small className="text-muted">{formatTime(currentTime)}</small>
            <small className="text-muted">{formatTime(duration)}</small>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AudioPlayer;
