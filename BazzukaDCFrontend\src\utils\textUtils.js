/**
 * Utility functions for text formatting
 */

/**
 * Converts newline characters (\n) to HTML line breaks (<br>)
 * @param {string} text - The text to format
 * @returns {string} - The formatted text with HTML line breaks
 */
export const formatTextWithLineBreaks = (text) => {
  if (!text) return '';
  return text.replace(/\n/g, '<br>');
};

/**
 * Formats transcript text for display by converting newlines to HTML breaks
 * and handling any other special formatting needs
 * @param {string} transcript - The transcript text to format
 * @param {string} channel - The channel type (e.g., 'call', 'email')
 * @returns {string} - The formatted transcript
 */
export const formatTranscript = (transcript, channel) => {
  if (!transcript) return '';
  if (channel && channel.toLowerCase() === 'call') {
    // For calls, keep line breaks
    let formatted = transcript.replace(/\n{2,}/g, '\n');
    formatted = formatted.replace(/\n/g, '<br>');
    return formatted;
  } else {
    // For emails, remove all breaks
    let formatted = transcript.replace(/\n+/g, ' ');
    formatted = formatted.replace(/\s+/g, ' ').trim();
    return formatted;
  }
};
